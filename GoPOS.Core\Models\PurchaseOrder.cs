using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج أمر الشراء
    /// </summary>
    public class PurchaseOrder
    {
        public int Id { get; set; }

        [Required]
        public string OrderNumber { get; set; }

        [Required]
        public DateTime OrderDate { get; set; }

        public DateTime? ExpectedDeliveryDate { get; set; }

        public DateTime? DeliveryDate { get; set; }

        [Required]
        public int SupplierId { get; set; }
        public Supplier Supplier { get; set; }

        [Required]
        public PurchaseOrderStatus Status { get; set; }

        [Required]
        public decimal TotalAmount { get; set; }

        public decimal? Discount { get; set; }

        [Required]
        public decimal FinalAmount { get; set; }

        public decimal? Tax { get; set; }

        public decimal? Shipping { get; set; }

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; }

        public int UserId { get; set; }
        public User User { get; set; }

        [MaxLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public ICollection<PurchaseOrderItem> Items { get; set; }
        public ICollection<InventoryTransaction> InventoryTransactions { get; set; }
    }

    public enum PurchaseOrderStatus
    {
        Draft = 0,           // مسودة
        Pending = 1,         // قيد الانتظار
        Approved = 2,        // معتمد
        Ordered = 3,         // تم الطلب
        PartiallyReceived = 4, // استلام جزئي
        Received = 5,        // تم الاستلام
        Cancelled = 6        // ملغي
    }
}
