namespace GoPOS.Data.Models
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        /// <summary>
        /// معرف العميل
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// اسم العميل
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone { get; set; }
        
        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string Email { get; set; }
        
        /// <summary>
        /// العنوان
        /// </summary>
        public string Address { get; set; }
        
        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// هل العميل نشط
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// عدد الفواتير
        /// </summary>
        public int InvoiceCount { get; set; }
        
        /// <summary>
        /// إجمالي المشتريات
        /// </summary>
        public decimal TotalPurchases { get; set; }
    }
}
