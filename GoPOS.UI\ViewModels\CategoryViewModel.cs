using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GoPOS.UI.ViewModels
{
    /// <summary>
    /// نموذج عرض الفئة
    /// </summary>
    public class CategoryViewModel : INotifyPropertyChanged
    {
        private int _id;
        private string _name;
        private string _code;
        private string _color;
        private string _description;
        private bool _isActive;
        private int _productCount;
        private int? _parentId;
        private string _parentName;

        /// <summary>
        /// معرف الفئة
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// كود الفئة
        /// </summary>
        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// لون الفئة
        /// </summary>
        public string Color
        {
            get => _color;
            set
            {
                if (_color != value)
                {
                    _color = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// وصف الفئة
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// هل الفئة نشطة
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// عدد المنتجات في الفئة
        /// </summary>
        public int ProductCount
        {
            get => _productCount;
            set
            {
                if (_productCount != value)
                {
                    _productCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// معرف الفئة الأب
        /// </summary>
        public int? ParentId
        {
            get => _parentId;
            set
            {
                if (_parentId != value)
                {
                    _parentId = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// اسم الفئة الأب
        /// </summary>
        public string ParentName
        {
            get => _parentName;
            set
            {
                if (_parentName != value)
                {
                    _parentName = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حدث تغيير الخاصية
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// إشعار بتغيير الخاصية
        /// </summary>
        /// <param name="propertyName">اسم الخاصية</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
