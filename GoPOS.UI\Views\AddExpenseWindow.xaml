<Window x:Class="GoPOS.UI.Views.AddExpenseWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        xmlns:System="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="إضافة منصرف جديد" Height="550" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock x:Name="WindowTitleTextBlock" Text="إضافة منصرف جديد" FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Text="💸" FontSize="20" Margin="10,0,0,0" VerticalAlignment="Center" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- نموذج إضافة المنصرف -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <!-- المبلغ -->
                <TextBlock Text="المبلغ *" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,5,0,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox x:Name="AmountTextBox" Grid.Column="0" Padding="10,8" BorderThickness="0" TextChanged="AmountTextBox_TextChanged"/>
                        <TextBlock Grid.Column="1" Text="ر.س" Padding="10,8" Foreground="Gray"/>
                    </Grid>
                </Border>

                <!-- الوصف -->
                <TextBlock Text="الوصف *" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,5,0,15">
                    <TextBox x:Name="DescriptionTextBox" Padding="10,8" BorderThickness="0" TextWrapping="Wrap"/>
                </Border>

                <!-- نوع المنصرف -->
                <TextBlock Text="نوع المنصرف *" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,5,0,15">
                    <ComboBox x:Name="ExpenseTypeComboBox" Padding="10,8" BorderThickness="0">
                        <ComboBoxItem Content="مصاريف تشغيلية" Tag="Operational"/>
                        <ComboBoxItem Content="رواتب" Tag="Salary"/>
                        <ComboBoxItem Content="إيجار" Tag="Rent"/>
                        <ComboBoxItem Content="مرافق" Tag="Utilities"/>
                        <ComboBoxItem Content="مشتريات" Tag="Purchases"/>
                        <ComboBoxItem Content="أخرى" Tag="Other"/>
                    </ComboBox>
                </Border>

                <!-- المستفيد -->
                <TextBlock Text="المستفيد" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,5,0,15">
                    <TextBox x:Name="BeneficiaryTextBox" Padding="10,8" BorderThickness="0"/>
                </Border>

                <!-- رقم المستند -->
                <TextBlock Text="رقم المستند" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,5,0,15">
                    <TextBox x:Name="DocumentNumberTextBox" Padding="10,8" BorderThickness="0"/>
                </Border>

                <!-- التاريخ -->
                <TextBlock Text="التاريخ *" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,5,0,15">
                    <DatePicker x:Name="ExpenseDatePicker" Padding="10,8" BorderThickness="0" SelectedDate="{Binding Source={x:Static System:DateTime.Now}}"/>
                </Border>

                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,5,0,15">
                    <TextBox x:Name="NotesTextBox" Padding="10,8" BorderThickness="0" TextWrapping="Wrap" Height="60" AcceptsReturn="True"/>
                </Border>

                <!-- تأكيد المنصرف -->
                <CheckBox x:Name="ConfirmExpenseCheckBox" Content="تأكيد المنصرف" IsChecked="True" Margin="0,0,0,15"/>

                <!-- رسالة الخطأ -->
                <TextBlock x:Name="ErrorMessageTextBlock" Foreground="Red" TextWrapping="Wrap" Visibility="Collapsed" Margin="0,0,0,15"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="2" Background="#F5F5F5" Margin="20,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock x:Name="RequiredFieldsTextBlock" Grid.Column="0" Text="* حقول مطلوبة" Foreground="Gray" VerticalAlignment="Center"/>

            <Button x:Name="CancelButton" Grid.Column="1" Content="إلغاء" Width="100" Margin="0,0,10,0" Padding="10,8" Click="CancelButton_Click">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>

            <Button x:Name="SaveButton" Grid.Column="2" Content="حفظ" Width="100" Padding="10,8" Background="#4CAF50" Foreground="White" Click="SaveButton_Click">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </Grid>
    </Grid>
</Window>
