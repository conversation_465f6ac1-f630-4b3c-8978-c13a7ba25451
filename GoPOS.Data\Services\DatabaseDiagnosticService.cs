using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.Data.Services
{
    /// <summary>
    /// خدمة تشخيص وإصلاح قاعدة البيانات
    /// </summary>
    public class DatabaseDiagnosticService
    {
        private readonly DatabaseContext _dbContext;
        private readonly MaintenanceRepository _maintenanceRepository;
        private readonly string _logFilePath;
        private readonly int _developerId;

        /// <summary>
        /// إنشاء خدمة تشخيص وإصلاح قاعدة البيانات
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        /// <param name="developerId">معرف المستخدم المطور</param>
        public DatabaseDiagnosticService(DatabaseContext dbContext, int developerId)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _maintenanceRepository = new MaintenanceRepository(dbContext);
            _developerId = developerId;

            // إنشاء مجلد السجلات إذا لم يكن موجودًا
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // تحديد مسار ملف السجل
            _logFilePath = Path.Combine(logDirectory, "DatabaseDiagnostic.log");
        }

        /// <summary>
        /// تشخيص وإصلاح قاعدة البيانات
        /// </summary>
        /// <param name="autoFix">إصلاح المشاكل تلقائيًا</param>
        /// <returns>تقرير التشخيص</returns>
        public async Task<DiagnosticReport> DiagnoseAndFixDatabaseAsync(bool autoFix = false)
        {
            DiagnosticReport report = new DiagnosticReport
            {
                StartTime = DateTime.Now,
                Issues = new List<DiagnosticIssue>()
            };

            try
            {
                LogMessage("بدء تشخيص قاعدة البيانات...");

                // فحص اتصال قاعدة البيانات
                await CheckDatabaseConnectionAsync(report, autoFix);

                // فحص سلامة الجداول
                await CheckTablesIntegrityAsync(report, autoFix);

                // فحص البيانات المتكررة
                await CheckDuplicateDataAsync(report, autoFix);

                // فحص القيم الفارغة في الحقول الإلزامية
                await CheckRequiredFieldsAsync(report, autoFix);

                // فحص الفهارس
                await CheckIndexesAsync(report, autoFix);

                // فحص مساحة قاعدة البيانات
                await CheckDatabaseSizeAsync(report, autoFix);

                report.EndTime = DateTime.Now;
                report.Duration = (report.EndTime - report.StartTime).TotalSeconds;
                report.Success = true;

                // تسجيل نتيجة التشخيص
                string issuesCount = report.Issues.Count > 0 ? $"تم العثور على {report.Issues.Count} مشكلة" : "لم يتم العثور على أي مشاكل";
                string fixedCount = report.Issues.Count(i => i.Fixed) > 0 ? $"تم إصلاح {report.Issues.Count(i => i.Fixed)} مشكلة" : "";
                
                string logDetails = $"اكتمل تشخيص قاعدة البيانات. {issuesCount}. {fixedCount}";
                LogMessage(logDetails);

                // تسجيل العملية في قاعدة البيانات
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "تشخيص قاعدة البيانات",
                    logDetails);

                return report;
            }
            catch (Exception ex)
            {
                report.EndTime = DateTime.Now;
                report.Duration = (report.EndTime - report.StartTime).TotalSeconds;
                report.Success = false;
                report.ErrorMessage = ex.Message;

                LogMessage($"حدث خطأ أثناء تشخيص قاعدة البيانات: {ex.Message}");

                // تسجيل الخطأ في قاعدة البيانات
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "خطأ في تشخيص قاعدة البيانات",
                    $"حدث خطأ أثناء تشخيص قاعدة البيانات: {ex.Message}");

                return report;
            }
        }

        #region فحوصات قاعدة البيانات

        /// <summary>
        /// فحص اتصال قاعدة البيانات
        /// </summary>
        private async Task CheckDatabaseConnectionAsync(DiagnosticReport report, bool autoFix)
        {
            try
            {
                LogMessage("فحص اتصال قاعدة البيانات...");

                // محاولة الاتصال بقاعدة البيانات
                string query = "SELECT 1";
                await _dbContext.ExecuteScalarAsync(query);

                LogMessage("اتصال قاعدة البيانات يعمل بشكل صحيح");
            }
            catch (Exception ex)
            {
                DiagnosticIssue issue = new DiagnosticIssue
                {
                    IssueType = "اتصال قاعدة البيانات",
                    Description = $"فشل الاتصال بقاعدة البيانات: {ex.Message}",
                    Severity = IssueSeverity.Critical,
                    Fixed = false
                };

                report.Issues.Add(issue);
                LogMessage($"مشكلة: {issue.Description}");

                // لا يمكن إصلاح مشاكل الاتصال تلقائيًا
            }
        }

        /// <summary>
        /// فحص سلامة الجداول
        /// </summary>
        private async Task CheckTablesIntegrityAsync(DiagnosticReport report, bool autoFix)
        {
            try
            {
                LogMessage("فحص سلامة الجداول...");

                // الحصول على قائمة الجداول
                string query = @"
                    SELECT TABLE_NAME 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_TYPE = 'BASE TABLE'";

                DataTable tablesTable = await _dbContext.ExecuteQueryAsync(query);
                List<string> tables = new List<string>();

                foreach (DataRow row in tablesTable.Rows)
                {
                    tables.Add(row["TABLE_NAME"].ToString());
                }

                // فحص كل جدول
                foreach (string table in tables)
                {
                    try
                    {
                        // فحص سلامة الجدول
                        string checkQuery = $"DBCC CHECKTABLE ([{table}]) WITH NO_INFOMSGS";
                        await _dbContext.ExecuteNonQueryAsync(checkQuery);
                    }
                    catch (Exception ex)
                    {
                        DiagnosticIssue issue = new DiagnosticIssue
                        {
                            IssueType = "سلامة الجدول",
                            Description = $"مشكلة في جدول {table}: {ex.Message}",
                            Severity = IssueSeverity.High,
                            Fixed = false
                        };

                        report.Issues.Add(issue);
                        LogMessage($"مشكلة: {issue.Description}");

                        if (autoFix)
                        {
                            try
                            {
                                // محاولة إصلاح الجدول
                                string repairQuery = $"DBCC CHECKTABLE ([{table}], REPAIR_ALLOW_DATA_LOSS) WITH NO_INFOMSGS";
                                await _dbContext.ExecuteNonQueryAsync(repairQuery);

                                issue.Fixed = true;
                                LogMessage($"تم إصلاح مشكلة في جدول {table}");
                            }
                            catch (Exception repairEx)
                            {
                                LogMessage($"فشل إصلاح جدول {table}: {repairEx.Message}");
                            }
                        }
                    }
                }

                LogMessage("اكتمل فحص سلامة الجداول");
            }
            catch (Exception ex)
            {
                DiagnosticIssue issue = new DiagnosticIssue
                {
                    IssueType = "فحص سلامة الجداول",
                    Description = $"فشل فحص سلامة الجداول: {ex.Message}",
                    Severity = IssueSeverity.High,
                    Fixed = false
                };

                report.Issues.Add(issue);
                LogMessage($"مشكلة: {issue.Description}");
            }
        }

        /// <summary>
        /// فحص البيانات المتكررة
        /// </summary>
        private async Task CheckDuplicateDataAsync(DiagnosticReport report, bool autoFix)
        {
            try
            {
                LogMessage("فحص البيانات المتكررة...");

                // فحص المنتجات المتكررة (نفس الباركود)
                string productsQuery = @"
                    SELECT Barcode, COUNT(*) as Count
                    FROM Products
                    GROUP BY Barcode
                    HAVING COUNT(*) > 1";

                DataTable duplicateProducts = await _dbContext.ExecuteQueryAsync(productsQuery);

                if (duplicateProducts.Rows.Count > 0)
                {
                    DiagnosticIssue issue = new DiagnosticIssue
                    {
                        IssueType = "بيانات متكررة",
                        Description = $"تم العثور على {duplicateProducts.Rows.Count} باركود متكرر في جدول المنتجات",
                        Severity = IssueSeverity.Medium,
                        Fixed = false
                    };

                    report.Issues.Add(issue);
                    LogMessage($"مشكلة: {issue.Description}");

                    if (autoFix)
                    {
                        try
                        {
                            // إصلاح المنتجات المتكررة (الاحتفاظ بأحدث منتج وتعطيل البقية)
                            foreach (DataRow row in duplicateProducts.Rows)
                            {
                                string barcode = row["Barcode"].ToString();
                                
                                string fixQuery = $@"
                                    UPDATE Products
                                    SET IsActive = 0
                                    WHERE Barcode = '{barcode}'
                                    AND Id NOT IN (
                                        SELECT TOP 1 Id
                                        FROM Products
                                        WHERE Barcode = '{barcode}'
                                        ORDER BY CreatedAt DESC
                                    )";

                                await _dbContext.ExecuteNonQueryAsync(fixQuery);
                            }

                            issue.Fixed = true;
                            LogMessage("تم إصلاح المنتجات المتكررة");
                        }
                        catch (Exception fixEx)
                        {
                            LogMessage($"فشل إصلاح المنتجات المتكررة: {fixEx.Message}");
                        }
                    }
                }

                LogMessage("اكتمل فحص البيانات المتكررة");
            }
            catch (Exception ex)
            {
                DiagnosticIssue issue = new DiagnosticIssue
                {
                    IssueType = "فحص البيانات المتكررة",
                    Description = $"فشل فحص البيانات المتكررة: {ex.Message}",
                    Severity = IssueSeverity.Medium,
                    Fixed = false
                };

                report.Issues.Add(issue);
                LogMessage($"مشكلة: {issue.Description}");
            }
        }

        /// <summary>
        /// فحص القيم الفارغة في الحقول الإلزامية
        /// </summary>
        private async Task CheckRequiredFieldsAsync(DiagnosticReport report, bool autoFix)
        {
            try
            {
                LogMessage("فحص القيم الفارغة في الحقول الإلزامية...");

                // فحص المنتجات ذات الأسماء الفارغة
                string productsQuery = @"
                    SELECT Id, Barcode
                    FROM Products
                    WHERE Name IS NULL OR Name = ''";

                DataTable emptyNameProducts = await _dbContext.ExecuteQueryAsync(productsQuery);

                if (emptyNameProducts.Rows.Count > 0)
                {
                    DiagnosticIssue issue = new DiagnosticIssue
                    {
                        IssueType = "حقول إلزامية فارغة",
                        Description = $"تم العثور على {emptyNameProducts.Rows.Count} منتج بدون اسم",
                        Severity = IssueSeverity.Medium,
                        Fixed = false
                    };

                    report.Issues.Add(issue);
                    LogMessage($"مشكلة: {issue.Description}");

                    if (autoFix)
                    {
                        try
                        {
                            // إصلاح المنتجات ذات الأسماء الفارغة
                            foreach (DataRow row in emptyNameProducts.Rows)
                            {
                                int productId = Convert.ToInt32(row["Id"]);
                                string barcode = row["Barcode"].ToString();
                                
                                string fixQuery = $@"
                                    UPDATE Products
                                    SET Name = 'منتج {productId} - {barcode}'
                                    WHERE Id = {productId}";

                                await _dbContext.ExecuteNonQueryAsync(fixQuery);
                            }

                            issue.Fixed = true;
                            LogMessage("تم إصلاح المنتجات ذات الأسماء الفارغة");
                        }
                        catch (Exception fixEx)
                        {
                            LogMessage($"فشل إصلاح المنتجات ذات الأسماء الفارغة: {fixEx.Message}");
                        }
                    }
                }

                LogMessage("اكتمل فحص القيم الفارغة في الحقول الإلزامية");
            }
            catch (Exception ex)
            {
                DiagnosticIssue issue = new DiagnosticIssue
                {
                    IssueType = "فحص القيم الفارغة",
                    Description = $"فشل فحص القيم الفارغة في الحقول الإلزامية: {ex.Message}",
                    Severity = IssueSeverity.Medium,
                    Fixed = false
                };

                report.Issues.Add(issue);
                LogMessage($"مشكلة: {issue.Description}");
            }
        }

        /// <summary>
        /// فحص الفهارس
        /// </summary>
        private async Task CheckIndexesAsync(DiagnosticReport report, bool autoFix)
        {
            try
            {
                LogMessage("فحص الفهارس...");

                // فحص الفهارس المتضررة
                string query = @"
                    SELECT OBJECT_NAME(ind.OBJECT_ID) AS TableName,
                           ind.name AS IndexName,
                           indexstats.avg_fragmentation_in_percent
                    FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, NULL) indexstats
                    INNER JOIN sys.indexes ind ON ind.object_id = indexstats.object_id
                                              AND ind.index_id = indexstats.index_id
                    WHERE indexstats.avg_fragmentation_in_percent > 30
                    ORDER BY indexstats.avg_fragmentation_in_percent DESC";

                DataTable fragmentedIndexes = await _dbContext.ExecuteQueryAsync(query);

                if (fragmentedIndexes.Rows.Count > 0)
                {
                    DiagnosticIssue issue = new DiagnosticIssue
                    {
                        IssueType = "فهارس متضررة",
                        Description = $"تم العثور على {fragmentedIndexes.Rows.Count} فهرس متضرر",
                        Severity = IssueSeverity.Low,
                        Fixed = false
                    };

                    report.Issues.Add(issue);
                    LogMessage($"مشكلة: {issue.Description}");

                    if (autoFix)
                    {
                        try
                        {
                            // إعادة بناء الفهارس المتضررة
                            foreach (DataRow row in fragmentedIndexes.Rows)
                            {
                                string tableName = row["TableName"].ToString();
                                string indexName = row["IndexName"].ToString();
                                double fragmentation = Convert.ToDouble(row["avg_fragmentation_in_percent"]);
                                
                                string fixQuery;
                                if (fragmentation > 70)
                                {
                                    // إعادة بناء الفهرس
                                    fixQuery = $"ALTER INDEX [{indexName}] ON [{tableName}] REBUILD";
                                }
                                else
                                {
                                    // إعادة تنظيم الفهرس
                                    fixQuery = $"ALTER INDEX [{indexName}] ON [{tableName}] REORGANIZE";
                                }

                                await _dbContext.ExecuteNonQueryAsync(fixQuery);
                            }

                            issue.Fixed = true;
                            LogMessage("تم إصلاح الفهارس المتضررة");
                        }
                        catch (Exception fixEx)
                        {
                            LogMessage($"فشل إصلاح الفهارس المتضررة: {fixEx.Message}");
                        }
                    }
                }

                LogMessage("اكتمل فحص الفهارس");
            }
            catch (Exception ex)
            {
                DiagnosticIssue issue = new DiagnosticIssue
                {
                    IssueType = "فحص الفهارس",
                    Description = $"فشل فحص الفهارس: {ex.Message}",
                    Severity = IssueSeverity.Low,
                    Fixed = false
                };

                report.Issues.Add(issue);
                LogMessage($"مشكلة: {issue.Description}");
            }
        }

        /// <summary>
        /// فحص مساحة قاعدة البيانات
        /// </summary>
        private async Task CheckDatabaseSizeAsync(DiagnosticReport report, bool autoFix)
        {
            try
            {
                LogMessage("فحص مساحة قاعدة البيانات...");

                // الحصول على حجم قاعدة البيانات
                string query = @"
                    SELECT 
                        DB_NAME() AS DatabaseName,
                        CAST(SUM(size * 8.0 / 1024) AS DECIMAL(18,2)) AS SizeMB
                    FROM sys.database_files
                    WHERE type_desc = 'ROWS'";

                DataTable sizeTable = await _dbContext.ExecuteQueryAsync(query);
                
                if (sizeTable.Rows.Count > 0)
                {
                    double sizeMB = Convert.ToDouble(sizeTable.Rows[0]["SizeMB"]);
                    
                    // إذا كان حجم قاعدة البيانات أكبر من 1 جيجابايت
                    if (sizeMB > 1024)
                    {
                        DiagnosticIssue issue = new DiagnosticIssue
                        {
                            IssueType = "حجم قاعدة البيانات",
                            Description = $"حجم قاعدة البيانات كبير: {sizeMB:N2} ميجابايت",
                            Severity = IssueSeverity.Low,
                            Fixed = false
                        };

                        report.Issues.Add(issue);
                        LogMessage($"مشكلة: {issue.Description}");

                        if (autoFix)
                        {
                            try
                            {
                                // ضغط قاعدة البيانات
                                string fixQuery = "DBCC SHRINKDATABASE (0, 10)";
                                await _dbContext.ExecuteNonQueryAsync(fixQuery);

                                issue.Fixed = true;
                                LogMessage("تم ضغط قاعدة البيانات");
                            }
                            catch (Exception fixEx)
                            {
                                LogMessage($"فشل ضغط قاعدة البيانات: {fixEx.Message}");
                            }
                        }
                    }
                }

                LogMessage("اكتمل فحص مساحة قاعدة البيانات");
            }
            catch (Exception ex)
            {
                DiagnosticIssue issue = new DiagnosticIssue
                {
                    IssueType = "فحص مساحة قاعدة البيانات",
                    Description = $"فشل فحص مساحة قاعدة البيانات: {ex.Message}",
                    Severity = IssueSeverity.Low,
                    Fixed = false
                };

                report.Issues.Add(issue);
                LogMessage($"مشكلة: {issue.Description}");
            }
        }

        #endregion

        /// <summary>
        /// تسجيل رسالة في ملف السجل
        /// </summary>
        private void LogMessage(string message)
        {
            try
            {
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
                File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
            }
            catch
            {
                // تجاهل أي خطأ أثناء تسجيل الرسالة
            }
        }
    }

    /// <summary>
    /// تقرير تشخيص قاعدة البيانات
    /// </summary>
    public class DiagnosticReport
    {
        /// <summary>
        /// وقت بدء التشخيص
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// وقت انتهاء التشخيص
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// مدة التشخيص بالثواني
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// هل نجح التشخيص
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// رسالة الخطأ (إذا فشل التشخيص)
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// قائمة المشاكل التي تم اكتشافها
        /// </summary>
        public List<DiagnosticIssue> Issues { get; set; }

        /// <summary>
        /// الحصول على عدد المشاكل التي تم إصلاحها
        /// </summary>
        public int FixedIssuesCount => Issues?.Count(i => i.Fixed) ?? 0;
    }

    /// <summary>
    /// مشكلة تم اكتشافها أثناء التشخيص
    /// </summary>
    public class DiagnosticIssue
    {
        /// <summary>
        /// نوع المشكلة
        /// </summary>
        public string IssueType { get; set; }

        /// <summary>
        /// وصف المشكلة
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// خطورة المشكلة
        /// </summary>
        public IssueSeverity Severity { get; set; }

        /// <summary>
        /// هل تم إصلاح المشكلة
        /// </summary>
        public bool Fixed { get; set; }
    }

    /// <summary>
    /// خطورة المشكلة
    /// </summary>
    public enum IssueSeverity
    {
        /// <summary>
        /// منخفضة
        /// </summary>
        Low,

        /// <summary>
        /// متوسطة
        /// </summary>
        Medium,

        /// <summary>
        /// عالية
        /// </summary>
        High,

        /// <summary>
        /// حرجة
        /// </summary>
        Critical
    }
}
