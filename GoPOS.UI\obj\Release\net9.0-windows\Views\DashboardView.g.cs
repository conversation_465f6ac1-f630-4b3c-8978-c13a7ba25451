﻿#pragma checksum "..\..\..\..\Views\DashboardView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "ECCF696FEA371D1028913B05D9C529206332EF3E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// DashboardView
    /// </summary>
    public partial class DashboardView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PeriodComboBox;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSalesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SalesChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OrderCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OrdersChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AverageOrderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AverageChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewCustomersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomersChangeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas SalesChartCanvas;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas CategoryChartCanvas;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView TopProductsListView;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\DashboardView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView TopCustomersListView;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/dashboardview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DashboardView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PeriodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 28 "..\..\..\..\Views\DashboardView.xaml"
            this.PeriodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PeriodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\..\Views\DashboardView.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TotalSalesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SalesChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.OrderCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.OrdersChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AverageOrderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AverageChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.NewCustomersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CustomersChangeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SalesChartCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 12:
            this.CategoryChartCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 13:
            this.TopProductsListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 14:
            this.TopCustomersListView = ((System.Windows.Controls.ListView)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

