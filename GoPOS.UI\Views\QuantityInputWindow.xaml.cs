using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for QuantityInputWindow.xaml
    /// </summary>
    public partial class QuantityInputWindow : Window
    {
        private int _systemQuantity;
        private int _actualQuantity;
        private int _difference;

        public int Quantity => _actualQuantity;

        public QuantityInputWindow(int systemQuantity)
        {
            InitializeComponent();
            
            _systemQuantity = systemQuantity;
            SystemQuantityTextBlock.Text = _systemQuantity.ToString();
            
            // تعيين الكمية الفعلية مساوية للكمية في النظام كقيمة افتراضية
            _actualQuantity = _systemQuantity;
            ActualQuantityTextBox.Text = _actualQuantity.ToString();
            
            // التركيز على حقل الكمية الفعلية وتحديد النص
            ActualQuantityTextBox.Focus();
            ActualQuantityTextBox.SelectAll();
        }

        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        private void ActualQuantityTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            if (int.TryParse(ActualQuantityTextBox.Text, out int quantity))
            {
                _actualQuantity = quantity;
                _difference = _actualQuantity - _systemQuantity;
                
                DifferenceTextBlock.Text = _difference.ToString();
                
                // تغيير لون الفرق حسب القيمة
                if (_difference < 0)
                    DifferenceTextBlock.Foreground = Brushes.Red;
                else if (_difference > 0)
                    DifferenceTextBlock.Foreground = Brushes.Green;
                else
                    DifferenceTextBlock.Foreground = Brushes.Black;
            }
            else
            {
                DifferenceTextBlock.Text = "0";
                DifferenceTextBlock.Foreground = Brushes.Black;
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(ActualQuantityTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال الكمية الفعلية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                ActualQuantityTextBox.Focus();
                return;
            }
            
            if (!int.TryParse(ActualQuantityTextBox.Text, out _actualQuantity) || _actualQuantity < 0)
            {
                MessageBox.Show("الرجاء إدخال كمية صحيحة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                ActualQuantityTextBox.Focus();
                ActualQuantityTextBox.SelectAll();
                return;
            }
            
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
