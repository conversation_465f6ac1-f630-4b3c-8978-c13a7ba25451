using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for BackupSchedulerWindow.xaml
    /// </summary>
    public partial class BackupSchedulerWindow : Window
    {
        // إعدادات النسخ الاحتياطي
        public bool EnableAutoBackup { get; private set; }
        public string BackupFrequency { get; private set; }
        public string WeekDay { get; private set; }
        public int MonthDay { get; private set; }
        public int BackupHour { get; private set; }
        public int BackupMinute { get; private set; }
        public string BackupPath { get; private set; }

        public BackupSchedulerWindow(bool enableAutoBackup, string backupFrequency, string weekDay, int monthDay, int backupHour, int backupMinute, string backupPath)
        {
            InitializeComponent();
            
            // تعيين القيم الأولية
            EnableAutoBackup = enableAutoBackup;
            BackupFrequency = backupFrequency;
            WeekDay = weekDay;
            MonthDay = monthDay;
            BackupHour = backupHour;
            BackupMinute = backupMinute;
            BackupPath = backupPath;
            
            // تحميل الإعدادات في واجهة المستخدم
            LoadSettings();
        }

        private void LoadSettings()
        {
            // تعيين حالة تفعيل النسخ الاحتياطي التلقائي
            EnableAutoBackupCheckBox.IsChecked = EnableAutoBackup;
            
            // تعيين تكرار النسخ الاحتياطي
            switch (BackupFrequency)
            {
                case "Daily":
                    BackupFrequencyComboBox.SelectedIndex = 0;
                    break;
                case "Weekly":
                    BackupFrequencyComboBox.SelectedIndex = 1;
                    break;
                case "Monthly":
                    BackupFrequencyComboBox.SelectedIndex = 2;
                    break;
                default:
                    BackupFrequencyComboBox.SelectedIndex = 1; // أسبوعي كقيمة افتراضية
                    break;
            }
            
            // تعيين يوم الأسبوع
            switch (WeekDay)
            {
                case "Saturday":
                    WeekDayComboBox.SelectedIndex = 0;
                    break;
                case "Sunday":
                    WeekDayComboBox.SelectedIndex = 1;
                    break;
                case "Monday":
                    WeekDayComboBox.SelectedIndex = 2;
                    break;
                case "Tuesday":
                    WeekDayComboBox.SelectedIndex = 3;
                    break;
                case "Wednesday":
                    WeekDayComboBox.SelectedIndex = 4;
                    break;
                case "Thursday":
                    WeekDayComboBox.SelectedIndex = 5;
                    break;
                case "Friday":
                    WeekDayComboBox.SelectedIndex = 6;
                    break;
                default:
                    WeekDayComboBox.SelectedIndex = 1; // الأحد كقيمة افتراضية
                    break;
            }
            
            // تعيين يوم الشهر
            if (MonthDay == -1) // آخر يوم في الشهر
            {
                MonthDayComboBox.SelectedIndex = 9;
            }
            else if (MonthDay >= 1 && MonthDay <= 5)
            {
                MonthDayComboBox.SelectedIndex = MonthDay - 1;
            }
            else
            {
                switch (MonthDay)
                {
                    case 10:
                        MonthDayComboBox.SelectedIndex = 5;
                        break;
                    case 15:
                        MonthDayComboBox.SelectedIndex = 6;
                        break;
                    case 20:
                        MonthDayComboBox.SelectedIndex = 7;
                        break;
                    case 25:
                        MonthDayComboBox.SelectedIndex = 8;
                        break;
                    default:
                        MonthDayComboBox.SelectedIndex = 0; // اليوم الأول كقيمة افتراضية
                        break;
                }
            }
            
            // تعيين وقت النسخ الاحتياطي
            BackupHourComboBox.SelectedIndex = BackupHour;
            
            switch (BackupMinute)
            {
                case 0:
                    BackupMinuteComboBox.SelectedIndex = 0;
                    break;
                case 15:
                    BackupMinuteComboBox.SelectedIndex = 1;
                    break;
                case 30:
                    BackupMinuteComboBox.SelectedIndex = 2;
                    break;
                case 45:
                    BackupMinuteComboBox.SelectedIndex = 3;
                    break;
                default:
                    BackupMinuteComboBox.SelectedIndex = 0; // 00 كقيمة افتراضية
                    break;
            }
            
            // تعيين مسار النسخ الاحتياطي
            BackupPathTextBox.Text = BackupPath;
            
            // تحديث حالة عناصر واجهة المستخدم
            UpdateUIState();
        }

        private void UpdateUIState()
        {
            // تحديث حالة عناصر واجهة المستخدم بناءً على الإعدادات الحالية
            bool isEnabled = EnableAutoBackupCheckBox.IsChecked == true;
            
            BackupFrequencyComboBox.IsEnabled = isEnabled;
            WeekDayComboBox.IsEnabled = isEnabled;
            MonthDayComboBox.IsEnabled = isEnabled;
            BackupHourComboBox.IsEnabled = isEnabled;
            BackupMinuteComboBox.IsEnabled = isEnabled;
            BackupPathTextBox.IsEnabled = isEnabled;
            BrowsePathButton.IsEnabled = isEnabled;
            
            // تحديث ظهور عناصر واجهة المستخدم بناءً على تكرار النسخ الاحتياطي
            if (BackupFrequencyComboBox.SelectedIndex == 1) // أسبوعي
            {
                WeekDayTextBlock.Visibility = Visibility.Visible;
                WeekDayComboBox.Visibility = Visibility.Visible;
                MonthDayTextBlock.Visibility = Visibility.Collapsed;
                MonthDayComboBox.Visibility = Visibility.Collapsed;
            }
            else if (BackupFrequencyComboBox.SelectedIndex == 2) // شهري
            {
                WeekDayTextBlock.Visibility = Visibility.Collapsed;
                WeekDayComboBox.Visibility = Visibility.Collapsed;
                MonthDayTextBlock.Visibility = Visibility.Visible;
                MonthDayComboBox.Visibility = Visibility.Visible;
            }
            else // يومي
            {
                WeekDayTextBlock.Visibility = Visibility.Collapsed;
                WeekDayComboBox.Visibility = Visibility.Collapsed;
                MonthDayTextBlock.Visibility = Visibility.Collapsed;
                MonthDayComboBox.Visibility = Visibility.Collapsed;
            }
        }

        private void EnableAutoBackupCheckBox_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // تحديث حالة عناصر واجهة المستخدم
            UpdateUIState();
        }

        private void BackupFrequencyComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تحديث ظهور عناصر واجهة المستخدم
            UpdateUIState();
        }

        private void BrowsePathButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح مربع حوار لاختيار مجلد النسخ الاحتياطي
                var dialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "اختيار مجلد النسخ الاحتياطي التلقائي",
                    ShowNewFolderButton = true
                };
                
                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    // تحديث مسار النسخ الاحتياطي
                    BackupPathTextBox.Text = dialog.SelectedPath;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اختيار مجلد النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة الإدخال
                if (EnableAutoBackupCheckBox.IsChecked == true && string.IsNullOrWhiteSpace(BackupPathTextBox.Text))
                {
                    MessageBox.Show("الرجاء تحديد مسار النسخ الاحتياطي", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    BackupPathTextBox.Focus();
                    return;
                }
                
                // حفظ الإعدادات
                EnableAutoBackup = EnableAutoBackupCheckBox.IsChecked == true;
                
                switch (BackupFrequencyComboBox.SelectedIndex)
                {
                    case 0:
                        BackupFrequency = "Daily";
                        break;
                    case 1:
                        BackupFrequency = "Weekly";
                        break;
                    case 2:
                        BackupFrequency = "Monthly";
                        break;
                }
                
                switch (WeekDayComboBox.SelectedIndex)
                {
                    case 0:
                        WeekDay = "Saturday";
                        break;
                    case 1:
                        WeekDay = "Sunday";
                        break;
                    case 2:
                        WeekDay = "Monday";
                        break;
                    case 3:
                        WeekDay = "Tuesday";
                        break;
                    case 4:
                        WeekDay = "Wednesday";
                        break;
                    case 5:
                        WeekDay = "Thursday";
                        break;
                    case 6:
                        WeekDay = "Friday";
                        break;
                }
                
                switch (MonthDayComboBox.SelectedIndex)
                {
                    case 0:
                        MonthDay = 1;
                        break;
                    case 1:
                        MonthDay = 2;
                        break;
                    case 2:
                        MonthDay = 3;
                        break;
                    case 3:
                        MonthDay = 4;
                        break;
                    case 4:
                        MonthDay = 5;
                        break;
                    case 5:
                        MonthDay = 10;
                        break;
                    case 6:
                        MonthDay = 15;
                        break;
                    case 7:
                        MonthDay = 20;
                        break;
                    case 8:
                        MonthDay = 25;
                        break;
                    case 9:
                        MonthDay = -1; // آخر يوم في الشهر
                        break;
                }
                
                BackupHour = BackupHourComboBox.SelectedIndex;
                
                switch (BackupMinuteComboBox.SelectedIndex)
                {
                    case 0:
                        BackupMinute = 0;
                        break;
                    case 1:
                        BackupMinute = 15;
                        break;
                    case 2:
                        BackupMinute = 30;
                        break;
                    case 3:
                        BackupMinute = 45;
                        break;
                }
                
                BackupPath = BackupPathTextBox.Text;
                
                // إنشاء مجلد النسخ الاحتياطي التلقائي إذا لم يكن موجودًا
                if (EnableAutoBackup)
                {
                    string autoBackupPath = Path.Combine(BackupPath, "Auto");
                    if (!Directory.Exists(autoBackupPath))
                    {
                        Directory.CreateDirectory(autoBackupPath);
                    }
                }
                
                // إغلاق النافذة
                DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة بدون حفظ
            DialogResult = false;
        }
    }
}
