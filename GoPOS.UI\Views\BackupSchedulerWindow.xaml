<Window x:Class="GoPOS.UI.Views.BackupSchedulerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="جدولة النسخ الاحتياطي التلقائي" 
        Height="450" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#4CAF50" CornerRadius="8" Margin="0,0,0,20">
            <Grid Margin="15,10">
                <StackPanel Orientation="Vertical">
                    <TextBlock Text="جدولة النسخ الاحتياطي التلقائي" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="إعداد جدولة النسخ الاحتياطي التلقائي لقاعدة البيانات" FontSize="14" Foreground="White" Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- محتوى النافذة -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- تفعيل النسخ الاحتياطي التلقائي -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="النسخ الاحتياطي التلقائي:" VerticalAlignment="Center" Margin="0,10"/>
            <CheckBox x:Name="EnableAutoBackupCheckBox" Grid.Row="0" Grid.Column="1" Content="تفعيل النسخ الاحتياطي التلقائي" IsChecked="True" VerticalAlignment="Center" Margin="0,10" Checked="EnableAutoBackupCheckBox_CheckedChanged" Unchecked="EnableAutoBackupCheckBox_CheckedChanged"/>

            <!-- تكرار النسخ الاحتياطي -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="تكرار النسخ الاحتياطي:" VerticalAlignment="Center" Margin="0,10"/>
            <ComboBox x:Name="BackupFrequencyComboBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,10" SelectionChanged="BackupFrequencyComboBox_SelectionChanged">
                <ComboBoxItem Content="يومي"/>
                <ComboBoxItem Content="أسبوعي" IsSelected="True"/>
                <ComboBoxItem Content="شهري"/>
            </ComboBox>

            <!-- يوم الأسبوع (للنسخ الأسبوعي) -->
            <TextBlock x:Name="WeekDayTextBlock" Grid.Row="2" Grid.Column="0" Text="يوم الأسبوع:" VerticalAlignment="Center" Margin="0,10"/>
            <ComboBox x:Name="WeekDayComboBox" Grid.Row="2" Grid.Column="1" Height="30" Margin="0,10">
                <ComboBoxItem Content="السبت"/>
                <ComboBoxItem Content="الأحد" IsSelected="True"/>
                <ComboBoxItem Content="الاثنين"/>
                <ComboBoxItem Content="الثلاثاء"/>
                <ComboBoxItem Content="الأربعاء"/>
                <ComboBoxItem Content="الخميس"/>
                <ComboBoxItem Content="الجمعة"/>
            </ComboBox>

            <!-- يوم الشهر (للنسخ الشهري) -->
            <TextBlock x:Name="MonthDayTextBlock" Grid.Row="3" Grid.Column="0" Text="يوم الشهر:" VerticalAlignment="Center" Margin="0,10" Visibility="Collapsed"/>
            <ComboBox x:Name="MonthDayComboBox" Grid.Row="3" Grid.Column="1" Height="30" Margin="0,10" Visibility="Collapsed">
                <ComboBoxItem Content="1" IsSelected="True"/>
                <ComboBoxItem Content="2"/>
                <ComboBoxItem Content="3"/>
                <ComboBoxItem Content="4"/>
                <ComboBoxItem Content="5"/>
                <ComboBoxItem Content="10"/>
                <ComboBoxItem Content="15"/>
                <ComboBoxItem Content="20"/>
                <ComboBoxItem Content="25"/>
                <ComboBoxItem Content="آخر يوم في الشهر"/>
            </ComboBox>

            <!-- وقت النسخ الاحتياطي -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="وقت النسخ الاحتياطي:" VerticalAlignment="Center" Margin="0,10"/>
            <Grid Grid.Row="4" Grid.Column="1" Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <ComboBox x:Name="BackupHourComboBox" Grid.Column="0" Width="80" Height="30">
                    <ComboBoxItem Content="00"/>
                    <ComboBoxItem Content="01"/>
                    <ComboBoxItem Content="02"/>
                    <ComboBoxItem Content="03"/>
                    <ComboBoxItem Content="04"/>
                    <ComboBoxItem Content="05"/>
                    <ComboBoxItem Content="06"/>
                    <ComboBoxItem Content="07"/>
                    <ComboBoxItem Content="08"/>
                    <ComboBoxItem Content="09"/>
                    <ComboBoxItem Content="10"/>
                    <ComboBoxItem Content="11"/>
                    <ComboBoxItem Content="12"/>
                    <ComboBoxItem Content="13"/>
                    <ComboBoxItem Content="14"/>
                    <ComboBoxItem Content="15"/>
                    <ComboBoxItem Content="16"/>
                    <ComboBoxItem Content="17"/>
                    <ComboBoxItem Content="18"/>
                    <ComboBoxItem Content="19"/>
                    <ComboBoxItem Content="20"/>
                    <ComboBoxItem Content="21"/>
                    <ComboBoxItem Content="22" IsSelected="True"/>
                    <ComboBoxItem Content="23"/>
                </ComboBox>
                
                <TextBlock Grid.Column="1" Text=":" FontSize="20" FontWeight="Bold" VerticalAlignment="Center" Margin="5,0"/>
                
                <ComboBox x:Name="BackupMinuteComboBox" Grid.Column="2" Width="80" Height="30" HorizontalAlignment="Left">
                    <ComboBoxItem Content="00" IsSelected="True"/>
                    <ComboBoxItem Content="15"/>
                    <ComboBoxItem Content="30"/>
                    <ComboBoxItem Content="45"/>
                </ComboBox>
            </Grid>

            <!-- مسار النسخ الاحتياطي -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="مسار النسخ الاحتياطي:" VerticalAlignment="Top" Margin="0,10"/>
            <Grid Grid.Row="5" Grid.Column="1" Margin="0,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox x:Name="BackupPathTextBox" Grid.Row="0" Grid.Column="0" Height="30"/>
                <Button x:Name="BrowsePathButton" Grid.Row="0" Grid.Column="1" Content="استعراض" Width="80" Height="30" Margin="10,0,0,0" Click="BrowsePathButton_Click"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Text="ملاحظة: سيتم حفظ النسخ الاحتياطية في مجلد فرعي باسم 'Auto' داخل المسار المحدد." TextWrapping="Wrap" Margin="0,5,0,0" Foreground="#666666"/>
            </Grid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="حفظ الإعدادات" Width="150" Height="40" Click="SaveButton_Click" Background="#4CAF50" Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
            
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="40" Margin="10,0,0,0" Click="CancelButton_Click" Background="#9E9E9E" Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </StackPanel>
    </Grid>
</Window>
