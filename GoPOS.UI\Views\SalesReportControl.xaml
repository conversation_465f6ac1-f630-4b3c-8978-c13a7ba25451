<UserControl x:Class="GoPOS.UI.Views.SalesReportControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- ملخص المبيعات -->
        <Grid Grid.Row="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي المبيعات -->
            <Border Grid.Column="0" Background="#4CAF50" CornerRadius="5" Margin="0,0,5,0">
                <Grid Margin="15,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="إجمالي المبيعات" Foreground="White" FontSize="14"/>
                    <TextBlock x:Name="TotalSalesTextBlock" Grid.Row="1" Text="0.00" Foreground="White" FontSize="20" FontWeight="Bold" Margin="0,5,0,0"/>
                </Grid>
            </Border>

            <!-- عدد الفواتير -->
            <Border Grid.Column="1" Background="#2196F3" CornerRadius="5" Margin="5,0">
                <Grid Margin="15,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="عدد الفواتير" Foreground="White" FontSize="14"/>
                    <TextBlock x:Name="InvoiceCountTextBlock" Grid.Row="1" Text="0" Foreground="White" FontSize="20" FontWeight="Bold" Margin="0,5,0,0"/>
                </Grid>
            </Border>

            <!-- متوسط قيمة الفاتورة -->
            <Border Grid.Column="2" Background="#FF9800" CornerRadius="5" Margin="5,0">
                <Grid Margin="15,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="متوسط قيمة الفاتورة" Foreground="White" FontSize="14"/>
                    <TextBlock x:Name="AverageInvoiceTextBlock" Grid.Row="1" Text="0.00" Foreground="White" FontSize="20" FontWeight="Bold" Margin="0,5,0,0"/>
                </Grid>
            </Border>

            <!-- إجمالي الضرائب -->
            <Border Grid.Column="3" Background="#673AB7" CornerRadius="5" Margin="5,0,0,0">
                <Grid Margin="15,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="إجمالي الضرائب" Foreground="White" FontSize="14"/>
                    <TextBlock x:Name="TotalTaxTextBlock" Grid.Row="1" Text="0.00" Foreground="White" FontSize="20" FontWeight="Bold" Margin="0,5,0,0"/>
                </Grid>
            </Border>
        </Grid>

        <!-- الرسم البياني للمبيعات -->
        <Border Grid.Row="1" Background="#F5F5F5" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="0,0,0,15">
            <Grid Margin="15">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="200"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="المبيعات حسب الفترة" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

                <Canvas x:Name="SalesChartCanvas" Grid.Row="1">
                    <!-- سيتم رسم الرسم البياني برمجياً -->
                </Canvas>
            </Grid>
        </Border>

        <!-- جدول المبيعات -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="تفاصيل المبيعات" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

            <DataGrid x:Name="SalesDataGrid" 
                      Grid.Row="1" 
                      AutoGenerateColumns="False" 
                      IsReadOnly="True" 
                      AlternatingRowBackground="#F5F5F5" 
                      BorderBrush="#DDDDDD" 
                      BorderThickness="1" 
                      RowHeight="35" 
                      HeadersVisibility="Column" 
                      GridLinesVisibility="Horizontal" 
                      HorizontalGridLinesBrush="#EEEEEE">
                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#673AB7"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Padding" Value="10,0"/>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat={}{0:yyyy/MM/dd}}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الوقت" Binding="{Binding Date, StringFormat={}{0:HH:mm}}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="*">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المستخدم" Binding="{Binding UserName}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethodText}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total, StringFormat={}{0:N2}}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الضريبة" Binding="{Binding TaxAmount, StringFormat={}{0:N2}}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="4" Padding="5,2" Margin="5">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}" Value="Completed">
                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="Pending">
                                                    <Setter Property="Background" Value="#FF9800"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                                                    <Setter Property="Background" Value="#F44336"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="Returned">
                                                    <Setter Property="Background" Value="#9C27B0"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Text="{Binding StatusText}" Foreground="White" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>
