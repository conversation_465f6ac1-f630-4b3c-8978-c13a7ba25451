using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using GoPOS.Core.Models;
using GoPOS.Data.DataAccess;
using GoPOS.UI.ViewModels;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InstallmentsManagementView.xaml
    /// </summary>
    public partial class InstallmentsManagementView : UserControl
    {
        private ObservableCollection<InstallmentPlanViewModel> _installmentPlans;
        private ObservableCollection<InstallmentViewModel> _installments;
        private ObservableCollection<CustomerViewModel> _customers;
        private InstallmentPlanViewModel _selectedPlan;
        private InstallmentViewModel _selectedInstallment;
        private InstallmentRepository _installmentRepository;
        private CustomerRepository _customerRepository;
        private DatabaseContext _dbContext;

        public InstallmentsManagementView()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _installmentRepository = new InstallmentRepository(_dbContext);
            _customerRepository = new CustomerRepository(_dbContext);

            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // تحميل العملاء من قاعدة البيانات
                _customers = new ObservableCollection<CustomerViewModel>();
                _customers.Add(new CustomerViewModel { Id = 0, Name = "جميع العملاء" });

                var customers = await _customerRepository.GetAllAsync();
                foreach (var customer in customers)
                {
                    _customers.Add(new CustomerViewModel
                    {
                        Id = customer.Id,
                        Name = customer.Name
                    });
                }

                CustomerFilterComboBox.ItemsSource = _customers;
                CustomerFilterComboBox.SelectedIndex = 0;

                // تحميل خطط الأقساط
                await LoadInstallmentPlansAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async Task LoadInstallmentPlansAsync(int customerId = 0, int statusFilter = -1)
        {
            try
            {
                // إظهار مؤشر التحميل
                Cursor = System.Windows.Input.Cursors.Wait;

                // إنشاء مجموعة جديدة من خطط الأقساط
                _installmentPlans = new ObservableCollection<InstallmentPlanViewModel>();

                // تحميل خطط الأقساط من قاعدة البيانات
                IEnumerable<InstallmentPlan> plans;

                if (customerId > 0 && statusFilter >= 0)
                {
                    // تحميل خطط الأقساط حسب العميل والحالة
                    plans = await _installmentRepository.GetPlansByCustomerAndStatusAsync(customerId, statusFilter);
                }
                else if (customerId > 0)
                {
                    // تحميل خطط الأقساط حسب العميل
                    plans = await _installmentRepository.GetPlansByCustomerAsync(customerId);
                }
                else if (statusFilter >= 0)
                {
                    // تحميل خطط الأقساط حسب الحالة
                    plans = await _installmentRepository.GetPlansByStatusAsync((InstallmentPlanStatus)statusFilter);
                }
                else
                {
                    // تحميل جميع خطط الأقساط
                    plans = await _installmentRepository.GetAllPlansAsync();
                }

                // تحويل خطط الأقساط إلى نماذج عرض
                foreach (var plan in plans)
                {
                    string statusText = "";
                    string canCancel = "Collapsed";

                    switch (plan.Status)
                    {
                        case InstallmentPlanStatus.Active:
                            statusText = "مستحقة";
                            canCancel = "Visible";
                            break;
                        case InstallmentPlanStatus.Completed:
                            statusText = "مدفوعة";
                            break;
                        case InstallmentPlanStatus.Defaulted:
                            statusText = "متأخرة";
                            canCancel = "Visible";
                            break;
                        case InstallmentPlanStatus.Cancelled:
                            statusText = "ملغاة";
                            break;
                    }

                    _installmentPlans.Add(new InstallmentPlanViewModel
                    {
                        Id = plan.Id,
                        OrderId = plan.OrderId,
                        CustomerId = plan.CustomerId,
                        CustomerName = plan.Customer?.Name ?? string.Empty,
                        TotalAmount = plan.TotalAmount,
                        DownPayment = plan.DownPayment,
                        RemainingAmount = plan.RemainingAmount,
                        NumberOfInstallments = plan.NumberOfInstallments,
                        Frequency = (int)plan.Frequency,
                        StartDate = plan.StartDate,
                        Status = (int)plan.Status,
                        StatusText = statusText,
                        CanCancel = canCancel
                    });
                }

                // تعيين مصدر البيانات للجدول
                InstallmentPlansDataGrid.ItemsSource = _installmentPlans;

                // حساب إجمالي المستحقات
                decimal totalDue = _installmentPlans
                    .Where(p => p.Status == 0 || p.Status == 2) // مستحقة أو متأخرة
                    .Sum(p => p.RemainingAmount);

                // تحديث نص إجمالي المستحقات
                TotalDueTextBlock.Text = $"إجمالي المستحقات: {totalDue:N2} ر.س";

                // مسح جدول الأقساط
                _installments = new ObservableCollection<InstallmentViewModel>();
                InstallmentsDataGrid.ItemsSource = _installments;

                // تعطيل زر تسديد القسط
                PayInstallmentButton.IsEnabled = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل خطط الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async Task LoadInstallmentsAsync(int planId)
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // إنشاء مجموعة جديدة من الأقساط
                _installments = new ObservableCollection<InstallmentViewModel>();

                // تحميل الأقساط من قاعدة البيانات
                var installments = await _installmentRepository.GetInstallmentsByPlanAsync(planId);

                // تحويل الأقساط إلى نماذج عرض
                foreach (var installment in installments)
                {
                    string statusText = "";

                    switch (installment.Status)
                    {
                        case InstallmentStatus.Pending:
                            statusText = "مستحق";
                            break;
                        case InstallmentStatus.Paid:
                            statusText = "مدفوع";
                            break;
                        case InstallmentStatus.Overdue:
                            statusText = "متأخر";
                            break;
                        case InstallmentStatus.Cancelled:
                            statusText = "ملغي";
                            break;
                    }

                    _installments.Add(new InstallmentViewModel
                    {
                        Id = installment.Id,
                        PlanId = installment.PlanId,
                        InstallmentNumber = installment.InstallmentNumber,
                        Amount = installment.Amount,
                        DueDate = installment.DueDate,
                        PaymentDate = installment.PaymentDate,
                        Status = (int)installment.Status,
                        StatusText = statusText,
                        Notes = installment.Notes ?? string.Empty
                    });
                }

                // تعيين مصدر البيانات للجدول
                InstallmentsDataGrid.ItemsSource = _installments;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async void CustomerFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تجنب التنفيذ أثناء تحميل الصفحة
                if (!IsLoaded) return;

                // الحصول على العميل المحدد
                CustomerViewModel selectedCustomer = CustomerFilterComboBox.SelectedItem as CustomerViewModel;

                // الحصول على حالة التصفية المحددة
                int statusFilter = -1;
                if (StatusFilterComboBox.SelectedIndex > 0)
                {
                    statusFilter = StatusFilterComboBox.SelectedIndex - 1;
                }

                // تحميل البيانات المصفاة
                if (selectedCustomer != null)
                {
                    await LoadInstallmentPlansAsync(selectedCustomer.Id, statusFilter);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصفية الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تجنب التنفيذ أثناء تحميل الصفحة
                if (!IsLoaded) return;

                // الحصول على العميل المحدد
                CustomerViewModel selectedCustomer = CustomerFilterComboBox.SelectedItem as CustomerViewModel;
                int customerId = selectedCustomer != null ? selectedCustomer.Id : 0;

                // الحصول على حالة التصفية المحددة
                int statusFilter = -1;
                if (StatusFilterComboBox.SelectedIndex > 0)
                {
                    statusFilter = StatusFilterComboBox.SelectedIndex - 1;
                }

                // تحميل البيانات المصفاة
                await LoadInstallmentPlansAsync(customerId, statusFilter);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصفية الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر لمنع النقر المتكرر
                RefreshButton.IsEnabled = false;

                // الحصول على العميل المحدد
                CustomerViewModel selectedCustomer = CustomerFilterComboBox.SelectedItem as CustomerViewModel;
                int customerId = selectedCustomer != null ? selectedCustomer.Id : 0;

                // الحصول على حالة التصفية المحددة
                int statusFilter = -1;
                if (StatusFilterComboBox.SelectedIndex > 0)
                {
                    statusFilter = StatusFilterComboBox.SelectedIndex - 1;
                }

                // تحميل البيانات المصفاة
                await LoadInstallmentPlansAsync(customerId, statusFilter);

                MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين الزر
                RefreshButton.IsEnabled = true;
            }
        }

        private async void InstallmentPlansDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                _selectedPlan = InstallmentPlansDataGrid.SelectedItem as InstallmentPlanViewModel;
                if (_selectedPlan != null)
                {
                    await LoadInstallmentsAsync(_selectedPlan.Id);
                }
                else
                {
                    // مسح جدول الأقساط إذا لم يتم تحديد خطة
                    _installments = new ObservableCollection<InstallmentViewModel>();
                    InstallmentsDataGrid.ItemsSource = _installments;

                    // تعطيل زر تسديد القسط
                    PayInstallmentButton.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void InstallmentsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                _selectedInstallment = InstallmentsDataGrid.SelectedItem as InstallmentViewModel;

                // تمكين زر تسديد القسط فقط إذا كان القسط قيد الانتظار
                PayInstallmentButton.IsEnabled = _selectedInstallment != null && _selectedInstallment.Status == 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديد القسط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                PayInstallmentButton.IsEnabled = false;
            }
        }

        private void PrintPlanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    // تعطيل الزر لمنع النقر المتكرر
                    button.IsEnabled = false;

                    // إظهار مؤشر الانتظار
                    Cursor = System.Windows.Input.Cursors.Wait;

                    InstallmentPlanViewModel plan = button.Tag as InstallmentPlanViewModel;
                    if (plan != null)
                    {
                        // محاكاة تأخير لعملية الطباعة
                        System.Threading.Thread.Sleep(1000);

                        // إعادة المؤشر إلى الوضع الطبيعي
                        Cursor = System.Windows.Input.Cursors.Arrow;

                        MessageBox.Show($"تم طباعة خطة الأقساط للطلب رقم {plan.OrderId} بنجاح", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن العثور على خطة الأقساط", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    // إعادة تمكين الزر بعد فترة قصيرة
                    System.Threading.Tasks.Task.Delay(200).ContinueWith(_ =>
                    {
                        Dispatcher.Invoke(() => button.IsEnabled = true);
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة خطة الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async void CancelPlanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    // تعطيل الزر لمنع النقر المتكرر
                    button.IsEnabled = false;

                    InstallmentPlanViewModel plan = button.Tag as InstallmentPlanViewModel;
                    if (plan != null)
                    {
                        MessageBoxResult result = MessageBox.Show(
                            $"هل أنت متأكد من إلغاء خطة الأقساط للطلب رقم {plan.OrderId}؟",
                            "تأكيد الإلغاء",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            // إظهار مؤشر الانتظار
                            Cursor = System.Windows.Input.Cursors.Wait;

                            // إلغاء خطة الأقساط في قاعدة البيانات
                            await _installmentRepository.UpdatePlanStatusAsync(plan.Id, (int)InstallmentPlanStatus.Cancelled);

                            // إعادة تحميل خطط الأقساط
                            CustomerViewModel selectedCustomer = CustomerFilterComboBox.SelectedItem as CustomerViewModel;
                            int customerId = selectedCustomer != null ? selectedCustomer.Id : 0;

                            int statusFilter = -1;
                            if (StatusFilterComboBox.SelectedIndex > 0)
                            {
                                statusFilter = StatusFilterComboBox.SelectedIndex - 1;
                            }

                            await LoadInstallmentPlansAsync(customerId, statusFilter);

                            // إعادة المؤشر إلى الوضع الطبيعي
                            Cursor = System.Windows.Input.Cursors.Arrow;

                            MessageBox.Show("تم إلغاء خطة الأقساط بنجاح", "تم الإلغاء", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن العثور على خطة الأقساط", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    // إعادة تمكين الزر
                    button.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إلغاء خطة الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async void PayInstallmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر لمنع النقر المتكرر
                PayInstallmentButton.IsEnabled = false;

                if (_selectedInstallment == null || _selectedInstallment.Status != 0)
                {
                    MessageBox.Show("الرجاء تحديد قسط قيد الانتظار لتسديده", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBoxResult result = MessageBox.Show(
                    $"هل تريد تسديد القسط رقم {_selectedInstallment.InstallmentNumber} بمبلغ {_selectedInstallment.Amount:N2} ر.س؟",
                    "تأكيد الدفع",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إظهار مؤشر الانتظار
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // تسديد القسط في قاعدة البيانات
                    await _installmentRepository.PayInstallmentAsync(_selectedInstallment.Id, DateTime.Now, "تم الدفع من خلال نظام إدارة الأقساط");

                    // إعادة تحميل الأقساط
                    await LoadInstallmentsAsync(_selectedPlan.Id);

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;

                    MessageBox.Show("تم تسديد القسط بنجاح", "تم الدفع", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إعادة تحميل خطط الأقساط لتحديث الحالة
                    CustomerViewModel selectedCustomer = CustomerFilterComboBox.SelectedItem as CustomerViewModel;
                    int customerId = selectedCustomer != null ? selectedCustomer.Id : 0;

                    int statusFilter = -1;
                    if (StatusFilterComboBox.SelectedIndex > 0)
                    {
                        statusFilter = StatusFilterComboBox.SelectedIndex - 1;
                    }

                    await LoadInstallmentPlansAsync(customerId, statusFilter);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تسديد القسط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
            finally
            {
                // إعادة تمكين الزر
                PayInstallmentButton.IsEnabled = _selectedInstallment != null && _selectedInstallment.Status == 0;
            }
        }
    }

    // فئات عرض البيانات (تم تعريفها مسبقًا في InstallmentPlanWindow.xaml.cs)
    // تم التعليق لتجنب التكرار
    /*
    public class InstallmentPlanViewModel
    {
        public int OrderId { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DownPayment { get; set; }
        public decimal RemainingAmount { get; set; }
        public int NumberOfInstallments { get; set; }
        public int Frequency { get; set; }
        public DateTime StartDate { get; set; }
        public int Status { get; set; }
        public string StatusText { get; set; }
        public string CanCancel { get; set; }
    }
    */
}
