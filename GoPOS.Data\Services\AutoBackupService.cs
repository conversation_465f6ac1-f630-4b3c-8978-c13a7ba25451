using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.Data.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي التلقائي
    /// </summary>
    public class AutoBackupService
    {
        private readonly MaintenanceRepository _maintenanceRepository;
        private readonly UserRepository _userRepository;
        private Timer _backupTimer;
        private readonly string _logFilePath;
        private readonly int _developerId;
        private readonly BackupSettings _settings;

        /// <summary>
        /// إنشاء خدمة النسخ الاحتياطي التلقائي
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        /// <param name="developerId">معرف المستخدم المطور</param>
        /// <param name="settings">إعدادات النسخ الاحتياطي</param>
        public AutoBackupService(DatabaseContext dbContext, int developerId, BackupSettings settings = null)
        {
            _maintenanceRepository = new MaintenanceRepository(dbContext);
            _userRepository = new UserRepository(dbContext);
            _developerId = developerId;
            _settings = settings ?? new BackupSettings();

            // إنشاء مجلد السجلات إذا لم يكن موجودًا
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // تحديد مسار ملف السجل
            _logFilePath = Path.Combine(logDirectory, "AutoBackup.log");
        }

        /// <summary>
        /// بدء خدمة النسخ الاحتياطي التلقائي
        /// </summary>
        public void Start()
        {
            try
            {
                // حساب الفترة الزمنية بالمللي ثانية
                int intervalMilliseconds = _settings.IntervalHours * 60 * 60 * 1000;

                // إنشاء مؤقت للنسخ الاحتياطي التلقائي
                _backupTimer = new Timer(async (state) => await PerformAutoBackupAsync(), null,
                    _settings.DelayStartup ? intervalMilliseconds : 0, // التأخير الأولي
                    intervalMilliseconds); // الفترة الزمنية بين النسخ الاحتياطي

                LogMessage($"تم بدء خدمة النسخ الاحتياطي التلقائي. الفترة الزمنية: {_settings.IntervalHours} ساعة");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء بدء خدمة النسخ الاحتياطي التلقائي: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إيقاف خدمة النسخ الاحتياطي التلقائي
        /// </summary>
        public void Stop()
        {
            try
            {
                // إيقاف المؤقت
                _backupTimer?.Dispose();
                _backupTimer = null;

                LogMessage("تم إيقاف خدمة النسخ الاحتياطي التلقائي");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء إيقاف خدمة النسخ الاحتياطي التلقائي: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تنفيذ النسخ الاحتياطي التلقائي
        /// </summary>
        private async Task PerformAutoBackupAsync()
        {
            try
            {
                LogMessage("بدء عملية النسخ الاحتياطي التلقائي...");

                // إنشاء نسخة احتياطية
                string backupPath = await _maintenanceRepository.CreateBackupAsync();

                // تسجيل العملية في قاعدة البيانات
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "نسخ احتياطي تلقائي",
                    $"تم إنشاء نسخة احتياطية تلقائية في المسار: {backupPath}");

                // حذف النسخ الاحتياطية القديمة إذا تم تمكين هذه الميزة
                if (_settings.DeleteOldBackups)
                {
                    await DeleteOldBackupsAsync();
                }

                LogMessage($"تم إنشاء نسخة احتياطية تلقائية بنجاح في المسار: {backupPath}");

                // إرسال إشعار بالبريد الإلكتروني إذا تم تمكين هذه الميزة
                if (_settings.SendEmailNotification)
                {
                    await SendBackupNotificationEmailAsync(backupPath);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء عملية النسخ الاحتياطي التلقائي: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف النسخ الاحتياطية القديمة
        /// </summary>
        private async Task DeleteOldBackupsAsync()
        {
            try
            {
                // الحصول على قائمة النسخ الاحتياطية
                List<string> backupFiles = _maintenanceRepository.GetAvailableBackups();

                // الاحتفاظ فقط بالعدد المحدد من النسخ الاحتياطية
                if (backupFiles.Count > _settings.KeepBackupsCount)
                {
                    // ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء (الأقدم أولاً)
                    backupFiles.Sort((a, b) => File.GetCreationTime(a).CompareTo(File.GetCreationTime(b)));

                    // حذف النسخ الاحتياطية القديمة
                    int filesToDelete = backupFiles.Count - _settings.KeepBackupsCount;
                    for (int i = 0; i < filesToDelete; i++)
                    {
                        string fileToDelete = backupFiles[i];
                        File.Delete(fileToDelete);

                        // تسجيل العملية
                        await _maintenanceRepository.LogMaintenanceOperationAsync(
                            _developerId,
                            "حذف نسخة احتياطية قديمة",
                            $"تم حذف النسخة الاحتياطية القديمة: {Path.GetFileName(fileToDelete)}");

                        LogMessage($"تم حذف النسخة الاحتياطية القديمة: {fileToDelete}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء حذف النسخ الاحتياطية القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// إرسال إشعار بالبريد الإلكتروني عن النسخ الاحتياطي
        /// </summary>
        private async Task SendBackupNotificationEmailAsync(string backupPath)
        {
            try
            {
                // هذه مجرد محاكاة لإرسال البريد الإلكتروني
                // في التطبيق الفعلي، يجب استخدام مكتبة لإرسال البريد الإلكتروني
                LogMessage($"تم إرسال إشعار بالبريد الإلكتروني عن النسخة الاحتياطية: {backupPath}");

                // تسجيل العملية
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "إرسال إشعار بالبريد الإلكتروني",
                    $"تم إرسال إشعار بالبريد الإلكتروني عن النسخة الاحتياطية: {Path.GetFileName(backupPath)}");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء إرسال إشعار البريد الإلكتروني: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل رسالة في ملف السجل
        /// </summary>
        private void LogMessage(string message)
        {
            try
            {
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
                File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
            }
            catch
            {
                // تجاهل أي خطأ أثناء تسجيل الرسالة
            }
        }
    }

    /// <summary>
    /// إعدادات النسخ الاحتياطي التلقائي
    /// </summary>
    public class BackupSettings
    {
        /// <summary>
        /// الفترة الزمنية بين النسخ الاحتياطي (بالساعات)
        /// </summary>
        public int IntervalHours { get; set; } = 24; // افتراضيًا كل 24 ساعة

        /// <summary>
        /// تأخير بدء النسخ الاحتياطي الأول
        /// </summary>
        public bool DelayStartup { get; set; } = true;

        /// <summary>
        /// حذف النسخ الاحتياطية القديمة
        /// </summary>
        public bool DeleteOldBackups { get; set; } = true;

        /// <summary>
        /// عدد النسخ الاحتياطية للاحتفاظ بها
        /// </summary>
        public int KeepBackupsCount { get; set; } = 7; // افتراضيًا الاحتفاظ بآخر 7 نسخ

        /// <summary>
        /// إرسال إشعار بالبريد الإلكتروني بعد النسخ الاحتياطي
        /// </summary>
        public bool SendEmailNotification { get; set; } = false;

        /// <summary>
        /// عنوان البريد الإلكتروني للإشعارات
        /// </summary>
        public string NotificationEmail { get; set; } = "";

        /// <summary>
        /// مجلد النسخ الاحتياطي
        /// </summary>
        public string BackupDirectory { get; set; } = "";

        /// <summary>
        /// ضغط ملفات النسخ الاحتياطي
        /// </summary>
        public bool CompressBackups { get; set; } = true;

        /// <summary>
        /// إنشاء نسخة احتياطية عند بدء التشغيل
        /// </summary>
        public bool BackupOnStartup { get; set; } = false;

        /// <summary>
        /// إنشاء نسخة احتياطية عند إغلاق البرنامج
        /// </summary>
        public bool BackupOnShutdown { get; set; } = true;
    }
}
