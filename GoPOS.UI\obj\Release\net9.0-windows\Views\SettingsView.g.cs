﻿#pragma checksum "..\..\..\..\Views\SettingsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3331EC3B49F09E2BB180899B8A78BBC6901AF412"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// SettingsView
    /// </summary>
    public partial class SettingsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StoreNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StoreAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StorePhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StoreEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StoreTaxNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaxRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowTaxCheckBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReceiptMessageTextBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveGeneralSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetGeneralSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DatabaseTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ServerNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DatabaseUserTextBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox DatabasePasswordBox;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BackupFrequencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseBackupPathButton;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestConnectionButton;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackupNowButton;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreBackupButton;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveDatabaseSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetDatabaseSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ReceiptPrinterComboBox;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ReportPrinterComboBox;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaperSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoPrintCheckBox;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CopiesComboBox;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PrintLogoCheckBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image LogoImage;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectLogoButton;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestPrintButton;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SavePrintSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetPrintSettingsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/settingsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SettingsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StoreNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.StoreAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.StorePhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.StoreEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.StoreTaxNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.FontSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.CurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.TaxRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.ShowTaxCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.ReceiptMessageTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.SaveGeneralSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 128 "..\..\..\..\Views\SettingsView.xaml"
            this.SaveGeneralSettingsButton.Click += new System.Windows.RoutedEventHandler(this.SaveGeneralSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ResetGeneralSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\..\Views\SettingsView.xaml"
            this.ResetGeneralSettingsButton.Click += new System.Windows.RoutedEventHandler(this.ResetGeneralSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.DatabaseTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.ServerNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.DatabaseNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.DatabaseUserTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.DatabasePasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 20:
            this.AutoBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.BackupFrequencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.BackupPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.BrowseBackupPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\..\Views\SettingsView.xaml"
            this.BrowseBackupPathButton.Click += new System.Windows.RoutedEventHandler(this.BrowseBackupPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.TestConnectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\..\Views\SettingsView.xaml"
            this.TestConnectionButton.Click += new System.Windows.RoutedEventHandler(this.TestConnectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.BackupNowButton = ((System.Windows.Controls.Button)(target));
            
            #line 209 "..\..\..\..\Views\SettingsView.xaml"
            this.BackupNowButton.Click += new System.Windows.RoutedEventHandler(this.BackupNowButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.RestoreBackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 210 "..\..\..\..\Views\SettingsView.xaml"
            this.RestoreBackupButton.Click += new System.Windows.RoutedEventHandler(this.RestoreBackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SaveDatabaseSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\Views\SettingsView.xaml"
            this.SaveDatabaseSettingsButton.Click += new System.Windows.RoutedEventHandler(this.SaveDatabaseSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.ResetDatabaseSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 215 "..\..\..\..\Views\SettingsView.xaml"
            this.ResetDatabaseSettingsButton.Click += new System.Windows.RoutedEventHandler(this.ResetDatabaseSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.ReceiptPrinterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 30:
            this.ReportPrinterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 31:
            this.PaperSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 32:
            this.AutoPrintCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.CopiesComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 34:
            this.PrintLogoCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.LogoImage = ((System.Windows.Controls.Image)(target));
            return;
            case 36:
            this.SelectLogoButton = ((System.Windows.Controls.Button)(target));
            
            #line 287 "..\..\..\..\Views\SettingsView.xaml"
            this.SelectLogoButton.Click += new System.Windows.RoutedEventHandler(this.SelectLogoButton_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.TestPrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 293 "..\..\..\..\Views\SettingsView.xaml"
            this.TestPrintButton.Click += new System.Windows.RoutedEventHandler(this.TestPrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.SavePrintSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 297 "..\..\..\..\Views\SettingsView.xaml"
            this.SavePrintSettingsButton.Click += new System.Windows.RoutedEventHandler(this.SavePrintSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.ResetPrintSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 298 "..\..\..\..\Views\SettingsView.xaml"
            this.ResetPrintSettingsButton.Click += new System.Windows.RoutedEventHandler(this.ResetPrintSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

