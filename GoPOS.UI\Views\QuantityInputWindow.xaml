<Window x:Class="GoPOS.UI.Views.QuantityInputWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إدخال الكمية" Height="200" Width="400"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إدخال الكمية الفعلية" FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>

        <!-- الكمية في النظام -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5">
            <TextBlock Text="الكمية في النظام:" Width="120"/>
            <TextBlock x:Name="SystemQuantityTextBlock" Text="0" FontWeight="Bold"/>
        </StackPanel>

        <!-- الكمية الفعلية -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,5">
            <TextBlock Text="الكمية الفعلية:" Width="120" VerticalAlignment="Center"/>
            <TextBox x:Name="ActualQuantityTextBox" Width="100" Height="30" PreviewTextInput="NumberValidationTextBox" TextChanged="ActualQuantityTextBox_TextChanged"/>
        </StackPanel>

        <!-- الفرق -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,5">
            <TextBlock Text="الفرق:" Width="120"/>
            <TextBlock x:Name="DifferenceTextBlock" Text="0" FontWeight="Bold"/>
        </StackPanel>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
            <Button x:Name="OkButton" Content="موافق" Width="100" Height="30" Margin="0,0,10,0" Click="OkButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="30" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
