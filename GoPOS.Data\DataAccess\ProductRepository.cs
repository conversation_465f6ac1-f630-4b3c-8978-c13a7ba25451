using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع المنتجات
    /// </summary>
    public class ProductRepository : IRepository<Product>
    {
        private readonly DatabaseContext _dbContext;

        /// <summary>
        /// إنشاء مستودع المنتجات
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public ProductRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        /// <summary>
        /// الحصول على جميع المنتجات
        /// </summary>
        /// <returns>قائمة بجميع المنتجات</returns>
        public async Task<IEnumerable<Product>> GetAllAsync()
        {
            string query = @"
                SELECT p.*, c.Name AS CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                ORDER BY p.Name";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return ConvertDataTableToProducts(dataTable);
        }

        /// <summary>
        /// الحصول على منتج بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المنتج</param>
        /// <returns>المنتج إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<Product> GetByIdAsync(int id)
        {
            string query = @"
                SELECT p.*, c.Name AS CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToProducts(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// إضافة منتج جديد
        /// </summary>
        /// <param name="entity">المنتج المراد إضافته</param>
        /// <returns>معرف المنتج المضاف</returns>
        public async Task<int> AddAsync(Product entity)
        {
            // التحقق من عدم وجود منتج بنفس الباركود
            if (!string.IsNullOrEmpty(entity.Barcode))
            {
                string checkQuery = @"
                    SELECT COUNT(*)
                    FROM Products
                    WHERE Barcode = @Barcode";

                SqlParameter[] checkParameters = new SqlParameter[]
                {
                    new SqlParameter("@Barcode", entity.Barcode)
                };

                object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
                int productCount = Convert.ToInt32(result);

                if (productCount > 0)
                {
                    throw new InvalidOperationException("الباركود موجود بالفعل");
                }
            }

            string query = @"
                INSERT INTO Products (Name, Description, Barcode, CategoryId, 
                    Price, CostPrice, Quantity, MinQuantity, IsActive, ImagePath)
                VALUES (@Name, @Description, @Barcode, @CategoryId, 
                    @Price, @CostPrice, @Quantity, @MinQuantity, @IsActive, @ImagePath);
                SELECT SCOPE_IDENTITY();";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Name", entity.Name),
                new SqlParameter("@Description", (object)entity.Description ?? DBNull.Value),
                new SqlParameter("@Barcode", (object)entity.Barcode ?? DBNull.Value),
                new SqlParameter("@CategoryId", (object)entity.CategoryId ?? DBNull.Value),
                new SqlParameter("@Price", entity.Price),
                new SqlParameter("@CostPrice", entity.CostPrice),
                new SqlParameter("@Quantity", entity.Quantity),
                new SqlParameter("@MinQuantity", entity.MinQuantity),
                new SqlParameter("@IsActive", entity.IsActive),
                new SqlParameter("@ImagePath", (object)entity.ImagePath ?? DBNull.Value)
            };

            object insertResult = await _dbContext.ExecuteScalarAsync(query, parameters);
            return Convert.ToInt32(insertResult);
        }

        /// <summary>
        /// تحديث منتج موجود
        /// </summary>
        /// <param name="entity">المنتج المراد تحديثه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateAsync(Product entity)
        {
            // التحقق من عدم وجود منتج آخر بنفس الباركود
            if (!string.IsNullOrEmpty(entity.Barcode))
            {
                string checkQuery = @"
                    SELECT COUNT(*)
                    FROM Products
                    WHERE Barcode = @Barcode AND Id <> @Id";

                SqlParameter[] checkParameters = new SqlParameter[]
                {
                    new SqlParameter("@Barcode", entity.Barcode),
                    new SqlParameter("@Id", entity.Id)
                };

                object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
                int productCount = Convert.ToInt32(result);

                if (productCount > 0)
                {
                    throw new InvalidOperationException("الباركود موجود بالفعل");
                }
            }

            string query = @"
                UPDATE Products
                SET Name = @Name,
                    Description = @Description,
                    Barcode = @Barcode,
                    CategoryId = @CategoryId,
                    Price = @Price,
                    CostPrice = @CostPrice,
                    Quantity = @Quantity,
                    MinQuantity = @MinQuantity,
                    IsActive = @IsActive,
                    ImagePath = @ImagePath
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", entity.Id),
                new SqlParameter("@Name", entity.Name),
                new SqlParameter("@Description", (object)entity.Description ?? DBNull.Value),
                new SqlParameter("@Barcode", (object)entity.Barcode ?? DBNull.Value),
                new SqlParameter("@CategoryId", (object)entity.CategoryId ?? DBNull.Value),
                new SqlParameter("@Price", entity.Price),
                new SqlParameter("@CostPrice", entity.CostPrice),
                new SqlParameter("@Quantity", entity.Quantity),
                new SqlParameter("@MinQuantity", entity.MinQuantity),
                new SqlParameter("@IsActive", entity.IsActive),
                new SqlParameter("@ImagePath", (object)entity.ImagePath ?? DBNull.Value)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// حذف منتج بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المنتج المراد حذفه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> DeleteAsync(int id)
        {
            // التحقق من عدم وجود فواتير مرتبطة بالمنتج
            string checkQuery = @"
                SELECT COUNT(*)
                FROM InvoiceItems
                WHERE ProductId = @Id";

            SqlParameter[] checkParameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
            int invoiceItemCount = Convert.ToInt32(result);

            if (invoiceItemCount > 0)
            {
                throw new InvalidOperationException("لا يمكن حذف المنتج لأنه مرتبط بفواتير");
            }

            string query = "DELETE FROM Products WHERE Id = @Id";
            return await _dbContext.ExecuteNonQueryAsync(query, checkParameters);
        }

        /// <summary>
        /// البحث عن منتجات بناءً على معايير معينة
        /// </summary>
        /// <param name="predicate">دالة التصفية</param>
        /// <returns>قائمة بالمنتجات التي تطابق معايير البحث</returns>
        public async Task<IEnumerable<Product>> FindAsync(Func<Product, bool> predicate)
        {
            var products = await GetAllAsync();
            return products.Where(predicate);
        }

        /// <summary>
        /// البحث عن منتجات بواسطة النص
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة بالمنتجات التي تطابق نص البحث</returns>
        public async Task<IEnumerable<Product>> SearchAsync(string searchText)
        {
            string query = @"
                SELECT p.*, c.Name AS CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Name LIKE @SearchText
                OR p.Barcode LIKE @SearchText
                OR p.Description LIKE @SearchText
                ORDER BY p.Name";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@SearchText", $"%{searchText}%")
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToProducts(dataTable);
        }

        /// <summary>
        /// الحصول على منتجات بواسطة الفئة
        /// </summary>
        /// <param name="categoryId">معرف الفئة</param>
        /// <returns>قائمة بالمنتجات في الفئة المحددة</returns>
        public async Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId)
        {
            string query = @"
                SELECT p.*, c.Name AS CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.CategoryId = @CategoryId
                ORDER BY p.Name";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@CategoryId", categoryId)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToProducts(dataTable);
        }

        /// <summary>
        /// الحصول على منتج بواسطة الباركود
        /// </summary>
        /// <param name="barcode">الباركود</param>
        /// <returns>المنتج إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<Product> GetByBarcodeAsync(string barcode)
        {
            string query = @"
                SELECT p.*, c.Name AS CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Barcode = @Barcode";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Barcode", barcode)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToProducts(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// تحديث كمية المنتج
        /// </summary>
        /// <param name="productId">معرف المنتج</param>
        /// <param name="quantity">الكمية الجديدة</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateQuantityAsync(int productId, decimal quantity)
        {
            string query = @"
                UPDATE Products
                SET Quantity = @Quantity
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", productId),
                new SqlParameter("@Quantity", quantity)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// الحصول على المنتجات التي تحتاج إلى إعادة الطلب
        /// </summary>
        /// <returns>قائمة بالمنتجات التي تحتاج إلى إعادة الطلب</returns>
        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            string query = @"
                SELECT p.*, c.Name AS CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Quantity <= p.MinQuantity AND p.IsActive = 1
                ORDER BY p.Quantity, p.Name";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return ConvertDataTableToProducts(dataTable);
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة منتجات
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة المنتجات</returns>
        private IEnumerable<Product> ConvertDataTableToProducts(DataTable dataTable)
        {
            List<Product> products = new List<Product>();

            foreach (DataRow row in dataTable.Rows)
            {
                Product product = new Product
                {
                    Id = Convert.ToInt32(row["Id"]),
                    Name = row["Name"].ToString(),
                    Price = Convert.ToDecimal(row["Price"]),
                    CostPrice = Convert.ToDecimal(row["CostPrice"]),
                    Quantity = Convert.ToDecimal(row["Quantity"]),
                    MinQuantity = Convert.ToDecimal(row["MinQuantity"]),
                    IsActive = Convert.ToBoolean(row["IsActive"])
                };

                if (row["Description"] != DBNull.Value)
                {
                    product.Description = row["Description"].ToString();
                }

                if (row["Barcode"] != DBNull.Value)
                {
                    product.Barcode = row["Barcode"].ToString();
                }

                if (row["CategoryId"] != DBNull.Value)
                {
                    product.CategoryId = Convert.ToInt32(row["CategoryId"]);
                    product.CategoryName = row["CategoryName"]?.ToString();
                }

                if (row["ImagePath"] != DBNull.Value)
                {
                    product.ImagePath = row["ImagePath"].ToString();
                }

                products.Add(product);
            }

            return products;
        }
    }
}
