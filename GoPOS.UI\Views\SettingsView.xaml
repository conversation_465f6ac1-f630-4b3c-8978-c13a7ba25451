<UserControl x:Class="GoPOS.UI.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إعدادات النظام" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- محتوى الإعدادات -->
        <TabControl Grid.Row="1">
            <!-- إعدادات عامة -->
            <TabItem Header="إعدادات عامة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="معلومات المتجر" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المتجر:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="StoreNameTextBox" Grid.Row="0" Grid.Column="1" Height="30" Margin="0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="العنوان:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="StoreAddressTextBox" Grid.Row="1" Grid.Column="1" Height="60" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="رقم الهاتف:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="StorePhoneTextBox" Grid.Row="2" Grid.Column="1" Height="30" Margin="0,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="البريد الإلكتروني:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="StoreEmailTextBox" Grid.Row="3" Grid.Column="1" Height="30" Margin="0,5"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="الرقم الضريبي:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="StoreTaxNumberTextBox" Grid.Row="4" Grid.Column="1" Height="30" Margin="0,5"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="إعدادات العرض" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="اللغة:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="LanguageComboBox" Grid.Row="0" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="العربية" IsSelected="True"/>
                                    <ComboBoxItem Content="English"/>
                                </ComboBox>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="السمة:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="ThemeComboBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="فاتح" IsSelected="True"/>
                                    <ComboBoxItem Content="داكن"/>
                                </ComboBox>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="حجم الخط:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="FontSizeComboBox" Grid.Row="2" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="صغير"/>
                                    <ComboBoxItem Content="متوسط" IsSelected="True"/>
                                    <ComboBoxItem Content="كبير"/>
                                </ComboBox>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="إعدادات الفاتورة" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="عملة النظام:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="CurrencyComboBox" Grid.Row="0" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="ريال سعودي (ر.س)" IsSelected="True"/>
                                    <ComboBoxItem Content="دولار أمريكي ($)"/>
                                    <ComboBoxItem Content="يورو (€)"/>
                                </ComboBox>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="نسبة الضريبة:" VerticalAlignment="Center" Margin="0,5"/>
                                <Grid Grid.Row="1" Grid.Column="1" Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="TaxRateTextBox" Grid.Column="0" Height="30" Text="15"/>
                                    <TextBlock Grid.Column="1" Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </Grid>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="عرض الضريبة:" VerticalAlignment="Center" Margin="0,5"/>
                                <CheckBox x:Name="ShowTaxCheckBox" Grid.Row="2" Grid.Column="1" Content="عرض الضريبة في الفاتورة" IsChecked="True" VerticalAlignment="Center" Margin="0,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="رسالة الفاتورة:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="ReceiptMessageTextBox" Grid.Row="3" Grid.Column="1" Height="60" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,5" Text="شكراً لتسوقكم معنا"/>
                            </Grid>
                        </GroupBox>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
                            <Button x:Name="SaveGeneralSettingsButton" Content="حفظ الإعدادات" Width="150" Height="40" Click="SaveGeneralSettingsButton_Click"/>
                            <Button x:Name="ResetGeneralSettingsButton" Content="إعادة تعيين" Width="100" Height="40" Margin="10,0,0,0" Click="ResetGeneralSettingsButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات قاعدة البيانات -->
            <TabItem Header="قاعدة البيانات">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="اتصال قاعدة البيانات" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="نوع قاعدة البيانات:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="DatabaseTypeComboBox" Grid.Row="0" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="SQL Server" IsSelected="True"/>
                                    <ComboBoxItem Content="SQLite"/>
                                </ComboBox>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم الخادم:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="ServerNameTextBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,5" Text="localhost"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="اسم قاعدة البيانات:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="DatabaseNameTextBox" Grid.Row="2" Grid.Column="1" Height="30" Margin="0,5" Text="GoPOS"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="اسم المستخدم:" VerticalAlignment="Center" Margin="0,5"/>
                                <TextBox x:Name="DatabaseUserTextBox" Grid.Row="3" Grid.Column="1" Height="30" Margin="0,5" Text="sa"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="كلمة المرور:" VerticalAlignment="Center" Margin="0,5"/>
                                <PasswordBox x:Name="DatabasePasswordBox" Grid.Row="4" Grid.Column="1" Height="30" Margin="0,5"/>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="النسخ الاحتياطي" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="النسخ الاحتياطي التلقائي:" VerticalAlignment="Center" Margin="0,5"/>
                                <CheckBox x:Name="AutoBackupCheckBox" Grid.Row="0" Grid.Column="1" Content="تفعيل النسخ الاحتياطي التلقائي" IsChecked="True" VerticalAlignment="Center" Margin="0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="تكرار النسخ الاحتياطي:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="BackupFrequencyComboBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="يومي"/>
                                    <ComboBoxItem Content="أسبوعي" IsSelected="True"/>
                                    <ComboBoxItem Content="شهري"/>
                                </ComboBox>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="مسار النسخ الاحتياطي:" VerticalAlignment="Center" Margin="0,5"/>
                                <Grid Grid.Row="2" Grid.Column="1" Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="BackupPathTextBox" Grid.Column="0" Height="30" Text="C:\GoPOS\Backup"/>
                                    <Button x:Name="BrowseBackupPathButton" Grid.Column="1" Content="استعراض" Width="80" Height="30" Margin="5,0,0,0" Click="BrowseBackupPathButton_Click"/>
                                </Grid>
                            </Grid>
                        </GroupBox>

                        <StackPanel Orientation="Horizontal" Margin="0,10,0,20">
                            <Button x:Name="TestConnectionButton" Content="اختبار الاتصال" Width="150" Height="40" Click="TestConnectionButton_Click"/>
                            <Button x:Name="BackupNowButton" Content="نسخ احتياطي الآن" Width="150" Height="40" Margin="10,0,0,0" Click="BackupNowButton_Click"/>
                            <Button x:Name="RestoreBackupButton" Content="استعادة نسخة احتياطية" Width="150" Height="40" Margin="10,0,0,0" Click="RestoreBackupButton_Click"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
                            <Button x:Name="SaveDatabaseSettingsButton" Content="حفظ الإعدادات" Width="150" Height="40" Click="SaveDatabaseSettingsButton_Click"/>
                            <Button x:Name="ResetDatabaseSettingsButton" Content="إعادة تعيين" Width="100" Height="40" Margin="10,0,0,0" Click="ResetDatabaseSettingsButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات الطباعة -->
            <TabItem Header="الطباعة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <GroupBox Header="إعدادات الطابعة" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="طابعة الإيصالات:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="ReceiptPrinterComboBox" Grid.Row="0" Grid.Column="1" Height="30" Margin="0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="طابعة التقارير:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="ReportPrinterComboBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="حجم الورق:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="PaperSizeComboBox" Grid.Row="2" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="80mm" IsSelected="True"/>
                                    <ComboBoxItem Content="58mm"/>
                                    <ComboBoxItem Content="A4"/>
                                </ComboBox>
                            </Grid>
                        </GroupBox>

                        <GroupBox Header="خيارات الطباعة" Margin="0,0,0,20">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="طباعة تلقائية:" VerticalAlignment="Center" Margin="0,5"/>
                                <CheckBox x:Name="AutoPrintCheckBox" Grid.Row="0" Grid.Column="1" Content="طباعة الإيصال تلقائياً بعد كل عملية بيع" IsChecked="True" VerticalAlignment="Center" Margin="0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="عدد النسخ:" VerticalAlignment="Center" Margin="0,5"/>
                                <ComboBox x:Name="CopiesComboBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,5">
                                    <ComboBoxItem Content="1" IsSelected="True"/>
                                    <ComboBoxItem Content="2"/>
                                    <ComboBoxItem Content="3"/>
                                </ComboBox>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="طباعة الشعار:" VerticalAlignment="Center" Margin="0,5"/>
                                <CheckBox x:Name="PrintLogoCheckBox" Grid.Row="2" Grid.Column="1" Content="طباعة شعار المتجر على الإيصال" IsChecked="True" VerticalAlignment="Center" Margin="0,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="شعار المتجر:" VerticalAlignment="Center" Margin="0,5"/>
                                <Grid Grid.Row="3" Grid.Column="1" Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" Height="100">
                                        <Image x:Name="LogoImage" Stretch="Uniform" Margin="5"/>
                                    </Border>
                                    <Button x:Name="SelectLogoButton" Grid.Column="1" Content="اختيار شعار" Width="80" Height="30" Margin="5,0,0,0" VerticalAlignment="Top" Click="SelectLogoButton_Click"/>
                                </Grid>
                            </Grid>
                        </GroupBox>

                        <StackPanel Orientation="Horizontal" Margin="0,10,0,20">
                            <Button x:Name="TestPrintButton" Content="اختبار الطباعة" Width="150" Height="40" Click="TestPrintButton_Click"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
                            <Button x:Name="SavePrintSettingsButton" Content="حفظ الإعدادات" Width="150" Height="40" Click="SavePrintSettingsButton_Click"/>
                            <Button x:Name="ResetPrintSettingsButton" Content="إعادة تعيين" Width="100" Height="40" Margin="10,0,0,0" Click="ResetPrintSettingsButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
