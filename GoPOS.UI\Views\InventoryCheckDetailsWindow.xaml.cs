using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InventoryCheckDetailsWindow.xaml
    /// </summary>
    public partial class InventoryCheckDetailsWindow : Window
    {
        private InventoryCheckViewModel _inventoryCheck;
        private ObservableCollection<InventoryCheckItemViewModel> _checkItems;
        private InventoryCheckItemViewModel _selectedItem;
        private bool _isViewMode;

        public bool IsReadOnly => _isViewMode;
        public bool IsEditable => !_isViewMode;
        public Visibility SaveButtonVisibility => _isViewMode ? Visibility.Collapsed : Visibility.Visible;
        public Visibility ProductsToolbarVisibility => _isViewMode ? Visibility.Collapsed : Visibility.Visible;

        public InventoryCheckDetailsWindow(InventoryCheckViewModel? inventoryCheck = null, bool isViewMode = false)
        {
            InitializeComponent();
            DataContext = this;

            _isViewMode = isViewMode;
            _inventoryCheck = inventoryCheck;

            if (_inventoryCheck == null)
            {
                // إنشاء جرد جديد
                _inventoryCheck = new InventoryCheckViewModel
                {
                    CheckNumber = GenerateCheckNumber(),
                    CheckDate = DateTime.Now,
                    StatusId = 0, // مسودة
                    WarehouseId = 1, // المخزن الرئيسي
                    WarehouseName = "المخزن الرئيسي",
                    UserName = "مدير النظام",
                    StatusText = "مسودة",
                    StatusColor = new SolidColorBrush(Colors.Gray)
                };
            }

            LoadInventoryCheckData();
            LoadCheckItems();
        }

        private string GenerateCheckNumber()
        {
            // توليد رقم جرد جديد (سيتم استبداله بتوليد من قاعدة البيانات)
            return $"INV-{DateTime.Now.Year}-{new Random().Next(1000, 9999)}";
        }

        private void LoadInventoryCheckData()
        {
            // تحميل بيانات الجرد
            CheckNumberTextBox.Text = _inventoryCheck.CheckNumber;
            CheckDatePicker.SelectedDate = _inventoryCheck.CheckDate;
            WarehouseComboBox.SelectedIndex = _inventoryCheck.WarehouseId - 1;
            StatusComboBox.SelectedIndex = _inventoryCheck.StatusId;
            NotesTextBox.Text = _inventoryCheck.Notes;
        }

        private void LoadCheckItems()
        {
            // تحميل عناصر الجرد (سيتم استبدالها بقراءة من قاعدة البيانات)
            _checkItems = new ObservableCollection<InventoryCheckItemViewModel>();
            Random random = new Random();

            // إذا كان جرد جديد، لا نضيف أي عناصر
            if (_inventoryCheck.Id == 0)
            {
                ProductsDataGrid.ItemsSource = _checkItems;
                return;
            }

            // بيانات تجريبية
            for (int i = 1; i <= 20; i++)
            {
                int categoryIndex = random.Next(0, 3);
                string productName = GetRandomProductName(categoryIndex);

                int systemQuantity = random.Next(10, 100);
                int? actualQuantity = null;
                int? difference = null;
                bool isChecked = false;
                SolidColorBrush differenceColor = Brushes.Black;

                // إذا كان الجرد مكتمل أو معتمد، نضيف قيم فعلية
                if (_inventoryCheck.StatusId >= 2)
                {
                    actualQuantity = systemQuantity + random.Next(-10, 11); // -10 إلى +10
                    difference = actualQuantity - systemQuantity;
                    isChecked = true;

                    if (difference < 0)
                        differenceColor = Brushes.Red;
                    else if (difference > 0)
                        differenceColor = Brushes.Green;
                }

                _checkItems.Add(new InventoryCheckItemViewModel
                {
                    Id = i,
                    Barcode = $"100{i:D4}",
                    Name = productName,
                    SystemQuantity = systemQuantity,
                    ActualQuantity = actualQuantity,
                    Difference = difference,
                    IsChecked = isChecked,
                    Notes = difference != null && difference != 0 ? "يرجى التحقق من الكمية" : "",
                    DifferenceColor = differenceColor
                });
            }

            ProductsDataGrid.ItemsSource = _checkItems;
        }

        private string GetRandomProductName(int categoryIndex)
        {
            string[] drinks = { "قهوة", "شاي", "عصير برتقال", "عصير تفاح", "ماء معدني", "مشروب غازي", "عصير مانجو" };
            string[] fastFood = { "برجر", "بيتزا", "ساندويتش", "دجاج مقلي", "بطاطس", "ناجتس", "شاورما" };
            string[] desserts = { "كيك", "آيس كريم", "كوكيز", "دونات", "تشيز كيك", "بودينج", "كنافة" };

            Random random = new Random();

            if (categoryIndex == 0)
                return drinks[random.Next(drinks.Length)];
            else if (categoryIndex == 1)
                return fastFood[random.Next(fastFood.Length)];
            else
                return desserts[random.Next(desserts.Length)];
        }

        private void ProductsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedItem = ProductsDataGrid.SelectedItem as InventoryCheckItemViewModel;
            RemoveProductButton.IsEnabled = _selectedItem != null;
        }

        private void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إضافة منتج
            ProductSelectionWindow selectionWindow = new ProductSelectionWindow();
            selectionWindow.Owner = this;

            if (selectionWindow.ShowDialog() == true)
            {
                // إضافة المنتج المحدد إلى قائمة الجرد
                var selectedProduct = selectionWindow.SelectedProduct;

                // التحقق من عدم وجود المنتج مسبقاً في القائمة
                foreach (var item in _checkItems)
                {
                    if (item.Barcode == selectedProduct.Barcode)
                    {
                        MessageBox.Show("المنتج موجود بالفعل في قائمة الجرد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                _checkItems.Add(new InventoryCheckItemViewModel
                {
                    Id = _checkItems.Count + 1,
                    Barcode = selectedProduct.Barcode,
                    Name = selectedProduct.Name,
                    SystemQuantity = selectedProduct.Quantity,
                    ActualQuantity = null,
                    Difference = null,
                    IsChecked = false,
                    Notes = "",
                    DifferenceColor = Brushes.Black
                });
            }
        }

        private void RemoveProductButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedItem == null) return;

            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إزالة المنتج المحدد من قائمة الجرد؟", "تأكيد الإزالة", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _checkItems.Remove(_selectedItem);
                _selectedItem = null;
                RemoveProductButton.IsEnabled = false;
            }
        }

        private void ImportProductsButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح مربع حوار اختيار ملف
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "ملفات Excel (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv",
                Title = "استيراد المنتجات"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                // محاكاة استيراد المنتجات
                Random random = new Random();
                int importCount = random.Next(5, 20);

                for (int i = 1; i <= importCount; i++)
                {
                    int categoryIndex = random.Next(0, 3);
                    string productName = GetRandomProductName(categoryIndex);

                    _checkItems.Add(new InventoryCheckItemViewModel
                    {
                        Id = _checkItems.Count + 1,
                        Barcode = $"200{i:D4}",
                        Name = productName,
                        SystemQuantity = random.Next(10, 100),
                        ActualQuantity = null,
                        Difference = null,
                        IsChecked = false,
                        Notes = "",
                        DifferenceColor = Brushes.Black
                    });
                }

                MessageBox.Show($"تم استيراد {importCount} منتج بنجاح", "تم الاستيراد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة الإدخال
            if (string.IsNullOrWhiteSpace(CheckNumberTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال رقم الجرد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                CheckNumberTextBox.Focus();
                return;
            }

            if (!CheckDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("الرجاء اختيار تاريخ الجرد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                CheckDatePicker.Focus();
                return;
            }

            if (_checkItems.Count == 0)
            {
                MessageBoxResult result = MessageBox.Show("قائمة المنتجات فارغة. هل ترغب في حفظ الجرد بدون منتجات؟", "تنبيه", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            // حفظ بيانات الجرد (سيتم استبداله بالحفظ في قاعدة البيانات)
            _inventoryCheck.CheckNumber = CheckNumberTextBox.Text;
            _inventoryCheck.CheckDate = CheckDatePicker.SelectedDate.Value;
            _inventoryCheck.WarehouseId = WarehouseComboBox.SelectedIndex + 1;
            _inventoryCheck.StatusId = StatusComboBox.SelectedIndex;
            _inventoryCheck.Notes = NotesTextBox.Text;

            MessageBox.Show("تم حفظ بيانات الجرد بنجاح", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            if (!_isViewMode)
            {
                MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إغلاق النافذة بدون حفظ التغييرات؟", "تأكيد الإغلاق", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            DialogResult = false;
            Close();
        }
    }

    // فئة عرض بيانات عنصر الجرد
    public class InventoryCheckItemViewModel
    {
        public int Id { get; set; }
        public required string Barcode { get; set; }
        public required string Name { get; set; }
        public int SystemQuantity { get; set; }
        public int? ActualQuantity { get; set; }
        public int? Difference { get; set; }
        public bool IsChecked { get; set; }
        public string Notes { get; set; } = string.Empty;
        public required SolidColorBrush DifferenceColor { get; set; }
    }
}
