using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// واجهة المستودع العامة
    /// </summary>
    /// <typeparam name="T">نوع الكيان</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// الحصول على جميع العناصر
        /// </summary>
        /// <returns>قائمة بجميع العناصر</returns>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// الحصول على عنصر بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف العنصر</param>
        /// <returns>العنصر إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        Task<T> GetByIdAsync(int id);

        /// <summary>
        /// إضافة عنصر جديد
        /// </summary>
        /// <param name="entity">العنصر المراد إضافته</param>
        /// <returns>معرف العنصر المضاف</returns>
        Task<int> AddAsync(T entity);

        /// <summary>
        /// تحديث عنصر موجود
        /// </summary>
        /// <param name="entity">العنصر المراد تحديثه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        Task<int> UpdateAsync(T entity);

        /// <summary>
        /// حذف عنصر بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف العنصر المراد حذفه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        Task<int> DeleteAsync(int id);

        /// <summary>
        /// البحث عن عناصر بناءً على معايير معينة
        /// </summary>
        /// <param name="predicate">دالة التصفية</param>
        /// <returns>قائمة بالعناصر التي تطابق معايير البحث</returns>
        Task<IEnumerable<T>> FindAsync(Func<T, bool> predicate);
    }
}
