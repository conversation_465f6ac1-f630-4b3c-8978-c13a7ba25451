using System;

namespace GoPOS.Data.Models
{
    /// <summary>
    /// نوع المنصرف
    /// </summary>
    public enum ExpenseType
    {
        /// <summary>
        /// مصروفات عامة
        /// </summary>
        General = 0,
        
        /// <summary>
        /// رواتب
        /// </summary>
        Salary = 1,
        
        /// <summary>
        /// إيجار
        /// </summary>
        Rent = 2,
        
        /// <summary>
        /// مرافق
        /// </summary>
        Utilities = 3,
        
        /// <summary>
        /// مشتريات
        /// </summary>
        Purchases = 4,
        
        /// <summary>
        /// أخرى
        /// </summary>
        Other = 5
    }

    /// <summary>
    /// نموذج المنصرف
    /// </summary>
    public class Expense
    {
        /// <summary>
        /// معرف المنصرف
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// تاريخ المنصرف
        /// </summary>
        public DateTime Date { get; set; }
        
        /// <summary>
        /// مبلغ المنصرف
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// وصف المنصرف
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// نوع المنصرف
        /// </summary>
        public ExpenseType Type { get; set; }
        
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// هل تم تأكيد المنصرف
        /// </summary>
        public bool IsConfirmed { get; set; }
        
        /// <summary>
        /// المستفيد
        /// </summary>
        public string Beneficiary { get; set; }
        
        /// <summary>
        /// رقم المستند
        /// </summary>
        public string DocumentNumber { get; set; }
    }
}
