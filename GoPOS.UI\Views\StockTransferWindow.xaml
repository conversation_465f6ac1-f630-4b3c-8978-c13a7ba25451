<Window x:Class="GoPOS.UI.Views.StockTransferWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="نقل المخزون" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="نقل المخزون" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- نموذج نقل المخزون -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="150"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- المنتج -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="المنتج:" VerticalAlignment="Center" Margin="0,5"/>
            <Grid Grid.Row="0" Grid.Column="1" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <ComboBox x:Name="ProductComboBox" Grid.Column="0" Height="30" DisplayMemberPath="Name" SelectionChanged="ProductComboBox_SelectionChanged"/>
                <Button x:Name="ScanBarcodeButton" Grid.Column="1" Content="مسح الباركود" Width="100" Height="30" Margin="5,0,0,0" Click="ScanBarcodeButton_Click"/>
            </Grid>

            <!-- الباركود -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="الباركود:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="BarcodeTextBox" Grid.Row="1" Grid.Column="1" Height="30" IsReadOnly="True" Margin="0,5"/>

            <!-- المخزن المصدر -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="المخزن المصدر:" VerticalAlignment="Center" Margin="0,5"/>
            <ComboBox x:Name="SourceWarehouseComboBox" Grid.Row="2" Grid.Column="1" Height="30" Margin="0,5" SelectionChanged="SourceWarehouseComboBox_SelectionChanged">
                <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                <ComboBoxItem Content="مخزن الفرع 1"/>
                <ComboBoxItem Content="مخزن الفرع 2"/>
            </ComboBox>

            <!-- الكمية في المخزن المصدر -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="الكمية المتاحة:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="SourceQuantityTextBox" Grid.Row="3" Grid.Column="1" Height="30" IsReadOnly="True" Margin="0,5"/>

            <!-- المخزن الهدف -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="المخزن الهدف:" VerticalAlignment="Center" Margin="0,5"/>
            <ComboBox x:Name="TargetWarehouseComboBox" Grid.Row="4" Grid.Column="1" Height="30" Margin="0,5" SelectionChanged="TargetWarehouseComboBox_SelectionChanged">
                <ComboBoxItem Content="مخزن الفرع 1" IsSelected="True"/>
                <ComboBoxItem Content="مخزن الفرع 2"/>
                <ComboBoxItem Content="مخزن الفرع 3"/>
            </ComboBox>

            <!-- الكمية في المخزن الهدف -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="الكمية الحالية:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="TargetQuantityTextBox" Grid.Row="5" Grid.Column="1" Height="30" IsReadOnly="True" Margin="0,5"/>

            <!-- الكمية المنقولة -->
            <TextBlock Grid.Row="6" Grid.Column="0" Text="الكمية المنقولة:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="TransferQuantityTextBox" Grid.Row="6" Grid.Column="1" Height="30" Margin="0,5" TextChanged="TransferQuantityTextBox_TextChanged"/>

            <!-- الكمية بعد النقل -->
            <TextBlock Grid.Row="7" Grid.Column="0" Text="الكمية بعد النقل:" VerticalAlignment="Center" Margin="0,5"/>
            <Grid Grid.Row="7" Grid.Column="1" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBox x:Name="SourceFinalQuantityTextBox" Grid.Column="0" Height="30" IsReadOnly="True"/>
                <TextBlock Grid.Column="1" Text="→" FontSize="20" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="10,0"/>
                <TextBox x:Name="TargetFinalQuantityTextBox" Grid.Column="2" Height="30" IsReadOnly="True"/>
            </Grid>

            <!-- ملاحظات -->
            <TextBlock Grid.Row="8" Grid.Column="0" Text="ملاحظات:" VerticalAlignment="Top" Margin="0,5"/>
            <TextBox x:Name="NotesTextBox" Grid.Row="8" Grid.Column="1" Height="80" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,5"/>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button x:Name="TransferButton" Content="نقل" Width="100" Height="40" Margin="0,0,10,0" Click="TransferButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="40" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
