using System;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج بيانات المنصرفات
    /// </summary>
    public class Expense
    {
        /// <summary>
        /// معرف المنصرف
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// تاريخ المنصرف
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// مبلغ المنصرف
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// وصف المنصرف
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// نوع المنصرف
        /// </summary>
        public ExpenseType Type { get; set; }

        /// <summary>
        /// معرف المستخدم الذي قام بإضافة المنصرف
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// اسم المستخدم الذي قام بإضافة المنصرف
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// هل تم تأكيد المنصرف
        /// </summary>
        public bool IsConfirmed { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// رقم المستند
        /// </summary>
        public string DocumentNumber { get; set; }

        /// <summary>
        /// المستفيد
        /// </summary>
        public string Beneficiary { get; set; }
    }

    /// <summary>
    /// أنواع المنصرفات
    /// </summary>
    public enum ExpenseType
    {
        /// <summary>
        /// مصاريف تشغيلية
        /// </summary>
        Operational = 1,

        /// <summary>
        /// رواتب
        /// </summary>
        Salary = 2,

        /// <summary>
        /// إيجار
        /// </summary>
        Rent = 3,

        /// <summary>
        /// مرافق
        /// </summary>
        Utilities = 4,

        /// <summary>
        /// مشتريات
        /// </summary>
        Purchases = 5,

        /// <summary>
        /// أخرى
        /// </summary>
        Other = 6
    }
}
