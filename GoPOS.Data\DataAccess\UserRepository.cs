using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;
using System.Security.Cryptography;
using System.Text;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع المستخدمين
    /// </summary>
    public class UserRepository : IRepository<User>
    {
        private readonly DatabaseContext _dbContext;

        /// <summary>
        /// إنشاء مستودع المستخدمين
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public UserRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <returns>قائمة بجميع المستخدمين</returns>
        public async Task<IEnumerable<User>> GetAllAsync()
        {
            string query = @"
                SELECT *
                FROM Users
                ORDER BY FullName";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return ConvertDataTableToUsers(dataTable);
        }

        /// <summary>
        /// الحصول على مستخدم بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المستخدم</param>
        /// <returns>المستخدم إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<User> GetByIdAsync(int id)
        {
            string query = @"
                SELECT *
                FROM Users
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToUsers(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        /// <param name="entity">المستخدم المراد إضافته</param>
        /// <returns>معرف المستخدم المضاف</returns>
        public async Task<int> AddAsync(User entity)
        {
            // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
            string checkQuery = @"
                SELECT COUNT(*)
                FROM Users
                WHERE Username = @Username";

            SqlParameter[] checkParameters = new SqlParameter[]
            {
                new SqlParameter("@Username", entity.Username)
            };

            object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
            int userCount = Convert.ToInt32(result);

            if (userCount > 0)
            {
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل");
            }

            // إضافة المستخدم
            string query = @"
                INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, IsActive, CreatedAt, LastLogin)
                VALUES (@Username, @PasswordHash, @FullName, @Email, @Role, @IsActive, @CreatedAt, @LastLogin);
                
                DECLARE @UserId INT = SCOPE_IDENTITY();
                
                INSERT INTO UserPermissions (UserId, CanManageSales, CanManageInvoices, CanReturnInvoices, 
                    CanManageProducts, CanManageCategories, CanManageInventory, CanManageUsers, 
                    CanViewReports, CanManageSettings)
                VALUES (@UserId, @CanManageSales, @CanManageInvoices, @CanReturnInvoices, 
                    @CanManageProducts, @CanManageCategories, @CanManageInventory, @CanManageUsers, 
                    @CanViewReports, @CanManageSettings);
                
                SELECT @UserId;";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Username", entity.Username),
                new SqlParameter("@PasswordHash", entity.PasswordHash),
                new SqlParameter("@FullName", entity.FullName),
                new SqlParameter("@Email", (object)entity.Email ?? DBNull.Value),
                new SqlParameter("@Role", (int)entity.Role),
                new SqlParameter("@IsActive", entity.IsActive),
                new SqlParameter("@CreatedAt", DateTime.Now),
                new SqlParameter("@LastLogin", (object)entity.LastLogin ?? DBNull.Value),
                new SqlParameter("@CanManageSales", entity.Permissions?.CanManageSales ?? true),
                new SqlParameter("@CanManageInvoices", entity.Permissions?.CanManageInvoices ?? true),
                new SqlParameter("@CanReturnInvoices", entity.Permissions?.CanReturnInvoices ?? true),
                new SqlParameter("@CanManageProducts", entity.Permissions?.CanManageProducts ?? true),
                new SqlParameter("@CanManageCategories", entity.Permissions?.CanManageCategories ?? true),
                new SqlParameter("@CanManageInventory", entity.Permissions?.CanManageInventory ?? true),
                new SqlParameter("@CanManageUsers", entity.Permissions?.CanManageUsers ?? true),
                new SqlParameter("@CanViewReports", entity.Permissions?.CanViewReports ?? true),
                new SqlParameter("@CanManageSettings", entity.Permissions?.CanManageSettings ?? true)
            };

            result = await _dbContext.ExecuteScalarAsync(query, parameters);
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// تحديث مستخدم موجود
        /// </summary>
        /// <param name="entity">المستخدم المراد تحديثه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateAsync(User entity)
        {
            // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
            string checkQuery = @"
                SELECT COUNT(*)
                FROM Users
                WHERE Username = @Username AND Id <> @Id";

            SqlParameter[] checkParameters = new SqlParameter[]
            {
                new SqlParameter("@Username", entity.Username),
                new SqlParameter("@Id", entity.Id)
            };

            object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
            int userCount = Convert.ToInt32(result);

            if (userCount > 0)
            {
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل");
            }

            // تحديث المستخدم
            string query = @"
                UPDATE Users
                SET Username = @Username,
                    FullName = @FullName,
                    Email = @Email,
                    Role = @Role,
                    IsActive = @IsActive
                WHERE Id = @Id;
                
                UPDATE UserPermissions
                SET CanManageSales = @CanManageSales,
                    CanManageInvoices = @CanManageInvoices,
                    CanReturnInvoices = @CanReturnInvoices,
                    CanManageProducts = @CanManageProducts,
                    CanManageCategories = @CanManageCategories,
                    CanManageInventory = @CanManageInventory,
                    CanManageUsers = @CanManageUsers,
                    CanViewReports = @CanViewReports,
                    CanManageSettings = @CanManageSettings
                WHERE UserId = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", entity.Id),
                new SqlParameter("@Username", entity.Username),
                new SqlParameter("@FullName", entity.FullName),
                new SqlParameter("@Email", (object)entity.Email ?? DBNull.Value),
                new SqlParameter("@Role", (int)entity.Role),
                new SqlParameter("@IsActive", entity.IsActive),
                new SqlParameter("@CanManageSales", entity.Permissions?.CanManageSales ?? true),
                new SqlParameter("@CanManageInvoices", entity.Permissions?.CanManageInvoices ?? true),
                new SqlParameter("@CanReturnInvoices", entity.Permissions?.CanReturnInvoices ?? true),
                new SqlParameter("@CanManageProducts", entity.Permissions?.CanManageProducts ?? true),
                new SqlParameter("@CanManageCategories", entity.Permissions?.CanManageCategories ?? true),
                new SqlParameter("@CanManageInventory", entity.Permissions?.CanManageInventory ?? true),
                new SqlParameter("@CanManageUsers", entity.Permissions?.CanManageUsers ?? true),
                new SqlParameter("@CanViewReports", entity.Permissions?.CanViewReports ?? true),
                new SqlParameter("@CanManageSettings", entity.Permissions?.CanManageSettings ?? true)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPasswordHash">تشفير كلمة المرور الجديدة</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> ChangePasswordAsync(int userId, string newPasswordHash)
        {
            string query = @"
                UPDATE Users
                SET PasswordHash = @PasswordHash
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", userId),
                new SqlParameter("@PasswordHash", newPasswordHash)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// حذف مستخدم بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المستخدم المراد حذفه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> DeleteAsync(int id)
        {
            // حذف صلاحيات المستخدم أولاً
            string deletePermissionsQuery = @"
                DELETE FROM UserPermissions
                WHERE UserId = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            await _dbContext.ExecuteNonQueryAsync(deletePermissionsQuery, parameters);

            // ثم حذف المستخدم
            string query = "DELETE FROM Users WHERE Id = @Id";
            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// البحث عن مستخدمين بناءً على معايير معينة
        /// </summary>
        /// <param name="predicate">دالة التصفية</param>
        /// <returns>قائمة بالمستخدمين الذين يطابقون معايير البحث</returns>
        public async Task<IEnumerable<User>> FindAsync(Func<User, bool> predicate)
        {
            var users = await GetAllAsync();
            return users.Where(predicate);
        }

        /// <summary>
        /// البحث عن مستخدم بواسطة اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>المستخدم إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<User> GetByUsernameAsync(string username)
        {
            string query = @"
                SELECT u.*, p.*
                FROM Users u
                LEFT JOIN UserPermissions p ON u.Id = p.UserId
                WHERE u.Username = @Username";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Username", username)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToUsers(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// تحديث آخر تسجيل دخول للمستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateLastLoginAsync(int userId)
        {
            string query = @"
                UPDATE Users
                SET LastLogin = @LastLogin
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", userId),
                new SqlParameter("@LastLogin", DateTime.Now)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة مستخدمين
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة المستخدمين</returns>
        private IEnumerable<User> ConvertDataTableToUsers(DataTable dataTable)
        {
            List<User> users = new List<User>();
            Dictionary<int, User> userDictionary = new Dictionary<int, User>();

            foreach (DataRow row in dataTable.Rows)
            {
                int userId = Convert.ToInt32(row["Id"]);

                if (!userDictionary.ContainsKey(userId))
                {
                    User user = new User
                    {
                        Id = userId,
                        Username = row["Username"].ToString(),
                        PasswordHash = row["PasswordHash"].ToString(),
                        FullName = row["FullName"].ToString(),
                        Role = (UserRole)Convert.ToInt32(row["Role"]),
                        IsActive = Convert.ToBoolean(row["IsActive"]),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"]),
                        Permissions = new UserPermissions()
                    };

                    if (row["Email"] != DBNull.Value)
                    {
                        user.Email = row["Email"].ToString();
                    }

                    if (row["LastLogin"] != DBNull.Value)
                    {
                        user.LastLogin = Convert.ToDateTime(row["LastLogin"]);
                    }

                    // إذا كان هناك صلاحيات في الجدول
                    if (dataTable.Columns.Contains("CanManageSales"))
                    {
                        user.Permissions.CanManageSales = Convert.ToBoolean(row["CanManageSales"]);
                        user.Permissions.CanManageInvoices = Convert.ToBoolean(row["CanManageInvoices"]);
                        user.Permissions.CanReturnInvoices = Convert.ToBoolean(row["CanReturnInvoices"]);
                        user.Permissions.CanManageProducts = Convert.ToBoolean(row["CanManageProducts"]);
                        user.Permissions.CanManageCategories = Convert.ToBoolean(row["CanManageCategories"]);
                        user.Permissions.CanManageInventory = Convert.ToBoolean(row["CanManageInventory"]);
                        user.Permissions.CanManageUsers = Convert.ToBoolean(row["CanManageUsers"]);
                        user.Permissions.CanViewReports = Convert.ToBoolean(row["CanViewReports"]);
                        user.Permissions.CanManageSettings = Convert.ToBoolean(row["CanManageSettings"]);
                    }

                    userDictionary.Add(userId, user);
                    users.Add(user);
                }
            }

            return users;
        }

        /// <summary>
        /// التحقق من صحة بيانات تسجيل الدخول
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>المستخدم إذا كانت بيانات تسجيل الدخول صحيحة، وإلا فإنه يرجع null</returns>
        public async Task<User> ValidateUserAsync(string username, string password)
        {
            // الحصول على المستخدم بواسطة اسم المستخدم
            User user = await GetByUsernameAsync(username);

            if (user == null || !user.IsActive)
            {
                return null;
            }

            // التحقق من صحة كلمة المرور
            if (VerifyPassword(password, user.PasswordHash))
            {
                // تحديث آخر تسجيل دخول
                await UpdateLastLoginAsync(user.Id);
                return user;
            }

            return null;
        }

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="passwordHash">تشفير كلمة المرور</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة، وإلا فإنه يرجع false</returns>
        private bool VerifyPassword(string password, string passwordHash)
        {
            // في الإصدار الأولي، نستخدم مقارنة بسيطة
            // في الإصدار النهائي، يجب استخدام تشفير آمن مثل BCrypt
            return HashPassword(password) == passwordHash;
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>تشفير كلمة المرور</returns>
        public string HashPassword(string password)
        {
            // في الإصدار الأولي، نستخدم تشفير بسيط
            // في الإصدار النهائي، يجب استخدام تشفير آمن مثل BCrypt
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] bytes = Encoding.UTF8.GetBytes(password);
                byte[] hash = sha256.ComputeHash(bytes);
                return Convert.ToBase64String(hash);
            }
        }
    }
}
