<UserControl x:Class="GoPOS.UI.Views.POSTerminalView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="350"/>
        </Grid.ColumnDefinitions>

        <!-- الجانب الأيمن - المنتجات والفئات -->
        <Grid Grid.Column="0" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط البحث -->
            <Grid Grid.Row="0" Margin="0,0,0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,0,5,0">
                    <Grid>
                        <TextBox x:Name="SearchTextBox"
                                 Height="40"
                                 FontSize="16"
                                 Padding="10,5"
                                 BorderThickness="0"
                                 KeyDown="SearchTextBox_KeyDown"
                                 VerticalContentAlignment="Center">
                            <TextBox.Resources>
                                <Style TargetType="TextBox">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="TextBox">
                                                <Border Background="{TemplateBinding Background}"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        BorderThickness="{TemplateBinding BorderThickness}">
                                                    <Grid>
                                                        <TextBlock x:Name="PlaceholderText"
                                                                   Text="ابحث عن منتج بالاسم أو الباركود..."
                                                                   Foreground="Gray"
                                                                   Padding="{TemplateBinding Padding}"
                                                                   VerticalAlignment="Center"
                                                                   Visibility="Collapsed"/>
                                                        <ScrollViewer x:Name="PART_ContentHost"
                                                                      Focusable="False"
                                                                      HorizontalScrollBarVisibility="Hidden"
                                                                      VerticalScrollBarVisibility="Hidden"/>
                                                    </Grid>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="Text" Value="">
                                                        <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                                                    </Trigger>
                                                    <Trigger Property="IsKeyboardFocused" Value="True">
                                                        <Setter TargetName="PlaceholderText" Property="Visibility" Value="Collapsed"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </TextBox.Resources>
                        </TextBox>
                    </Grid>
                </Border>

                <Button x:Name="SearchButton"
                        Grid.Column="1"
                        Height="40"
                        Width="80"
                        Click="SearchButton_Click"
                        Background="#2196F3"
                        Foreground="White"
                        FontWeight="SemiBold"
                        Margin="0,0,5,0">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔍" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="بحث" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button x:Name="BarcodeButton"
                        Grid.Column="2"
                        Height="40"
                        Width="40"
                        Click="BarcodeButton_Click"
                        ToolTip="مسح الباركود"
                        Background="#673AB7"
                        Foreground="White"
                        Margin="0,0,5,0">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <TextBlock Text="📷" FontSize="18"/>
                </Button>

                <Button x:Name="QuickBarcodeButton"
                        Grid.Column="3"
                        Height="40"
                        Width="40"
                        Click="QuickBarcodeButton_Click"
                        ToolTip="إدخال باركود يدوياً"
                        Background="#FF9800"
                        Foreground="White">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <TextBlock Text="📊" FontSize="18"/>
                </Button>
            </Grid>

            <!-- شريط الفئات -->
            <Border Grid.Row="1" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,0,0,10" Background="#F8F8F8">
                <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled" Padding="5">
                    <StackPanel x:Name="CategoriesPanel" Orientation="Horizontal">
                        <Button Content="الكل"
                                Width="100"
                                Height="40"
                                Margin="5"
                                Click="CategoryButton_Click"
                                Tag="0"
                                Background="#2196F3"
                                Foreground="White"
                                FontWeight="SemiBold">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="20"/>
                                </Style>
                            </Button.Resources>
                        </Button>
                        <!-- سيتم إضافة أزرار الفئات ديناميكياً -->
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- شبكة المنتجات -->
            <Border Grid.Row="2" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4">
                <Grid>
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <WrapPanel x:Name="ProductsPanel" Margin="10">
                            <!-- سيتم إضافة بطاقات المنتجات ديناميكياً -->
                        </WrapPanel>
                    </ScrollViewer>

                    <!-- رسالة عند عدم وجود منتجات -->
                    <TextBlock x:Name="NoProductsMessage"
                               Text="لا توجد منتجات متاحة في هذه الفئة"
                               FontSize="18"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Visibility="Collapsed"
                               Foreground="Gray"/>

                    <!-- مؤشر التحميل -->
                    <StackPanel x:Name="LoadingIndicator"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Visibility="Collapsed">
                        <TextBlock Text="جاري تحميل المنتجات..."
                                   FontSize="16"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,10"
                                   Foreground="Gray"/>
                        <ProgressBar Width="200"
                                     Height="10"
                                     IsIndeterminate="True"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- الجانب الأيسر - سلة المشتريات والدفع -->
        <Border Grid.Column="1" Background="#F5F5F5" CornerRadius="8" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,10,0,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- عنوان السلة -->
                <Border Grid.Row="0" Background="#2196F3" CornerRadius="8,8,0,0">
                    <TextBlock Text="سلة المشتريات"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="White"
                               Padding="15,10"
                               HorizontalAlignment="Center"/>
                </Border>

                <!-- معلومات العميل -->
                <Grid Grid.Row="1" Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="العميل:" VerticalAlignment="Center" FontWeight="SemiBold"/>
                    <Border Grid.Column="1" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="5,0">
                        <ComboBox x:Name="CustomerComboBox"
                                  DisplayMemberPath="Name"
                                  BorderThickness="0"
                                  Padding="5"/>
                    </Border>
                    <Button Grid.Column="2"
                            Content="+"
                            Width="30"
                            Height="30"
                            Click="AddCustomerButton_Click"
                            ToolTip="إضافة عميل جديد"
                            Background="#4CAF50"
                            Foreground="White"
                            FontWeight="Bold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                </Grid>

            <!-- قائمة المشتريات -->
            <Border Grid.Row="2" Margin="10" BorderThickness="1" BorderBrush="#DDDDDD" CornerRadius="4" Background="White">
                <Grid>
                    <ListView x:Name="CartListView"
                              BorderThickness="0"
                              Background="Transparent">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="Margin" Value="0,2"/>
                                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                <Setter Property="BorderBrush" Value="#EEEEEE"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="المنتج" Width="120">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding ProductName}"
                                                       FontWeight="SemiBold"
                                                       VerticalAlignment="Center"
                                                       TextTrimming="CharacterEllipsis"
                                                       ToolTip="{Binding ProductName}"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="السعر" Width="70">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding UnitPrice, StringFormat={}{0:N2}}"
                                                       VerticalAlignment="Center"
                                                       HorizontalAlignment="Center"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="الكمية" Width="80">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <Grid>
                                                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <Button Content="-"
                                                                Width="24"
                                                                Height="24"
                                                                Margin="0,0,2,0"
                                                                Click="DecreaseQuantityButton_Click"
                                                                Tag="{Binding}"
                                                                Background="#F44336"
                                                                Foreground="White"
                                                                FontWeight="Bold">
                                                            <Button.Resources>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="CornerRadius" Value="4,0,0,4"/>
                                                                </Style>
                                                            </Button.Resources>
                                                        </Button>
                                                        <TextBlock Text="{Binding Quantity}"
                                                                   Width="24"
                                                                   TextAlignment="Center"
                                                                   VerticalAlignment="Center"
                                                                   FontWeight="SemiBold"
                                                                   Background="#F5F5F5"
                                                                   Padding="0,4"/>
                                                        <Button Content="+"
                                                                Width="24"
                                                                Height="24"
                                                                Margin="2,0,0,0"
                                                                Click="IncreaseQuantityButton_Click"
                                                                Tag="{Binding}"
                                                                Background="#4CAF50"
                                                                Foreground="White"
                                                                FontWeight="Bold">
                                                            <Button.Resources>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="CornerRadius" Value="0,4,4,0"/>
                                                                </Style>
                                                            </Button.Resources>
                                                        </Button>
                                                    </StackPanel>
                                                </Border>
                                            </Grid>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="الإجمالي" Width="70">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding TotalPrice, StringFormat={}{0:N2}}"
                                                           VerticalAlignment="Center"
                                                           FontWeight="SemiBold"
                                                           Foreground="#2196F3"/>
                                                <Button Content="❌"
                                                        Width="24"
                                                        Height="24"
                                                        Margin="5,0,0,0"
                                                        Click="RemoveItemButton_Click"
                                                        Tag="{Binding}"
                                                        ToolTip="إزالة من السلة"
                                                        Background="Transparent"
                                                        BorderThickness="0"
                                                        Foreground="#F44336"
                                                        FontSize="12"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                            </GridView>
                        </ListView.View>
                    </ListView>

                    <!-- رسالة عند عدم وجود منتجات في السلة -->
                    <TextBlock x:Name="EmptyCartMessage"
                               Text="السلة فارغة"
                               FontSize="16"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="Gray"
                               Visibility="Visible"/>
                </Grid>
            </Border>

            <!-- ملخص الفاتورة -->
            <Border Grid.Row="3" Margin="10" BorderThickness="1" BorderBrush="#DDDDDD" CornerRadius="4" Background="White">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="المجموع:" FontWeight="SemiBold"/>
                    <TextBlock x:Name="SubtotalTextBlock"
                               Grid.Row="0"
                               Grid.Column="1"
                               Text="0.00"
                               FontWeight="SemiBold"
                               TextAlignment="Left"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="الخصم:" Margin="0,10,0,0"/>
                    <Grid Grid.Row="1" Grid.Column="1" Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4,0,0,4">
                            <TextBox x:Name="DiscountTextBox"
                                     Width="60"
                                     TextAlignment="Left"
                                     TextChanged="DiscountTextBox_TextChanged"
                                     BorderThickness="0"
                                     Padding="5,3"/>
                        </Border>
                        <Border Grid.Column="1" BorderBrush="#DDDDDD" CornerRadius="0,4,4,0" BorderThickness="0,1,1,1">
                            <ComboBox x:Name="DiscountTypeComboBox"
                                      Width="40"
                                      SelectedIndex="0"
                                      SelectionChanged="DiscountTypeComboBox_SelectionChanged"
                                      BorderThickness="0">
                                <ComboBoxItem Content="%"/>
                                <ComboBoxItem Content="ر.س"/>
                            </ComboBox>
                        </Border>
                    </Grid>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="الضريبة:" Margin="0,10,0,0"/>
                    <TextBlock x:Name="TaxTextBlock"
                               Grid.Row="2"
                               Grid.Column="1"
                               Text="0.00"
                               TextAlignment="Left"
                               Margin="0,10,0,0"
                               Foreground="#2196F3"/>

                    <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="0,10"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="الإجمالي:" FontWeight="Bold" FontSize="16" Margin="0,10,0,0"/>
                    <TextBlock x:Name="TotalTextBlock"
                               Grid.Row="4"
                               Grid.Column="1"
                               Text="0.00"
                               FontWeight="Bold"
                               FontSize="16"
                               TextAlignment="Left"
                               Margin="0,10,0,0"
                               Foreground="#4CAF50"/>
                </Grid>
            </Border>

            <!-- أزرار الدفع -->
            <StackPanel Grid.Row="4" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Button x:Name="PayCashButton"
                            Grid.Column="0"
                            Height="50"
                            Background="#4CAF50"
                            Foreground="White"
                            FontSize="16"
                            FontWeight="Bold"
                            Margin="0,0,5,0"
                            Click="PayCashButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💵" FontSize="18" Margin="0,0,8,0"/>
                            <TextBlock Text="نقداً" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="PayCardButton"
                            Grid.Column="1"
                            Height="50"
                            Background="#2196F3"
                            Foreground="White"
                            FontSize="16"
                            FontWeight="Bold"
                            Margin="5,0,5,0"
                            Click="PayCardButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💳" FontSize="18" Margin="0,0,8,0"/>
                            <TextBlock Text="بطاقة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="InstallmentButton"
                            Grid.Column="2"
                            Height="50"
                            Background="#FF9800"
                            Foreground="White"
                            FontSize="16"
                            FontWeight="Bold"
                            Margin="5,0,0,0"
                            Click="InstallmentButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📅" FontSize="18" Margin="0,0,8,0"/>
                            <TextBlock Text="أقساط" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Grid>
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Button x:Name="SaveDraftButton"
                            Grid.Column="0"
                            Height="40"
                            Margin="0,0,5,0"
                            Background="#9E9E9E"
                            Foreground="White"
                            Click="SaveDraftButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📝" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="حفظ مسودة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="WalletPayButton"
                            Grid.Column="1"
                            Height="40"
                            Margin="5,0,5,0"
                            Background="#673AB7"
                            Foreground="White"
                            Click="WalletPayButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📱" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="محفظة إلكترونية" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button x:Name="CancelButton"
                            Grid.Column="2"
                            Height="40"
                            Margin="5,0,0,0"
                            Background="#F44336"
                            Foreground="White"
                            Click="CancelButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <Button Grid.Column="1"
                            Content="حفظ كمسودة"
                            Height="40"
                            Margin="5,0,0,0"
                            Background="#2196F3"
                            Foreground="White"
                            Click="SaveDraftButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="💾" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="حفظ كمسودة" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </Grid>
            </StackPanel>
        </Grid>
        </Border>
    </Grid>
</UserControl>
