using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع الفواتير
    /// </summary>
    public class InvoiceRepository : IRepository<Invoice>
    {
        private readonly DatabaseContext _dbContext;

        /// <summary>
        /// إنشاء مستودع الفواتير
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public InvoiceRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        /// <summary>
        /// الحصول على جميع الفواتير
        /// </summary>
        /// <returns>قائمة بجميع الفواتير</returns>
        public async Task<IEnumerable<Invoice>> GetAllAsync()
        {
            string query = @"
                SELECT i.*, c.Name AS CustomerName, u.UserName
                FROM Invoices i
                LEFT JOIN Customers c ON i.CustomerId = c.Id
                LEFT JOIN Users u ON i.UserId = u.Id
                ORDER BY i.Date DESC";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return await ConvertDataTableToInvoicesAsync(dataTable);
        }

        /// <summary>
        /// الحصول على فاتورة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الفاتورة</param>
        /// <returns>الفاتورة إذا تم العثور عليها، وإلا فإنه يرجع null</returns>
        public async Task<Invoice> GetByIdAsync(int id)
        {
            string query = @"
                SELECT i.*, c.Name AS CustomerName, u.UserName
                FROM Invoices i
                LEFT JOIN Customers c ON i.CustomerId = c.Id
                LEFT JOIN Users u ON i.UserId = u.Id
                WHERE i.Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            var invoices = await ConvertDataTableToInvoicesAsync(dataTable);
            return invoices.FirstOrDefault();
        }

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        /// <param name="entity">الفاتورة المراد إضافتها</param>
        /// <returns>معرف الفاتورة المضافة</returns>
        public async Task<int> AddAsync(Invoice entity)
        {
            // بدء المعاملة
            await _dbContext.OpenConnectionAsync();
            SqlTransaction transaction = null;

            try
            {
                // إنشاء المعاملة
                transaction = _dbContext.GetConnection().BeginTransaction();

                // إضافة الفاتورة
                string query = @"
                    INSERT INTO Invoices (InvoiceNumber, Date, CustomerId, UserId, Subtotal, 
                        TaxAmount, DiscountAmount, Total, PaymentMethod, Status, Notes)
                    VALUES (@InvoiceNumber, @Date, @CustomerId, @UserId, @Subtotal, 
                        @TaxAmount, @DiscountAmount, @Total, @PaymentMethod, @Status, @Notes);
                    
                    SELECT SCOPE_IDENTITY();";

                SqlCommand command = new SqlCommand(query, _dbContext.GetConnection(), transaction);
                command.Parameters.AddRange(new SqlParameter[]
                {
                    new SqlParameter("@InvoiceNumber", entity.InvoiceNumber),
                    new SqlParameter("@Date", entity.Date),
                    new SqlParameter("@CustomerId", (object)entity.CustomerId ?? DBNull.Value),
                    new SqlParameter("@UserId", entity.UserId),
                    new SqlParameter("@Subtotal", entity.Subtotal),
                    new SqlParameter("@TaxAmount", entity.TaxAmount),
                    new SqlParameter("@DiscountAmount", entity.DiscountAmount),
                    new SqlParameter("@Total", entity.Total),
                    new SqlParameter("@PaymentMethod", (int)entity.PaymentMethod),
                    new SqlParameter("@Status", (int)entity.Status),
                    new SqlParameter("@Notes", (object)entity.Notes ?? DBNull.Value)
                });

                // الحصول على معرف الفاتورة المضافة
                int invoiceId = Convert.ToInt32(await command.ExecuteScalarAsync());

                // إضافة عناصر الفاتورة
                if (entity.Items != null && entity.Items.Any())
                {
                    foreach (var item in entity.Items)
                    {
                        string itemQuery = @"
                            INSERT INTO InvoiceItems (InvoiceId, ProductId, Quantity, UnitPrice, 
                                TaxAmount, DiscountAmount, Total)
                            VALUES (@InvoiceId, @ProductId, @Quantity, @UnitPrice, 
                                @TaxAmount, @DiscountAmount, @Total)";

                        SqlCommand itemCommand = new SqlCommand(itemQuery, _dbContext.GetConnection(), transaction);
                        itemCommand.Parameters.AddRange(new SqlParameter[]
                        {
                            new SqlParameter("@InvoiceId", invoiceId),
                            new SqlParameter("@ProductId", item.ProductId),
                            new SqlParameter("@Quantity", item.Quantity),
                            new SqlParameter("@UnitPrice", item.UnitPrice),
                            new SqlParameter("@TaxAmount", item.TaxAmount),
                            new SqlParameter("@DiscountAmount", item.DiscountAmount),
                            new SqlParameter("@Total", item.Total)
                        });

                        await itemCommand.ExecuteNonQueryAsync();

                        // تحديث المخزون
                        string updateStockQuery = @"
                            UPDATE Products
                            SET Quantity = Quantity - @Quantity
                            WHERE Id = @ProductId";

                        SqlCommand updateStockCommand = new SqlCommand(updateStockQuery, _dbContext.GetConnection(), transaction);
                        updateStockCommand.Parameters.AddRange(new SqlParameter[]
                        {
                            new SqlParameter("@ProductId", item.ProductId),
                            new SqlParameter("@Quantity", item.Quantity)
                        });

                        await updateStockCommand.ExecuteNonQueryAsync();
                    }
                }

                // إضافة معلومات الدفع
                if (entity.Payments != null && entity.Payments.Any())
                {
                    foreach (var payment in entity.Payments)
                    {
                        string paymentQuery = @"
                            INSERT INTO InvoicePayments (InvoiceId, PaymentMethod, Amount, 
                                ReferenceNumber, PaymentDate)
                            VALUES (@InvoiceId, @PaymentMethod, @Amount, 
                                @ReferenceNumber, @PaymentDate)";

                        SqlCommand paymentCommand = new SqlCommand(paymentQuery, _dbContext.GetConnection(), transaction);
                        paymentCommand.Parameters.AddRange(new SqlParameter[]
                        {
                            new SqlParameter("@InvoiceId", invoiceId),
                            new SqlParameter("@PaymentMethod", (int)payment.PaymentMethod),
                            new SqlParameter("@Amount", payment.Amount),
                            new SqlParameter("@ReferenceNumber", (object)payment.ReferenceNumber ?? DBNull.Value),
                            new SqlParameter("@PaymentDate", payment.PaymentDate)
                        });

                        await paymentCommand.ExecuteNonQueryAsync();
                    }
                }

                // إتمام المعاملة
                transaction.Commit();

                return invoiceId;
            }
            catch (Exception)
            {
                // التراجع عن المعاملة في حالة حدوث خطأ
                transaction?.Rollback();
                throw;
            }
            finally
            {
                // إغلاق الاتصال
                _dbContext.CloseConnection();
            }
        }

        /// <summary>
        /// تحديث فاتورة موجودة
        /// </summary>
        /// <param name="entity">الفاتورة المراد تحديثها</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateAsync(Invoice entity)
        {
            // لا يمكن تحديث الفواتير المكتملة
            if (entity.Status == InvoiceStatus.Completed || entity.Status == InvoiceStatus.Returned)
            {
                throw new InvalidOperationException("لا يمكن تحديث الفواتير المكتملة أو المرتجعة");
            }

            string query = @"
                UPDATE Invoices
                SET CustomerId = @CustomerId,
                    Subtotal = @Subtotal,
                    TaxAmount = @TaxAmount,
                    DiscountAmount = @DiscountAmount,
                    Total = @Total,
                    PaymentMethod = @PaymentMethod,
                    Status = @Status,
                    Notes = @Notes
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", entity.Id),
                new SqlParameter("@CustomerId", (object)entity.CustomerId ?? DBNull.Value),
                new SqlParameter("@Subtotal", entity.Subtotal),
                new SqlParameter("@TaxAmount", entity.TaxAmount),
                new SqlParameter("@DiscountAmount", entity.DiscountAmount),
                new SqlParameter("@Total", entity.Total),
                new SqlParameter("@PaymentMethod", (int)entity.PaymentMethod),
                new SqlParameter("@Status", (int)entity.Status),
                new SqlParameter("@Notes", (object)entity.Notes ?? DBNull.Value)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// حذف فاتورة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الفاتورة المراد حذفها</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> DeleteAsync(int id)
        {
            // التحقق من حالة الفاتورة
            var invoice = await GetByIdAsync(id);
            if (invoice == null)
            {
                throw new InvalidOperationException("الفاتورة غير موجودة");
            }

            if (invoice.Status == InvoiceStatus.Completed || invoice.Status == InvoiceStatus.Returned)
            {
                throw new InvalidOperationException("لا يمكن حذف الفواتير المكتملة أو المرتجعة");
            }

            // بدء المعاملة
            await _dbContext.OpenConnectionAsync();
            SqlTransaction transaction = null;

            try
            {
                // إنشاء المعاملة
                transaction = _dbContext.GetConnection().BeginTransaction();

                // حذف مدفوعات الفاتورة
                string deletePaymentsQuery = "DELETE FROM InvoicePayments WHERE InvoiceId = @Id";
                SqlCommand deletePaymentsCommand = new SqlCommand(deletePaymentsQuery, _dbContext.GetConnection(), transaction);
                deletePaymentsCommand.Parameters.Add(new SqlParameter("@Id", id));
                await deletePaymentsCommand.ExecuteNonQueryAsync();

                // استعادة المخزون
                string restoreStockQuery = @"
                    UPDATE p
                    SET p.Quantity = p.Quantity + i.Quantity
                    FROM Products p
                    INNER JOIN InvoiceItems i ON p.Id = i.ProductId
                    WHERE i.InvoiceId = @Id";
                SqlCommand restoreStockCommand = new SqlCommand(restoreStockQuery, _dbContext.GetConnection(), transaction);
                restoreStockCommand.Parameters.Add(new SqlParameter("@Id", id));
                await restoreStockCommand.ExecuteNonQueryAsync();

                // حذف عناصر الفاتورة
                string deleteItemsQuery = "DELETE FROM InvoiceItems WHERE InvoiceId = @Id";
                SqlCommand deleteItemsCommand = new SqlCommand(deleteItemsQuery, _dbContext.GetConnection(), transaction);
                deleteItemsCommand.Parameters.Add(new SqlParameter("@Id", id));
                await deleteItemsCommand.ExecuteNonQueryAsync();

                // حذف الفاتورة
                string deleteInvoiceQuery = "DELETE FROM Invoices WHERE Id = @Id";
                SqlCommand deleteInvoiceCommand = new SqlCommand(deleteInvoiceQuery, _dbContext.GetConnection(), transaction);
                deleteInvoiceCommand.Parameters.Add(new SqlParameter("@Id", id));
                int result = await deleteInvoiceCommand.ExecuteNonQueryAsync();

                // إتمام المعاملة
                transaction.Commit();

                return result;
            }
            catch (Exception)
            {
                // التراجع عن المعاملة في حالة حدوث خطأ
                transaction?.Rollback();
                throw;
            }
            finally
            {
                // إغلاق الاتصال
                _dbContext.CloseConnection();
            }
        }

        /// <summary>
        /// البحث عن فواتير بناءً على معايير معينة
        /// </summary>
        /// <param name="predicate">دالة التصفية</param>
        /// <returns>قائمة بالفواتير التي تطابق معايير البحث</returns>
        public async Task<IEnumerable<Invoice>> FindAsync(Func<Invoice, bool> predicate)
        {
            var invoices = await GetAllAsync();
            return invoices.Where(predicate);
        }

        /// <summary>
        /// البحث عن فواتير بواسطة النص
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة بالفواتير التي تطابق نص البحث</returns>
        public async Task<IEnumerable<Invoice>> SearchAsync(string searchText)
        {
            string query = @"
                SELECT i.*, c.Name AS CustomerName, u.UserName
                FROM Invoices i
                LEFT JOIN Customers c ON i.CustomerId = c.Id
                LEFT JOIN Users u ON i.UserId = u.Id
                WHERE i.InvoiceNumber LIKE @SearchText
                OR c.Name LIKE @SearchText
                OR u.UserName LIKE @SearchText
                OR CAST(i.Total AS NVARCHAR) LIKE @SearchText
                ORDER BY i.Date DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@SearchText", $"%{searchText}%")
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return await ConvertDataTableToInvoicesAsync(dataTable);
        }

        /// <summary>
        /// الحصول على فواتير بواسطة حالة الفاتورة
        /// </summary>
        /// <param name="status">حالة الفاتورة</param>
        /// <returns>قائمة بالفواتير بالحالة المحددة</returns>
        public async Task<IEnumerable<Invoice>> GetByStatusAsync(InvoiceStatus status)
        {
            string query = @"
                SELECT i.*, c.Name AS CustomerName, u.UserName
                FROM Invoices i
                LEFT JOIN Customers c ON i.CustomerId = c.Id
                LEFT JOIN Users u ON i.UserId = u.Id
                WHERE i.Status = @Status
                ORDER BY i.Date DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Status", (int)status)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return await ConvertDataTableToInvoicesAsync(dataTable);
        }

        /// <summary>
        /// الحصول على فواتير بواسطة التاريخ
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة بالفواتير في الفترة المحددة</returns>
        public async Task<IEnumerable<Invoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            string query = @"
                SELECT i.*, c.Name AS CustomerName, u.UserName
                FROM Invoices i
                LEFT JOIN Customers c ON i.CustomerId = c.Id
                LEFT JOIN Users u ON i.UserId = u.Id
                WHERE CONVERT(DATE, i.Date) BETWEEN CONVERT(DATE, @FromDate) AND CONVERT(DATE, @ToDate)
                ORDER BY i.Date DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@FromDate", fromDate),
                new SqlParameter("@ToDate", toDate)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return await ConvertDataTableToInvoicesAsync(dataTable);
        }

        /// <summary>
        /// الحصول على عناصر الفاتورة
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <returns>قائمة بعناصر الفاتورة</returns>
        public async Task<IEnumerable<InvoiceItem>> GetInvoiceItemsAsync(int invoiceId)
        {
            string query = @"
                SELECT i.*, p.Name AS ProductName, p.Barcode
                FROM InvoiceItems i
                LEFT JOIN Products p ON i.ProductId = p.Id
                WHERE i.InvoiceId = @InvoiceId";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@InvoiceId", invoiceId)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToInvoiceItems(dataTable);
        }

        /// <summary>
        /// الحصول على مدفوعات الفاتورة
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <returns>قائمة بمدفوعات الفاتورة</returns>
        public async Task<IEnumerable<InvoicePayment>> GetInvoicePaymentsAsync(int invoiceId)
        {
            string query = @"
                SELECT *
                FROM InvoicePayments
                WHERE InvoiceId = @InvoiceId";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@InvoiceId", invoiceId)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToInvoicePayments(dataTable);
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة فواتير
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة الفواتير</returns>
        private async Task<IEnumerable<Invoice>> ConvertDataTableToInvoicesAsync(DataTable dataTable)
        {
            List<Invoice> invoices = new List<Invoice>();

            foreach (DataRow row in dataTable.Rows)
            {
                int invoiceId = Convert.ToInt32(row["Id"]);

                Invoice invoice = new Invoice
                {
                    Id = invoiceId,
                    InvoiceNumber = row["InvoiceNumber"].ToString(),
                    Date = Convert.ToDateTime(row["Date"]),
                    UserId = Convert.ToInt32(row["UserId"]),
                    UserName = row["UserName"]?.ToString(),
                    Subtotal = Convert.ToDecimal(row["Subtotal"]),
                    TaxAmount = Convert.ToDecimal(row["TaxAmount"]),
                    DiscountAmount = Convert.ToDecimal(row["DiscountAmount"]),
                    Total = Convert.ToDecimal(row["Total"]),
                    PaymentMethod = (PaymentMethod)Convert.ToInt32(row["PaymentMethod"]),
                    Status = (InvoiceStatus)Convert.ToInt32(row["Status"])
                };

                if (row["CustomerId"] != DBNull.Value)
                {
                    invoice.CustomerId = Convert.ToInt32(row["CustomerId"]);
                    invoice.CustomerName = row["CustomerName"]?.ToString();
                }

                if (row["Notes"] != DBNull.Value)
                {
                    invoice.Notes = row["Notes"].ToString();
                }

                // الحصول على عناصر الفاتورة
                invoice.Items = (await GetInvoiceItemsAsync(invoiceId)).ToList();

                // الحصول على مدفوعات الفاتورة
                invoice.Payments = (await GetInvoicePaymentsAsync(invoiceId)).ToList();

                invoices.Add(invoice);
            }

            return invoices;
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة عناصر الفاتورة
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة عناصر الفاتورة</returns>
        private IEnumerable<InvoiceItem> ConvertDataTableToInvoiceItems(DataTable dataTable)
        {
            List<InvoiceItem> items = new List<InvoiceItem>();

            foreach (DataRow row in dataTable.Rows)
            {
                InvoiceItem item = new InvoiceItem
                {
                    Id = Convert.ToInt32(row["Id"]),
                    InvoiceId = Convert.ToInt32(row["InvoiceId"]),
                    ProductId = Convert.ToInt32(row["ProductId"]),
                    ProductName = row["ProductName"]?.ToString(),
                    Barcode = row["Barcode"]?.ToString(),
                    Quantity = Convert.ToDecimal(row["Quantity"]),
                    UnitPrice = Convert.ToDecimal(row["UnitPrice"]),
                    TaxAmount = Convert.ToDecimal(row["TaxAmount"]),
                    DiscountAmount = Convert.ToDecimal(row["DiscountAmount"]),
                    Total = Convert.ToDecimal(row["Total"])
                };

                items.Add(item);
            }

            return items;
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة مدفوعات الفاتورة
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة مدفوعات الفاتورة</returns>
        private IEnumerable<InvoicePayment> ConvertDataTableToInvoicePayments(DataTable dataTable)
        {
            List<InvoicePayment> payments = new List<InvoicePayment>();

            foreach (DataRow row in dataTable.Rows)
            {
                InvoicePayment payment = new InvoicePayment
                {
                    Id = Convert.ToInt32(row["Id"]),
                    InvoiceId = Convert.ToInt32(row["InvoiceId"]),
                    PaymentMethod = (PaymentMethod)Convert.ToInt32(row["PaymentMethod"]),
                    Amount = Convert.ToDecimal(row["Amount"]),
                    PaymentDate = Convert.ToDateTime(row["PaymentDate"])
                };

                if (row["ReferenceNumber"] != DBNull.Value)
                {
                    payment.ReferenceNumber = row["ReferenceNumber"].ToString();
                }

                payments.Add(payment);
            }

            return payments;
        }
    }
}
