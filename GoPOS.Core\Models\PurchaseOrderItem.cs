using System;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج عنصر أمر الشراء
    /// </summary>
    public class PurchaseOrderItem
    {
        public int Id { get; set; }

        [Required]
        public int PurchaseOrderId { get; set; }
        public PurchaseOrder PurchaseOrder { get; set; }

        [Required]
        public int ProductId { get; set; }
        public Product Product { get; set; }

        [Required]
        public int Quantity { get; set; }

        public int ReceivedQuantity { get; set; }

        [Required]
        public decimal UnitPrice { get; set; }

        [Required]
        public decimal TotalPrice { get; set; }

        public decimal? Discount { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [MaxLength(200)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
    }
}
