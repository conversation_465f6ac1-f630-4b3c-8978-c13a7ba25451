using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(15)]
        public string Phone { get; set; }

        [MaxLength(100)]
        [EmailAddress]
        public string Email { get; set; }

        [MaxLength(200)]
        public string Address { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public ICollection<Order> Orders { get; set; }
    }
}
