<Window x:Class="GoPOS.UI.Views.BackupInfoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="معلومات النسخة الاحتياطية" 
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        Background="#F5F5F5"
        Icon="/GoPOS.UI;component/Resources/Images/logo.png">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#BDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#1565C0" Padding="15">
            <TextBlock Text="معلومات النسخة الاحتياطية" FontSize="18" FontWeight="Bold" Foreground="White"/>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- اسم الملف -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الملف:" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="FileNameTextBlock" Text="-" Margin="10,0,0,10"/>
            
            <!-- المسار الكامل -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="المسار الكامل:" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="FullPathTextBlock" Text="-" Margin="10,0,0,10" TextWrapping="Wrap"/>
            
            <!-- تاريخ الإنشاء -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ الإنشاء:" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="CreationDateTextBlock" Text="-" Margin="10,0,0,10"/>
            
            <!-- حجم الملف -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="حجم الملف:" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Grid.Row="3" Grid.Column="1" x:Name="FileSizeTextBlock" Text="-" Margin="10,0,0,10"/>
            
            <!-- نوع النسخة الاحتياطية -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="نوع النسخة:" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Grid.Row="4" Grid.Column="1" x:Name="BackupTypeTextBlock" Text="-" Margin="10,0,0,10"/>
            
            <!-- اسم قاعدة البيانات -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="قاعدة البيانات:" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Grid.Row="5" Grid.Column="1" x:Name="DatabaseNameTextBlock" Text="-" Margin="10,0,0,10"/>
            
            <!-- المستخدم -->
            <TextBlock Grid.Row="6" Grid.Column="0" Text="تم الإنشاء بواسطة:" FontWeight="Bold" Margin="0,0,0,10"/>
            <TextBlock Grid.Row="6" Grid.Column="1" x:Name="CreatedByTextBlock" Text="-" Margin="10,0,0,10"/>
            
            <!-- حالة الملف -->
            <TextBlock Grid.Row="7" Grid.Column="0" Text="حالة الملف:" FontWeight="Bold" Margin="0,0,0,10"/>
            <StackPanel Grid.Row="7" Grid.Column="1" Orientation="Horizontal" Margin="10,0,0,10">
                <Ellipse x:Name="FileStatusEllipse" Width="16" Height="16" Fill="Green" VerticalAlignment="Center"/>
                <TextBlock x:Name="FileStatusTextBlock" Text="سليم" Margin="5,0,0,0" VerticalAlignment="Center"/>
            </StackPanel>
            
            <!-- معلومات إضافية -->
            <TextBlock Grid.Row="8" Grid.Column="0" Text="معلومات إضافية:" FontWeight="Bold" VerticalAlignment="Top" Margin="0,0,0,0"/>
            <TextBox Grid.Row="8" Grid.Column="1" x:Name="AdditionalInfoTextBox" IsReadOnly="True" TextWrapping="Wrap" 
                     VerticalScrollBarVisibility="Auto" Margin="10,0,0,0" BorderThickness="0" Background="Transparent"/>
        </Grid>
        
        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
            <Button x:Name="RestoreButton" Content="استعادة" Click="RestoreButton_Click" Width="100"/>
            <Button x:Name="CloseButton" Content="إغلاق" Click="CloseButton_Click" Width="100" Background="#9E9E9E"/>
        </StackPanel>
    </Grid>
</Window>
