using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for ProductSelectionWindow.xaml
    /// </summary>
    public partial class ProductSelectionWindow : Window
    {
        private ObservableCollection<ProductViewModel> _products;
        private ObservableCollection<ProductViewModel> _filteredProducts;
        private ProductViewModel _selectedProduct;

        public ProductViewModel SelectedProduct => _selectedProduct;

        public ProductSelectionWindow()
        {
            InitializeComponent();
            LoadProducts();

            // التركيز على حقل البحث
            SearchTextBox.Focus();
        }

        private void LoadProducts()
        {
            // تحميل قائمة المنتجات من قاعدة البيانات
            _products = new ObservableCollection<ProductViewModel>();
            Random random = new Random();

            // بيانات المنتجات
            for (int i = 1; i <= 50; i++)
            {
                int categoryIndex = random.Next(0, 3);
                string categoryName = categoryIndex == 0 ? "مشروبات" : (categoryIndex == 1 ? "وجبات سريعة" : "حلويات");

                string productName = GetRandomProductName(categoryIndex);

                _products.Add(new ProductViewModel
                {
                    Id = i,
                    Barcode = $"100{i:D4}",
                    Name = productName,
                    CategoryId = categoryIndex + 1,
                    CategoryName = categoryName,
                    Quantity = random.Next(0, 100),
                    Price = (decimal)Math.Round(random.Next(10, 100) + random.NextDouble(), 2)
                });
            }

            _filteredProducts = new ObservableCollection<ProductViewModel>(_products);
            ProductsDataGrid.ItemsSource = _filteredProducts;
        }

        private string GetRandomProductName(int categoryIndex)
        {
            string[] drinks = { "قهوة", "شاي", "عصير برتقال", "عصير تفاح", "ماء معدني", "مشروب غازي", "عصير مانجو" };
            string[] fastFood = { "برجر", "بيتزا", "ساندويتش", "دجاج مقلي", "بطاطس", "ناجتس", "شاورما" };
            string[] desserts = { "كيك", "آيس كريم", "كوكيز", "دونات", "تشيز كيك", "بودينج", "كنافة" };

            Random random = new Random();

            if (categoryIndex == 0)
                return drinks[random.Next(drinks.Length)];
            else if (categoryIndex == 1)
                return fastFood[random.Next(fastFood.Length)];
            else
                return desserts[random.Next(desserts.Length)];
        }

        private void FilterProducts()
        {
            string searchText = SearchTextBox.Text.Trim().ToLower();
            int categoryIndex = CategoryComboBox.SelectedIndex;

            _filteredProducts.Clear();

            foreach (var product in _products)
            {
                bool matchesFilter = true;

                // تطبيق فلتر البحث
                if (!string.IsNullOrEmpty(searchText))
                {
                    if (!product.Name.ToLower().Contains(searchText) && !product.Barcode.ToLower().Contains(searchText))
                        matchesFilter = false;
                }

                // تطبيق فلتر الفئة
                if (categoryIndex > 0 && product.CategoryId != categoryIndex)
                    matchesFilter = false;

                if (matchesFilter)
                    _filteredProducts.Add(product);
            }
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                FilterProducts();
            }
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterProducts();
        }

        private void ProductsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedProduct = ProductsDataGrid.SelectedItem as ProductViewModel;
            SelectButton.IsEnabled = _selectedProduct != null;
        }

        private void ProductsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (_selectedProduct != null)
            {
                DialogResult = true;
                Close();
            }
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null)
            {
                MessageBox.Show("الرجاء اختيار منتج", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // فئة عرض بيانات المنتج
    public class ProductViewModel
    {
        public int Id { get; set; }
        public required string Barcode { get; set; }
        public required string Name { get; set; }
        public int CategoryId { get; set; }
        public required string CategoryName { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
    }
}
