using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;
using GoPOS.UI.ViewModels;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for CustomersManagementView.xaml
    /// </summary>
    public partial class CustomersManagementView : UserControl
    {
        private ObservableCollection<CustomerViewModel> _customers = new ObservableCollection<CustomerViewModel>();
        private CustomerViewModel? _selectedCustomer;
        private CustomerRepository _customerRepository;
        private DatabaseContext _dbContext;

        public CustomersManagementView()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _customerRepository = new CustomerRepository(_dbContext);

            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // تهيئة مجموعة العملاء
                _customers = new ObservableCollection<CustomerViewModel>();

                // تحميل العملاء من قاعدة البيانات
                var customers = await _customerRepository.GetAllAsync();

                foreach (var customer in customers)
                {
                    _customers.Add(new CustomerViewModel
                    {
                        Id = customer.Id,
                        Name = customer.Name,
                        Phone = customer.Phone,
                        Email = customer.Email,
                        Address = customer.Address,
                        Notes = customer.Notes,
                        IsActive = customer.IsActive,
                        InvoiceCount = customer.InvoiceCount,
                        TotalPurchases = customer.TotalPurchases
                    });
                }

                // تعيين مصدر البيانات للجدول
                CustomersDataGrid.ItemsSource = _customers;
                UpdateButtonsState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void CustomersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedCustomer = CustomersDataGrid.SelectedItem as CustomerViewModel;
            UpdateButtonsState();
        }

        private void UpdateButtonsState()
        {
            bool hasSelection = _selectedCustomer != null;
            EditCustomerButton.IsEnabled = hasSelection;
            DeleteCustomerButton.IsEnabled = hasSelection;
        }

        private void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إضافة عميل جديد
            CustomerEditorWindow editorWindow = new CustomerEditorWindow(_customerRepository);
            editorWindow.Owner = Window.GetWindow(this);
            editorWindow.ShowDialog();

            // إعادة تحميل العملاء بعد الإضافة
            if (editorWindow.DialogResult == true)
            {
                LoadDataAsync();
            }
        }

        private void EditCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCustomer == null) return;

            // فتح نافذة تعديل العميل
            CustomerEditorWindow editorWindow = new CustomerEditorWindow(_selectedCustomer, _customerRepository);
            editorWindow.Owner = Window.GetWindow(this);
            editorWindow.ShowDialog();

            // إعادة تحميل العملاء بعد التعديل
            if (editorWindow.DialogResult == true)
            {
                LoadDataAsync();
            }
        }

        private async void DeleteCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCustomer == null) return;

            MessageBoxResult result = MessageBox.Show(
                $"هل أنت متأكد من حذف العميل '{_selectedCustomer.Name}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // حذف العميل من قاعدة البيانات
                    await _customerRepository.DeleteAsync(_selectedCustomer.Id);

                    // حذف العميل من القائمة
                    _customers.Remove(_selectedCustomer);

                    MessageBox.Show("تم حذف العميل بنجاح", "تم الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء حذف العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchCustomers();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchCustomers();
            }
        }

        private async void SearchCustomers()
        {
            try
            {
                string searchText = SearchTextBox.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    LoadDataAsync();
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // البحث في قاعدة البيانات
                var searchResults = await _customerRepository.SearchAsync(searchText);

                // تحويل النتائج إلى نماذج عرض
                ObservableCollection<CustomerViewModel> filteredCustomers = new ObservableCollection<CustomerViewModel>();
                foreach (var customer in searchResults)
                {
                    filteredCustomers.Add(new CustomerViewModel
                    {
                        Id = customer.Id,
                        Name = customer.Name,
                        Phone = customer.Phone,
                        Email = customer.Email,
                        Address = customer.Address,
                        Notes = customer.Notes,
                        IsActive = customer.IsActive,
                        InvoiceCount = customer.InvoiceCount,
                        TotalPurchases = customer.TotalPurchases
                    });
                }

                // عرض النتائج
                CustomersDataGrid.ItemsSource = filteredCustomers;
                _customers = filteredCustomers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث عن العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }
    }


}
