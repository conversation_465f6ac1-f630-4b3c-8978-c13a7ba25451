<UserControl x:Class="GoPOS.UI.Views.InvoicesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والأدوات -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="إدارة الفواتير" FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text="📝" FontSize="24" Margin="10,0,0,0" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="CreateInvoiceButton" Content="إنشاء فاتورة جديدة" Padding="15,8" Margin="0,0,10,0" Background="#4CAF50" Foreground="White" Click="CreateInvoiceButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="+" FontSize="16" FontWeight="Bold" Margin="0,0,8,0"/>
                                    <TextBlock Text="إنشاء فاتورة جديدة" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="RefreshButton" Content="تحديث" Padding="15,8" Background="#FF9800" Foreground="White" Click="RefreshButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة الفواتير -->
            <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" Margin="10" CornerRadius="4">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط البحث والتصفية -->
                    <Grid Grid.Row="0" Background="#F5F5F5" Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Background="White">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="SearchTextBox" Grid.Column="0" Padding="10,5" BorderThickness="0" KeyDown="SearchTextBox_KeyDown"/>
                                <!-- استخدم TextBlock كبديل لـ PlaceholderText -->
                                <TextBlock IsHitTestVisible="False" Text="بحث برقم الفاتورة أو اسم العميل..." VerticalAlignment="Center" HorizontalAlignment="Left" Margin="10,0,0,0" Foreground="DarkGray">
                                    <TextBlock.Style>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Text, ElementName=SearchTextBox}" Value="">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <Button x:Name="SearchButton" Grid.Column="1" Content="🔍" Width="40" BorderThickness="0" Background="Transparent" Click="SearchButton_Click"/>
                            </Grid>
                        </Border>

                        <ComboBox x:Name="FilterStatusComboBox" Grid.Column="1" Width="120" Margin="10,0,0,0" Padding="10,5" SelectionChanged="FilterStatusComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                            <ComboBoxItem Content="مسودة"/>
                            <ComboBoxItem Content="مكتملة"/>
                            <ComboBoxItem Content="ملغاة"/>
                            <ComboBoxItem Content="مرتجعة"/>
                        </ComboBox>

                        <ComboBox x:Name="FilterPaymentComboBox" Grid.Column="2" Width="120" Margin="10,0,0,0" Padding="10,5" SelectionChanged="FilterPaymentComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع طرق الدفع" IsSelected="True"/>
                            <ComboBoxItem Content="نقداً"/>
                            <ComboBoxItem Content="بطاقة ائتمان"/>
                            <ComboBoxItem Content="تحويل بنكي"/>
                            <ComboBoxItem Content="أقساط"/>
                            <ComboBoxItem Content="أخرى"/>
                        </ComboBox>

                        <DatePicker x:Name="FilterDatePicker" Grid.Column="3" Width="120" Margin="10,0,0,0" SelectedDateChanged="FilterDatePicker_SelectedDateChanged"/>
                    </Grid>

                    <!-- جدول الفواتير -->
                    <DataGrid x:Name="InvoicesDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True"
                              BorderThickness="0" GridLinesVisibility="Horizontal" AlternatingRowBackground="#F5F5F5"
                              CanUserSortColumns="True" SelectionMode="Single" SelectionChanged="InvoicesDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat={}{0:yyyy/MM/dd}}" Width="100"/>
                            <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                            <DataGridTextColumn Header="المجموع" Binding="{Binding Total, StringFormat={}{0:N2}}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="80"/>
                            <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethodText}" Width="100"/>
                            <DataGridTextColumn Header="المستخدم" Binding="{Binding UserName}" Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="👁️" ToolTip="عرض" Margin="2" Padding="5" Click="ViewInvoiceButton_Click" Tag="{Binding}"/>
                                            <Button Content="📝" ToolTip="تعديل" Margin="2" Padding="5" Click="EditInvoiceButton_Click" Tag="{Binding}" Visibility="{Binding CanEdit}"/>
                                            <Button Content="🗑️" ToolTip="حذف" Margin="2" Padding="5" Click="DeleteInvoiceButton_Click" Tag="{Binding}" Visibility="{Binding CanDelete}"/>
                                            <Button Content="🖨️" ToolTip="طباعة" Margin="2" Padding="5" Click="PrintInvoiceButton_Click" Tag="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- رسالة عند عدم وجود فواتير -->
                    <TextBlock x:Name="NoInvoicesMessage" Grid.Row="1" Text="لا توجد فواتير للعرض"
                               FontSize="18" HorizontalAlignment="Center" VerticalAlignment="Center"
                               Foreground="Gray" Visibility="Collapsed"/>

                    <!-- مؤشر التحميل -->
                    <StackPanel x:Name="LoadingIndicator" Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center" Visibility="Collapsed">
                        <TextBlock Text="جاري تحميل البيانات..." FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,10" Foreground="Gray"/>
                        <ProgressBar Width="200" Height="10" IsIndeterminate="True"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- ملخص الفواتير -->
            <Border Grid.Column="1" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,10,10,10" CornerRadius="4">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان الملخص -->
                    <Border Grid.Row="0" Background="#2196F3" Margin="0" Padding="0,10" CornerRadius="4,4,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="ملخص الفواتير" FontSize="18" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="📊" FontSize="18" Margin="10,0,0,0" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- معلومات الملخص -->
                    <Grid Grid.Row="1" Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي الفواتير:" FontWeight="SemiBold"/>
                        <TextBlock x:Name="TotalInvoicesCountTextBlock" Grid.Row="0" Grid.Column="1" Text="0" FontWeight="Bold"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="إجمالي المبيعات:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="TotalSalesTextBlock" Grid.Row="1" Grid.Column="1" Text="0.00" Foreground="#4CAF50" FontWeight="Bold" Margin="0,10,0,0"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="فواتير اليوم:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="TodayInvoicesCountTextBlock" Grid.Row="2" Grid.Column="1" Text="0" Margin="0,10,0,0"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="مبيعات اليوم:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="TodaySalesTextBlock" Grid.Row="3" Grid.Column="1" Text="0.00" Foreground="#4CAF50" Margin="0,10,0,0"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="فواتير غير مدفوعة:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="UnpaidInvoicesCountTextBlock" Grid.Row="4" Grid.Column="1" Text="0" Foreground="#F44336" Margin="0,10,0,0"/>

                        <TextBlock Grid.Row="5" Grid.Column="0" Text="المبالغ غير المدفوعة:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="UnpaidAmountTextBlock" Grid.Row="5" Grid.Column="1" Text="0.00" Foreground="#F44336" Margin="0,10,0,0"/>
                    </Grid>

                    <!-- أزرار التقارير -->
                    <StackPanel Grid.Row="2" Margin="10">
                        <Button x:Name="DailySalesReportButton" Content="تقرير مبيعات اليوم" Margin="0,0,0,10" Padding="10" Background="#2196F3" Foreground="White" Click="DailySalesReportButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📊" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="تقرير مبيعات اليوم" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="MonthlySalesReportButton" Content="تقرير مبيعات الشهر" Margin="0,0,0,10" Padding="10" Background="#4CAF50" Foreground="White" Click="MonthlySalesReportButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📈" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="تقرير مبيعات الشهر" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="UnpaidInvoicesReportButton" Content="تقرير الفواتير غير المدفوعة" Margin="0,0,0,10" Padding="10" Background="#FF9800" Foreground="White" Click="UnpaidInvoicesReportButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="⚠️" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="تقرير الفواتير غير المدفوعة" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="PrintAllInvoicesButton" Content="طباعة جميع الفواتير" Padding="10" Background="#607D8B" Foreground="White" Click="PrintAllInvoicesButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🖨️" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="طباعة جميع الفواتير" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
