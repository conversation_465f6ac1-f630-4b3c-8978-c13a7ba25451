using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Animation;
using System.Windows.Controls;
using System.IO;
using System.Xml.Serialization;
using System.Threading.Tasks;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        private const string CREDENTIALS_FILE = "user_credentials.xml";
        private readonly DatabaseContext _dbContext;
        private readonly UserRepository _userRepository;

        public LoginWindow()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _userRepository = new UserRepository(_dbContext);

            // إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
            CreateDefaultUserIfNeededAsync();

            // تحميل بيانات المستخدم المحفوظة (إن وجدت)
            LoadSavedCredentials();

            // التركيز على حقل اسم المستخدم إذا كان فارغًا، وإلا على حقل كلمة المرور
            if (string.IsNullOrEmpty(UsernameTextBox.Text))
            {
                UsernameTextBox.Focus();
            }
            else
            {
                PasswordBox.Focus();
            }

            // إضافة معالج حدث الضغط على مفتاح Enter
            UsernameTextBox.KeyDown += InputField_KeyDown;
            PasswordBox.KeyDown += InputField_KeyDown;
        }

        /// <summary>
        /// إنشاء مستخدمين افتراضيين إذا لم يكن هناك مستخدمين في قاعدة البيانات
        /// </summary>
        private async void CreateDefaultUserIfNeededAsync()
        {
            try
            {
                // التحقق من وجود مستخدمين في قاعدة البيانات
                var users = await _userRepository.GetAllAsync();
                if (!users.Any())
                {
                    // إنشاء مستخدم مدير افتراضي
                    User adminUser = new User
                    {
                        Username = "admin",
                        PasswordHash = _userRepository.HashPassword("admin"),
                        FullName = "مدير النظام",
                        Email = "<EMAIL>",
                        Role = UserRole.Admin,
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        Permissions = new UserPermissions()
                    };

                    // إضافة المستخدم المدير إلى قاعدة البيانات
                    await _userRepository.AddAsync(adminUser);

                    // إنشاء مستخدم مطور (للصيانة)
                    User developerUser = new User
                    {
                        Username = "developer",
                        PasswordHash = _userRepository.HashPassword("dev@2024"),
                        FullName = "مطور النظام",
                        Email = "<EMAIL>",
                        Role = UserRole.Developer,
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        Permissions = new UserPermissions
                        {
                            CanPerformMaintenance = true,
                            CanManageBackups = true,
                            CanAccessDatabase = true
                        }
                    };

                    // إضافة المستخدم المطور إلى قاعدة البيانات
                    await _userRepository.AddAsync(developerUser);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء المستخدمين الافتراضيين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InputField_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إخفاء رسالة الخطأ السابقة إن وجدت
                ErrorMessageText.Visibility = Visibility.Collapsed;

                // تعطيل زر تسجيل الدخول لمنع النقر المتكرر
                LoginButton.IsEnabled = false;
                LoginButton.Content = "جاري تسجيل الدخول...";

                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                string username = UsernameTextBox.Text.Trim();
                string password = PasswordBox.Password;

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    ShowError("الرجاء إدخال اسم المستخدم وكلمة المرور");
                    return;
                }

                // التحقق من بيانات تسجيل الدخول من قاعدة البيانات
                User user = await _userRepository.ValidateUserAsync(username, password);

                if (user != null)
                {
                    // حفظ بيانات المستخدم إذا تم اختيار "تذكرني"
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        SaveCredentials(username);
                    }
                    else
                    {
                        // إزالة الملف إذا كان موجودًا
                        ClearSavedCredentials();
                    }

                    try
                    {
                        // إظهار رسالة ترحيبية
                        MessageBox.Show($"مرحبًا بك {user.FullName} في نظام نقاط البيع GoPOS", "ترحيب", MessageBoxButton.OK, MessageBoxImage.Information);

                        // تخزين معلومات المستخدم في الجلسة الحالية
                        App.CurrentUser = user;

                        MainWindow mainWindow = new MainWindow();
                        mainWindow.Show();
                        this.Close();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"حدث خطأ أثناء فتح النافذة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        // إعادة تمكين زر تسجيل الدخول
                        LoginButton.IsEnabled = true;
                        LoginButton.Content = "تسجيل الدخول";
                    }
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    PasswordBox.Password = string.Empty;
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين زر تسجيل الدخول
                LoginButton.IsEnabled = true;
                LoginButton.Content = "تسجيل الدخول";

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageText.Text = message;
            ErrorMessageText.Visibility = Visibility.Visible;

            // إضافة تأثير حركي لرسالة الخطأ
            DoubleAnimation animation = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = TimeSpan.FromSeconds(0.3)
            };
            ErrorMessageText.BeginAnimation(OpacityProperty, animation);
        }

        private void ForgotPassword_MouseDown(object sender, MouseButtonEventArgs e)
        {
            MessageBox.Show("لإعادة تعيين كلمة المرور، يرجى التواصل مع مسؤول النظام.", "نسيت كلمة المرور", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// معالجة حدث النقر على زر وضع الصيانة
        /// </summary>
        private void MaintenanceModeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من إدخال بيانات تسجيل الدخول
                string username = UsernameTextBox.Text.Trim();
                string password = PasswordBox.Password;

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    MessageBox.Show("الرجاء إدخال اسم المستخدم وكلمة المرور للدخول إلى وضع الصيانة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                // التحقق من صلاحيات المستخدم
                Task.Run(async () =>
                {
                    try
                    {
                        // التحقق من بيانات تسجيل الدخول
                        User user = await _userRepository.ValidateUserAsync(username, password);

                        Dispatcher.Invoke(() =>
                        {
                            if (user != null)
                            {
                                // التحقق من صلاحيات المستخدم
                                if (user.Role == UserRole.Developer || user.Permissions.CanPerformMaintenance)
                                {
                                    // تخزين معلومات المستخدم في الجلسة الحالية
                                    App.CurrentUser = user;

                                    // فتح نافذة الصيانة
                                    MaintenanceWindow maintenanceWindow = new MaintenanceWindow();
                                    maintenanceWindow.Show();
                                }
                                else
                                {
                                    MessageBox.Show("ليس لديك صلاحية للوصول إلى وضع الصيانة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                                }
                            }
                            else
                            {
                                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            }

                            // إعادة المؤشر إلى الوضع الطبيعي
                            Cursor = Cursors.Arrow;
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show($"حدث خطأ أثناء الوصول إلى وضع الصيانة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            Cursor = Cursors.Arrow;
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = Cursors.Arrow;
            }
        }

        #region حفظ واستعادة بيانات المستخدم   

        [Serializable]
        public class UserCredentials
        {
            public string Username { get; set; }
        }

        private void SaveCredentials(string username)
        {
            try
            {
                UserCredentials credentials = new UserCredentials { Username = username };
                XmlSerializer serializer = new XmlSerializer(typeof(UserCredentials));

                using (FileStream fs = new FileStream(CREDENTIALS_FILE, FileMode.Create))
                {
                    serializer.Serialize(fs, credentials);
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في حفظ بيانات المستخدم
                Console.WriteLine($"خطأ في حفظ بيانات المستخدم: {ex.Message}");
            }
        }

        private void LoadSavedCredentials()
        {
            try
            {
                if (File.Exists(CREDENTIALS_FILE))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(UserCredentials));

                    using (FileStream fs = new FileStream(CREDENTIALS_FILE, FileMode.Open))
                    {
                        UserCredentials credentials = (UserCredentials)serializer.Deserialize(fs);

                        if (credentials != null && !string.IsNullOrEmpty(credentials.Username))
                        {
                            UsernameTextBox.Text = credentials.Username;
                            RememberMeCheckBox.IsChecked = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في تحميل بيانات المستخدم
                Console.WriteLine($"خطأ في تحميل بيانات المستخدم: {ex.Message}");
            }
        }

        private void ClearSavedCredentials()
        {
            try
            {
                if (File.Exists(CREDENTIALS_FILE))
                {
                    File.Delete(CREDENTIALS_FILE);
                }
            }
            catch
            {
                // تجاهل الأخطاء في حذف ملف بيانات المستخدم
            }
        }

        #endregion
    }
}
