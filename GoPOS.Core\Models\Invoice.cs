using System;
using System.Collections.Generic;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج بيانات الفاتورة
    /// </summary>
    public class Invoice
    {
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        public string? InvoiceNumber { get; set; }

        /// <summary>
        /// تاريخ الفاتورة
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerId { get; set; }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// البريد الإلكتروني للعميل (اختياري)
        /// </summary>
        public string? CustomerEmail { get; set; }

        /// <summary>
        /// رقم هاتف العميل (اختياري)
        /// </summary>
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ الفاتورة
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// اسم المستخدم الذي أنشأ الفاتورة
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// المجموع الفرعي (قبل الخصم والضريبة)
        /// </summary>
        public decimal Subtotal { get; set; }

        /// <summary>
        /// قيمة الخصم
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// نسبة الخصم
        /// </summary>
        public decimal DiscountPercentage { get; set; }

        /// <summary>
        /// هل الخصم نسبة مئوية
        /// </summary>
        public bool IsDiscountPercentage { get; set; }

        /// <summary>
        /// قيمة الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// نسبة الضريبة
        /// </summary>
        public decimal TaxPercentage { get; set; }

        /// <summary>
        /// المجموع النهائي (بعد الخصم والضريبة)
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// حالة الفاتورة
        /// </summary>
        public InvoiceStatus Status { get; set; }

        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public InvoicePaymentMethod PaymentMethod { get; set; }

        /// <summary>
        /// هل تم الدفع بالكامل
        /// </summary>
        public bool IsPaid { get; set; }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// هل هي فاتورة أقساط
        /// </summary>
        public bool IsInstallment { get; set; }

        /// <summary>
        /// معرف خطة الأقساط (إذا كانت فاتورة أقساط)
        /// </summary>
        public int? InstallmentPlanId { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// عناصر الفاتورة
        /// </summary>
        public List<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();

        /// <summary>
        /// هل الفاتورة مرتجعة
        /// </summary>
        public bool IsReturnInvoice { get; set; }

        /// <summary>
        /// معرف الفاتورة الأصلية (في حال كانت هذه فاتورة مرتجعة)
        /// </summary>
        public int? OriginalInvoiceId { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ الفاتورة
        /// </summary>
        public int? UserIdCreated { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدّل الفاتورة آخر مرة
        /// </summary>
        public int? UserIdUpdated { get; set; }
    }

    /// <summary>
    /// نموذج بيانات عنصر الفاتورة
    /// </summary>
    public class InvoiceItem
    {
        /// <summary>
        /// معرف العنصر
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int InvoiceId { get; set; }

        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// اسم المنتج
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// الباركود
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// قيمة الخصم
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// نسبة الخصم
        /// </summary>
        public decimal DiscountPercentage { get; set; }

        /// <summary>
        /// هل الخصم نسبة مئوية
        /// </summary>
        public bool IsDiscountPercentage { get; set; }

        /// <summary>
        /// قيمة الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// نسبة الضريبة
        /// </summary>
        public decimal TaxPercentage { get; set; }

        /// <summary>
        /// المجموع (بعد الخصم والضريبة)
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ العنصر
        /// </summary>
        public int? UserIdCreated { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدّل العنصر آخر مرة
        /// </summary>
        public int? UserIdUpdated { get; set; }
    }

    /// <summary>
    /// حالات الفاتورة
    /// </summary>
    public enum InvoiceStatus
    {
        /// <summary>
        /// مسودة
        /// </summary>
        Draft = 1,

        /// <summary>
        /// مكتملة
        /// </summary>
        Completed = 2,

        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 3,

        /// <summary>
        /// مرتجعة
        /// </summary>
        Returned = 4
    }

    /// <summary>
    /// طرق الدفع للفواتير
    /// </summary>
    public enum InvoicePaymentMethod
    {
        /// <summary>
        /// نقداً
        /// </summary>
        Cash = 1,

        /// <summary>
        /// بطاقة ائتمان
        /// </summary>
        CreditCard = 2,

        /// <summary>
        /// تحويل بنكي
        /// </summary>
        BankTransfer = 3,

        /// <summary>
        /// أقساط
        /// </summary>
        Installment = 4,

        /// <summary>
        /// طريقة أخرى
        /// </summary>
        Other = 5
    }
}
