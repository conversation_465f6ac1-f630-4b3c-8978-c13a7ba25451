using System;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج القسط
    /// </summary>
    public class Installment
    {
        public int Id { get; set; }

        [Required]
        public int OrderId { get; set; }
        public Order Order { get; set; }

        [Required]
        public int InstallmentPlanId { get; set; }
        public InstallmentPlan InstallmentPlan { get; set; }

        [Required]
        public int InstallmentNumber { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        public DateTime? PaymentDate { get; set; }

        [Required]
        public InstallmentStatus Status { get; set; }

        [MaxLength(200)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public enum InstallmentStatus
    {
        Pending = 0,
        Paid = 1,
        Overdue = 2,
        Cancelled = 3
    }
}
