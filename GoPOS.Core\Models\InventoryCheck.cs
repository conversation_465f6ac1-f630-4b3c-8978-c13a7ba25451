using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج الجرد
    /// </summary>
    public class InventoryCheck
    {
        public int Id { get; set; }

        [Required]
        public string CheckNumber { get; set; }

        [Required]
        public DateTime CheckDate { get; set; }

        [Required]
        public InventoryCheckStatus Status { get; set; }

        [Required]
        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; }

        public int UserId { get; set; }
        public User User { get; set; }

        public int? ApprovedByUserId { get; set; }
        public User ApprovedByUser { get; set; }

        public DateTime? ApprovedDate { get; set; }

        [MaxLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public ICollection<InventoryCheckItem> Items { get; set; }
        public ICollection<InventoryTransaction> InventoryTransactions { get; set; }
    }

    public enum InventoryCheckStatus
    {
        Draft = 0,       // مسودة
        InProgress = 1,  // قيد التنفيذ
        Completed = 2,   // مكتمل
        Approved = 3,    // معتمد
        Cancelled = 4    // ملغي
    }
}
