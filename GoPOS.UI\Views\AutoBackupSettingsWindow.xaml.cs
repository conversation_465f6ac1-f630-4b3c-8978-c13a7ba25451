using System;
using System.IO;
using System.Windows;
using System.Windows.Forms;
using GoPOS.Data.Services;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for AutoBackupSettingsWindow.xaml
    /// </summary>
    public partial class AutoBackupSettingsWindow : Window
    {
        private BackupSettings _settings;
        private string _defaultBackupDirectory;

        /// <summary>
        /// إنشاء نافذة إعدادات النسخ الاحتياطي التلقائي
        /// </summary>
        /// <param name="settings">إعدادات النسخ الاحتياطي الحالية</param>
        public AutoBackupSettingsWindow(BackupSettings settings = null)
        {
            InitializeComponent();
            
            // تحديد مجلد النسخ الاحتياطي الافتراضي
            _defaultBackupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
            
            // استخدام الإعدادات المقدمة أو إنشاء إعدادات جديدة
            _settings = settings ?? new BackupSettings();
            
            // تعبئة الحقول بالإعدادات الحالية
            LoadSettings();
        }

        /// <summary>
        /// تعبئة الحقول بالإعدادات الحالية
        /// </summary>
        private void LoadSettings()
        {
            // تمكين النسخ الاحتياطي التلقائي
            EnableAutoBackupCheckBox.IsChecked = true; // دائمًا ممكّن عند فتح النافذة
            
            // الفترة الزمنية
            foreach (System.Windows.Controls.ComboBoxItem item in IntervalHoursComboBox.Items)
            {
                if (item.Content.ToString() == _settings.IntervalHours.ToString())
                {
                    IntervalHoursComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تأخير البدء
            DelayStartupCheckBox.IsChecked = _settings.DelayStartup;
            
            // حذف النسخ الاحتياطية القديمة
            DeleteOldBackupsCheckBox.IsChecked = _settings.DeleteOldBackups;
            
            // عدد النسخ الاحتياطية للاحتفاظ بها
            foreach (System.Windows.Controls.ComboBoxItem item in KeepBackupsCountComboBox.Items)
            {
                if (item.Content.ToString() == _settings.KeepBackupsCount.ToString())
                {
                    KeepBackupsCountComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // إرسال إشعار بالبريد الإلكتروني
            SendEmailNotificationCheckBox.IsChecked = _settings.SendEmailNotification;
            
            // البريد الإلكتروني للإشعارات
            NotificationEmailTextBox.Text = _settings.NotificationEmail;
            
            // مجلد النسخ الاحتياطي
            BackupDirectoryTextBox.Text = string.IsNullOrEmpty(_settings.BackupDirectory) ? 
                _defaultBackupDirectory : _settings.BackupDirectory;
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void SaveSettings()
        {
            // الفترة الزمنية
            if (IntervalHoursComboBox.SelectedItem != null)
            {
                _settings.IntervalHours = int.Parse(
                    ((System.Windows.Controls.ComboBoxItem)IntervalHoursComboBox.SelectedItem).Content.ToString());
            }
            
            // تأخير البدء
            _settings.DelayStartup = DelayStartupCheckBox.IsChecked ?? true;
            
            // حذف النسخ الاحتياطية القديمة
            _settings.DeleteOldBackups = DeleteOldBackupsCheckBox.IsChecked ?? true;
            
            // عدد النسخ الاحتياطية للاحتفاظ بها
            if (KeepBackupsCountComboBox.SelectedItem != null)
            {
                _settings.KeepBackupsCount = int.Parse(
                    ((System.Windows.Controls.ComboBoxItem)KeepBackupsCountComboBox.SelectedItem).Content.ToString());
            }
            
            // إرسال إشعار بالبريد الإلكتروني
            _settings.SendEmailNotification = SendEmailNotificationCheckBox.IsChecked ?? false;
            
            // البريد الإلكتروني للإشعارات
            _settings.NotificationEmail = NotificationEmailTextBox.Text;
            
            // مجلد النسخ الاحتياطي
            _settings.BackupDirectory = BackupDirectoryTextBox.Text;
            
            // التحقق من وجود مجلد النسخ الاحتياطي وإنشائه إذا لم يكن موجودًا
            if (!string.IsNullOrEmpty(_settings.BackupDirectory) && !Directory.Exists(_settings.BackupDirectory))
            {
                try
                {
                    Directory.CreateDirectory(_settings.BackupDirectory);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show(
                        $"حدث خطأ أثناء إنشاء مجلد النسخ الاحتياطي: {ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// معالجة حدث النقر على زر الحفظ
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة الإدخال
                if (SendEmailNotificationCheckBox.IsChecked == true && string.IsNullOrWhiteSpace(NotificationEmailTextBox.Text))
                {
                    System.Windows.MessageBox.Show(
                        "الرجاء إدخال عنوان البريد الإلكتروني للإشعارات",
                        "تنبيه",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    NotificationEmailTextBox.Focus();
                    return;
                }
                
                // حفظ الإعدادات
                SaveSettings();
                
                // إغلاق النافذة
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة حدث النقر على زر الإلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// معالجة حدث النقر على زر استعراض مجلد النسخ الاحتياطي
        /// </summary>
        private void BrowseDirectoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مربع حوار اختيار المجلد
                FolderBrowserDialog folderDialog = new FolderBrowserDialog();
                folderDialog.Description = "اختر مجلد النسخ الاحتياطي";
                folderDialog.ShowNewFolderButton = true;
                
                // تعيين المجلد الافتراضي
                if (!string.IsNullOrEmpty(BackupDirectoryTextBox.Text) && Directory.Exists(BackupDirectoryTextBox.Text))
                {
                    folderDialog.SelectedPath = BackupDirectoryTextBox.Text;
                }
                else
                {
                    folderDialog.SelectedPath = _defaultBackupDirectory;
                }
                
                // عرض مربع الحوار
                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    BackupDirectoryTextBox.Text = folderDialog.SelectedPath;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء اختيار المجلد: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على إعدادات النسخ الاحتياطي
        /// </summary>
        public BackupSettings GetSettings()
        {
            return _settings;
        }
    }
}
