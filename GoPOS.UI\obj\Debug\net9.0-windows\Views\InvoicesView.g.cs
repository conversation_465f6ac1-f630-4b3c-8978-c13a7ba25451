﻿#pragma checksum "..\..\..\..\Views\InvoicesView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FF64BFC27543117000E5823A9C94782D42D0F3E9"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// InvoicesView
    /// </summary>
    public partial class InvoicesView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 31 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterPaymentComboBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FilterDatePicker;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoInvoicesMessage;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadingIndicator;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSalesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayInvoicesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodaySalesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidInvoicesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DailySalesReportButton;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MonthlySalesReportButton;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UnpaidInvoicesReportButton;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\Views\InvoicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintAllInvoicesButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/invoicesview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\InvoicesView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CreateInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 31 "..\..\..\..\Views\InvoicesView.xaml"
            this.CreateInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.CreateInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\Views\InvoicesView.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 96 "..\..\..\..\Views\InvoicesView.xaml"
            this.SearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.SearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\..\Views\InvoicesView.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.FilterStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 114 "..\..\..\..\Views\InvoicesView.xaml"
            this.FilterStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterStatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FilterPaymentComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 122 "..\..\..\..\Views\InvoicesView.xaml"
            this.FilterPaymentComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterPaymentComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FilterDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 131 "..\..\..\..\Views\InvoicesView.xaml"
            this.FilterDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.FilterDatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 137 "..\..\..\..\Views\InvoicesView.xaml"
            this.InvoicesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InvoicesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.NoInvoicesMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.LoadingIndicator = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.TotalInvoicesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TotalSalesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TodayInvoicesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.TodaySalesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.UnpaidInvoicesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.UnpaidAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.DailySalesReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\Views\InvoicesView.xaml"
            this.DailySalesReportButton.Click += new System.Windows.RoutedEventHandler(this.DailySalesReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.MonthlySalesReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 243 "..\..\..\..\Views\InvoicesView.xaml"
            this.MonthlySalesReportButton.Click += new System.Windows.RoutedEventHandler(this.MonthlySalesReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.UnpaidInvoicesReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\..\Views\InvoicesView.xaml"
            this.UnpaidInvoicesReportButton.Click += new System.Windows.RoutedEventHandler(this.UnpaidInvoicesReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.PrintAllInvoicesButton = ((System.Windows.Controls.Button)(target));
            
            #line 275 "..\..\..\..\Views\InvoicesView.xaml"
            this.PrintAllInvoicesButton.Click += new System.Windows.RoutedEventHandler(this.PrintAllInvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 9:
            
            #line 150 "..\..\..\..\Views\InvoicesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 151 "..\..\..\..\Views\InvoicesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 152 "..\..\..\..\Views\InvoicesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 12:
            
            #line 153 "..\..\..\..\Views\InvoicesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

