﻿<Window x:Class="GoPOS.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI"
        mc:Ignorable="d"
        Title="نظام نقاط البيع - GoPOS | تصميم GoLx" Height="768" Width="1024"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط القوائم -->
        <Menu Grid.Row="0" FontSize="14" x:Name="MainMenu">
            <MenuItem Header="ملف">
                <MenuItem Header="تسجيل الخروج" x:Name="LogoutMenuItem" Click="LogoutMenuItem_Click"/>
                <Separator/>
                <MenuItem Header="خروج" x:Name="ExitMenuItem" Click="ExitMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="المنتجات">
                <MenuItem Header="إدارة المنتجات" x:Name="ManageProductsMenuItem" Click="ManageProductsMenuItem_Click"/>
                <MenuItem Header="إدارة الفئات" x:Name="ManageCategoriesMenuItem" Click="ManageCategoriesMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="المبيعات">
                <MenuItem Header="نقطة البيع" x:Name="POSMenuItem" Click="POSMenuItem_Click"/>
                <MenuItem Header="الفواتير" x:Name="InvoicesMenuItem" Click="InvoicesMenuItem_Click"/>
                <MenuItem Header="المنصرفات" x:Name="ExpensesMenuItem" Click="ExpensesMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="العملاء">
                <MenuItem Header="إدارة العملاء" x:Name="ManageCustomersMenuItem" Click="ManageCustomersMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="التقارير">
                <MenuItem Header="تقرير المبيعات" x:Name="SalesReportMenuItem" Click="SalesReportMenuItem_Click"/>
                <MenuItem Header="تقرير المخزون" x:Name="InventoryReportMenuItem" Click="InventoryReportMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="الإعدادات">
                <MenuItem Header="إدارة المستخدمين" x:Name="ManageUsersMenuItem" Click="ManageUsersMenuItem_Click"/>
                <MenuItem Header="إعدادات النظام" x:Name="SystemSettingsMenuItem" Click="SystemSettingsMenuItem_Click"/>
            </MenuItem>
            <MenuItem Header="مساعدة">
                <MenuItem Header="المساعدة" x:Name="HelpMenuItem" Click="HelpMenuItem_Click"/>
                <Separator/>
                <MenuItem Header="حول البرنامج" x:Name="AboutMenuItem" Click="AboutMenuItem_Click"/>
            </MenuItem>
        </Menu>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- القائمة الجانبية -->
            <Border Grid.Column="0" Background="#F0F0F0" Margin="0,0,10,0" CornerRadius="8" BorderBrush="#DDDDDD" BorderThickness="1">
                <StackPanel x:Name="MenuPanel">
                    <TextBlock Text="القائمة الرئيسية" FontSize="18" FontWeight="Bold" Margin="10" Foreground="#2196F3"/>

                    <Button x:Name="DashboardButton" Content="لوحة المعلومات" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📊" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="POSButton" Content="نقطة البيع" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="💰" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="ProductsButton" Content="المنتجات" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📦" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="InventoryButton" Content="المخزون" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🏭" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="InventoryCheckButton" Content="الجرد الدوري" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="CustomersButton" Content="العملاء" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="👥" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="InstallmentsButton" Content="الأقساط" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="InvoicesButton" Content="الفواتير" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📝" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="ExpensesButton" Content="المنصرفات" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="💸" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="ReportsButton" Content="التقارير" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📈" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="SettingsButton" Content="الإعدادات" Height="50" Click="MenuButton_Click" Margin="5,2">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚙️" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </Border>

            <!-- منطقة العرض الرئيسية -->
            <Border Grid.Column="1" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="8">
                <Grid x:Name="ContentArea">
                    <!-- سيتم تحميل المحتوى ديناميكياً -->
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" x:Name="WelcomePanel" Visibility="Visible">
                        <TextBlock Text="مرحباً بك في نظام نقاط البيع GoPOS"
                                   FontSize="24"
                                   FontWeight="Bold"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="تصميم وتطوير: GoLx"
                                   FontSize="16"
                                   FontWeight="SemiBold"
                                   HorizontalAlignment="Center"
                                   Foreground="#2196F3"
                                   Margin="0,0,0,5"/>
                        <TextBlock Text="نظام متكامل لإدارة نقاط البيع والمخزون"
                                   FontSize="14"
                                   HorizontalAlignment="Center"
                                   Foreground="#666666"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- شريط الحالة -->
        <StatusBar Grid.Row="2" Background="#F0F0F0">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="👤" FontSize="14" Margin="0,0,5,0"/>
                    <TextBlock x:Name="UserNameText" Text="المستخدم: مدير النظام" FontWeight="SemiBold"/>
                </StackPanel>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔔" FontSize="14" Margin="0,0,5,0"/>
                    <TextBlock x:Name="NotificationsText" Text="الإشعارات: 0" Foreground="#2196F3" Cursor="Hand" MouseDown="Notifications_MouseDown"/>
                </StackPanel>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🕒" FontSize="14" Margin="0,0,5,0"/>
                    <TextBlock x:Name="DateTimeText" Text="التاريخ والوقت" FontWeight="SemiBold"/>
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="تصميم: GoLx" Margin="0,0,10,0" Foreground="#2196F3" FontWeight="SemiBold"/>
                    <TextBlock x:Name="VersionText" Text="الإصدار: 1.0.0"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
