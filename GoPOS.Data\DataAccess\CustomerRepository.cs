using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع العملاء
    /// </summary>
    public class CustomerRepository : IRepository<Customer>
    {
        private readonly DatabaseContext _dbContext;

        /// <summary>
        /// إنشاء مستودع العملاء
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public CustomerRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        /// <returns>قائمة بجميع العملاء</returns>
        public async Task<IEnumerable<Customer>> GetAllAsync()
        {
            string query = @"
                SELECT c.*, 
                    (SELECT COUNT(*) FROM Invoices WHERE CustomerId = c.Id) AS InvoiceCount,
                    (SELECT SUM(Total) FROM Invoices WHERE CustomerId = c.Id) AS TotalPurchases
                FROM Customers c
                ORDER BY c.Name";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return ConvertDataTableToCustomers(dataTable);
        }

        /// <summary>
        /// الحصول على عميل بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف العميل</param>
        /// <returns>العميل إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<Customer> GetByIdAsync(int id)
        {
            string query = @"
                SELECT c.*, 
                    (SELECT COUNT(*) FROM Invoices WHERE CustomerId = c.Id) AS InvoiceCount,
                    (SELECT SUM(Total) FROM Invoices WHERE CustomerId = c.Id) AS TotalPurchases
                FROM Customers c
                WHERE c.Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToCustomers(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        /// <param name="entity">العميل المراد إضافته</param>
        /// <returns>معرف العميل المضاف</returns>
        public async Task<int> AddAsync(Customer entity)
        {
            // التحقق من عدم وجود عميل بنفس رقم الهاتف
            if (!string.IsNullOrEmpty(entity.Phone))
            {
                string checkQuery = @"
                    SELECT COUNT(*)
                    FROM Customers
                    WHERE Phone = @Phone";

                SqlParameter[] checkParameters = new SqlParameter[]
                {
                    new SqlParameter("@Phone", entity.Phone)
                };

                object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
                int customerCount = Convert.ToInt32(result);

                if (customerCount > 0)
                {
                    throw new InvalidOperationException("رقم الهاتف موجود بالفعل");
                }
            }

            string query = @"
                INSERT INTO Customers (Name, Phone, Email, Address, Notes, IsActive)
                VALUES (@Name, @Phone, @Email, @Address, @Notes, @IsActive);
                SELECT SCOPE_IDENTITY();";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Name", entity.Name),
                new SqlParameter("@Phone", (object)entity.Phone ?? DBNull.Value),
                new SqlParameter("@Email", (object)entity.Email ?? DBNull.Value),
                new SqlParameter("@Address", (object)entity.Address ?? DBNull.Value),
                new SqlParameter("@Notes", (object)entity.Notes ?? DBNull.Value),
                new SqlParameter("@IsActive", entity.IsActive)
            };

            object insertResult = await _dbContext.ExecuteScalarAsync(query, parameters);
            return Convert.ToInt32(insertResult);
        }

        /// <summary>
        /// تحديث عميل موجود
        /// </summary>
        /// <param name="entity">العميل المراد تحديثه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateAsync(Customer entity)
        {
            // التحقق من عدم وجود عميل آخر بنفس رقم الهاتف
            if (!string.IsNullOrEmpty(entity.Phone))
            {
                string checkQuery = @"
                    SELECT COUNT(*)
                    FROM Customers
                    WHERE Phone = @Phone AND Id <> @Id";

                SqlParameter[] checkParameters = new SqlParameter[]
                {
                    new SqlParameter("@Phone", entity.Phone),
                    new SqlParameter("@Id", entity.Id)
                };

                object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
                int customerCount = Convert.ToInt32(result);

                if (customerCount > 0)
                {
                    throw new InvalidOperationException("رقم الهاتف موجود بالفعل");
                }
            }

            string query = @"
                UPDATE Customers
                SET Name = @Name,
                    Phone = @Phone,
                    Email = @Email,
                    Address = @Address,
                    Notes = @Notes,
                    IsActive = @IsActive
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", entity.Id),
                new SqlParameter("@Name", entity.Name),
                new SqlParameter("@Phone", (object)entity.Phone ?? DBNull.Value),
                new SqlParameter("@Email", (object)entity.Email ?? DBNull.Value),
                new SqlParameter("@Address", (object)entity.Address ?? DBNull.Value),
                new SqlParameter("@Notes", (object)entity.Notes ?? DBNull.Value),
                new SqlParameter("@IsActive", entity.IsActive)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// حذف عميل بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف العميل المراد حذفه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> DeleteAsync(int id)
        {
            // التحقق من عدم وجود فواتير مرتبطة بالعميل
            string checkQuery = @"
                SELECT COUNT(*)
                FROM Invoices
                WHERE CustomerId = @Id";

            SqlParameter[] checkParameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
            int invoiceCount = Convert.ToInt32(result);

            if (invoiceCount > 0)
            {
                throw new InvalidOperationException("لا يمكن حذف العميل لأنه مرتبط بفواتير");
            }

            // التحقق من عدم وجود خطط أقساط مرتبطة بالعميل
            string checkInstallmentsQuery = @"
                SELECT COUNT(*)
                FROM InstallmentPlans
                WHERE CustomerId = @Id";

            object installmentsResult = await _dbContext.ExecuteScalarAsync(checkInstallmentsQuery, checkParameters);
            int installmentCount = Convert.ToInt32(installmentsResult);

            if (installmentCount > 0)
            {
                throw new InvalidOperationException("لا يمكن حذف العميل لأنه مرتبط بخطط أقساط");
            }

            string query = "DELETE FROM Customers WHERE Id = @Id";
            return await _dbContext.ExecuteNonQueryAsync(query, checkParameters);
        }

        /// <summary>
        /// البحث عن عملاء بناءً على معايير معينة
        /// </summary>
        /// <param name="predicate">دالة التصفية</param>
        /// <returns>قائمة بالعملاء الذين يطابقون معايير البحث</returns>
        public async Task<IEnumerable<Customer>> FindAsync(Func<Customer, bool> predicate)
        {
            var customers = await GetAllAsync();
            return customers.Where(predicate);
        }

        /// <summary>
        /// البحث عن عملاء بواسطة النص
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة بالعملاء الذين يطابقون نص البحث</returns>
        public async Task<IEnumerable<Customer>> SearchAsync(string searchText)
        {
            string query = @"
                SELECT c.*, 
                    (SELECT COUNT(*) FROM Invoices WHERE CustomerId = c.Id) AS InvoiceCount,
                    (SELECT SUM(Total) FROM Invoices WHERE CustomerId = c.Id) AS TotalPurchases
                FROM Customers c
                WHERE c.Name LIKE @SearchText
                OR c.Phone LIKE @SearchText
                OR c.Email LIKE @SearchText
                OR c.Address LIKE @SearchText
                ORDER BY c.Name";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@SearchText", $"%{searchText}%")
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToCustomers(dataTable);
        }

        /// <summary>
        /// الحصول على عميل بواسطة رقم الهاتف
        /// </summary>
        /// <param name="phone">رقم الهاتف</param>
        /// <returns>العميل إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<Customer> GetByPhoneAsync(string phone)
        {
            string query = @"
                SELECT c.*, 
                    (SELECT COUNT(*) FROM Invoices WHERE CustomerId = c.Id) AS InvoiceCount,
                    (SELECT SUM(Total) FROM Invoices WHERE CustomerId = c.Id) AS TotalPurchases
                FROM Customers c
                WHERE c.Phone = @Phone";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Phone", phone)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToCustomers(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// الحصول على فواتير العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة بفواتير العميل</returns>
        public async Task<DataTable> GetCustomerInvoicesAsync(int customerId)
        {
            string query = @"
                SELECT i.Id, i.InvoiceNumber, i.Date, i.Total, i.Status,
                    (SELECT COUNT(*) FROM InvoiceItems WHERE InvoiceId = i.Id) AS ItemCount
                FROM Invoices i
                WHERE i.CustomerId = @CustomerId
                ORDER BY i.Date DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@CustomerId", customerId)
            };

            return await _dbContext.ExecuteQueryAsync(query, parameters);
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة عملاء
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة العملاء</returns>
        private IEnumerable<Customer> ConvertDataTableToCustomers(DataTable dataTable)
        {
            List<Customer> customers = new List<Customer>();

            foreach (DataRow row in dataTable.Rows)
            {
                Customer customer = new Customer
                {
                    Id = Convert.ToInt32(row["Id"]),
                    Name = row["Name"].ToString(),
                    IsActive = Convert.ToBoolean(row["IsActive"])
                };

                if (row["Phone"] != DBNull.Value)
                {
                    customer.Phone = row["Phone"].ToString();
                }

                if (row["Email"] != DBNull.Value)
                {
                    customer.Email = row["Email"].ToString();
                }

                if (row["Address"] != DBNull.Value)
                {
                    customer.Address = row["Address"].ToString();
                }

                if (row["Notes"] != DBNull.Value)
                {
                    customer.Notes = row["Notes"].ToString();
                }

                if (row["InvoiceCount"] != DBNull.Value)
                {
                    customer.InvoiceCount = Convert.ToInt32(row["InvoiceCount"]);
                }

                if (row["TotalPurchases"] != DBNull.Value)
                {
                    customer.TotalPurchases = Convert.ToDecimal(row["TotalPurchases"]);
                }

                customers.Add(customer);
            }

            return customers;
        }
    }
}
