<Window x:Class="GoPOS.UI.Views.CategoryEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إدارة الفئات" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock x:Name="TitleTextBlock" Grid.Row="0" Text="إضافة فئة جديدة" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- اسم الفئة -->
            <StackPanel Grid.Row="0" Margin="0,0,0,15">
                <TextBlock Text="اسم الفئة:" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="30"/>
            </StackPanel>

            <!-- كود الفئة -->
            <StackPanel Grid.Row="1" Margin="0,0,0,15">
                <TextBlock Text="كود الفئة:" Margin="0,0,0,5"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBox x:Name="CodeTextBox" Grid.Column="0" Height="30"/>
                    <Button x:Name="GenerateCodeButton" Grid.Column="1" Content="توليد" Width="60" Height="30" Margin="5,0,0,0" Click="GenerateCodeButton_Click"/>
                </Grid>
            </StackPanel>

            <!-- لون الفئة -->
            <StackPanel Grid.Row="2" Margin="0,0,0,15">
                <TextBlock Text="لون الفئة:" Margin="0,0,0,5"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBox x:Name="ColorTextBox" Grid.Column="0" Height="30" Text="#2196F3"/>
                    <Border x:Name="ColorPreview" Grid.Column="1" Width="30" Height="30" Margin="5,0,0,0" Background="#2196F3" BorderBrush="#CCCCCC" BorderThickness="1"/>
                </Grid>
            </StackPanel>

            <!-- حالة الفئة -->
            <StackPanel Grid.Row="3" Margin="0,0,0,15">
                <CheckBox x:Name="IsActiveCheckBox" Content="فئة نشطة" IsChecked="True"/>
            </StackPanel>

            <!-- وصف الفئة -->
            <StackPanel Grid.Row="4" Margin="0,0,0,0">
                <TextBlock Text="وصف الفئة:" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" Height="100" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
            </StackPanel>
        </Grid>

        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button x:Name="SaveButton" Grid.Column="1" Content="حفظ" Width="100" Height="35" Margin="0,0,10,0" Click="SaveButton_Click" Background="#4CAF50" Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
            
            <Button x:Name="CancelButton" Grid.Column="2" Content="إلغاء" Width="100" Height="35" Click="CancelButton_Click" Background="#F44336" Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </Grid>
    </Grid>
</Window>
