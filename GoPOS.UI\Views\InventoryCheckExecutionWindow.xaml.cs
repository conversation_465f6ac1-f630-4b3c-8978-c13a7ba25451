using System;
using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InventoryCheckExecutionWindow.xaml
    /// </summary>
    public partial class InventoryCheckExecutionWindow : Window
    {
        private InventoryCheckViewModel _inventoryCheck;
        private ObservableCollection<InventoryCheckItemViewModel> _checkItems;
        private ObservableCollection<InventoryCheckItemViewModel> _filteredItems;
        private InventoryCheckItemViewModel _selectedItem;
        private int _totalItems = 0;
        private int _checkedItems = 0;

        public InventoryCheckExecutionWindow(InventoryCheckViewModel inventoryCheck)
        {
            InitializeComponent();
            
            _inventoryCheck = inventoryCheck;
            
            LoadInventoryCheckData();
            LoadCheckItems();
            UpdateProgress();
        }

        private void LoadInventoryCheckData()
        {
            // تحميل بيانات الجرد
            CheckNumberTextBox.Text = _inventoryCheck.CheckNumber;
            CheckDateTextBox.Text = _inventoryCheck.CheckDate.ToString("yyyy/MM/dd");
            WarehouseTextBox.Text = _inventoryCheck.WarehouseName;
        }

        private void LoadCheckItems()
        {
            // تحميل عناصر الجرد (سيتم استبدالها بقراءة من قاعدة البيانات)
            _checkItems = new ObservableCollection<InventoryCheckItemViewModel>();
            Random random = new Random();

            // بيانات تجريبية
            for (int i = 1; i <= 50; i++)
            {
                int categoryIndex = random.Next(0, 3);
                string productName = GetRandomProductName(categoryIndex);
                
                int systemQuantity = random.Next(10, 100);
                int? actualQuantity = null;
                int? difference = null;
                bool isChecked = false;
                SolidColorBrush differenceColor = Brushes.Black;

                _checkItems.Add(new InventoryCheckItemViewModel
                {
                    Id = i,
                    Barcode = $"100{i:D4}",
                    Name = productName,
                    SystemQuantity = systemQuantity,
                    ActualQuantity = actualQuantity,
                    Difference = difference,
                    IsChecked = isChecked,
                    Notes = "",
                    DifferenceColor = differenceColor
                });
            }

            _totalItems = _checkItems.Count;
            _filteredItems = new ObservableCollection<InventoryCheckItemViewModel>(_checkItems);
            ProductsDataGrid.ItemsSource = _filteredItems;
        }

        private string GetRandomProductName(int categoryIndex)
        {
            string[] drinks = { "قهوة", "شاي", "عصير برتقال", "عصير تفاح", "ماء معدني", "مشروب غازي", "عصير مانجو" };
            string[] fastFood = { "برجر", "بيتزا", "ساندويتش", "دجاج مقلي", "بطاطس", "ناجتس", "شاورما" };
            string[] desserts = { "كيك", "آيس كريم", "كوكيز", "دونات", "تشيز كيك", "بودينج", "كنافة" };

            Random random = new Random();
            
            if (categoryIndex == 0)
                return drinks[random.Next(drinks.Length)];
            else if (categoryIndex == 1)
                return fastFood[random.Next(fastFood.Length)];
            else
                return desserts[random.Next(desserts.Length)];
        }

        private void UpdateProgress()
        {
            // حساب عدد العناصر التي تم جردها
            _checkedItems = 0;
            foreach (var item in _checkItems)
            {
                if (item.IsChecked)
                    _checkedItems++;
            }
            
            // تحديث شريط التقدم
            double progressPercentage = _totalItems > 0 ? (double)_checkedItems / _totalItems * 100 : 0;
            ProgressBar.Value = progressPercentage;
            ProgressTextBlock.Text = $"{progressPercentage:F0}% ({_checkedItems}/{_totalItems})";
            
            // تحديث حالة زر إكمال الجرد
            CompleteButton.IsEnabled = _checkedItems == _totalItems;
        }

        private void FilterItems()
        {
            string searchText = SearchTextBox.Text.Trim().ToLower();
            int filterIndex = FilterComboBox.SelectedIndex;
            
            _filteredItems.Clear();
            
            foreach (var item in _checkItems)
            {
                bool matchesFilter = true;
                
                // تطبيق فلتر البحث
                if (!string.IsNullOrEmpty(searchText))
                {
                    if (!item.Name.ToLower().Contains(searchText) && !item.Barcode.ToLower().Contains(searchText))
                        matchesFilter = false;
                }
                
                // تطبيق فلتر الحالة
                switch (filterIndex)
                {
                    case 1: // تم الجرد
                        if (!item.IsChecked)
                            matchesFilter = false;
                        break;
                    case 2: // لم يتم الجرد
                        if (item.IsChecked)
                            matchesFilter = false;
                        break;
                    case 3: // يوجد فرق
                        if (!item.Difference.HasValue || item.Difference.Value == 0)
                            matchesFilter = false;
                        break;
                }
                
                if (matchesFilter)
                    _filteredItems.Add(item);
            }
        }

        private void ProductsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedItem = ProductsDataGrid.SelectedItem as InventoryCheckItemViewModel;
        }

        private void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                ScanBarcode();
            }
        }

        private void ScanButton_Click(object sender, RoutedEventArgs e)
        {
            ScanBarcode();
        }

        private void ScanBarcode()
        {
            string barcode = BarcodeTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(barcode))
            {
                MessageBox.Show("الرجاء إدخال الباركود", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                BarcodeTextBox.Focus();
                return;
            }
            
            // البحث عن المنتج بالباركود
            InventoryCheckItemViewModel foundItem = null;
            
            foreach (var item in _checkItems)
            {
                if (item.Barcode == barcode)
                {
                    foundItem = item;
                    break;
                }
            }
            
            if (foundItem == null)
            {
                MessageBox.Show("لم يتم العثور على منتج بهذا الباركود", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                BarcodeTextBox.SelectAll();
                return;
            }
            
            // عرض نافذة إدخال الكمية
            QuantityInputWindow inputWindow = new QuantityInputWindow(foundItem.SystemQuantity);
            inputWindow.Owner = this;
            
            if (inputWindow.ShowDialog() == true)
            {
                int actualQuantity = inputWindow.Quantity;
                
                // تحديث بيانات المنتج
                foundItem.ActualQuantity = actualQuantity;
                foundItem.Difference = actualQuantity - foundItem.SystemQuantity;
                foundItem.IsChecked = true;
                
                // تحديث لون الفرق
                if (foundItem.Difference < 0)
                    foundItem.DifferenceColor = Brushes.Red;
                else if (foundItem.Difference > 0)
                    foundItem.DifferenceColor = Brushes.Green;
                else
                    foundItem.DifferenceColor = Brushes.Black;
                
                // إضافة ملاحظة إذا كان هناك فرق
                if (foundItem.Difference != 0)
                    foundItem.Notes = "يرجى التحقق من الكمية";
                
                // تحديث شريط التقدم
                UpdateProgress();
                
                // تحديث العرض
                ProductsDataGrid.Items.Refresh();
                
                // مسح حقل الباركود والتركيز عليه
                BarcodeTextBox.Clear();
                BarcodeTextBox.Focus();
            }
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                FilterItems();
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            FilterItems();
        }

        private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterItems();
        }

        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        private void ActualQuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            TextBox textBox = sender as TextBox;
            if (textBox != null)
            {
                InventoryCheckItemViewModel item = textBox.Tag as InventoryCheckItemViewModel;
                if (item != null && int.TryParse(textBox.Text, out int actualQuantity))
                {
                    // تحديث بيانات المنتج
                    item.ActualQuantity = actualQuantity;
                    item.Difference = actualQuantity - item.SystemQuantity;
                    item.IsChecked = true;
                    
                    // تحديث لون الفرق
                    if (item.Difference < 0)
                        item.DifferenceColor = Brushes.Red;
                    else if (item.Difference > 0)
                        item.DifferenceColor = Brushes.Green;
                    else
                        item.DifferenceColor = Brushes.Black;
                    
                    // إضافة ملاحظة إذا كان هناك فرق
                    if (item.Difference != 0)
                        item.Notes = "يرجى التحقق من الكمية";
                    
                    // تحديث شريط التقدم
                    UpdateProgress();
                }
            }
        }

        private void CompleteButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إكمال عملية الجرد؟", "تأكيد الإكمال", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // تحديث حالة الجرد إلى مكتمل
                _inventoryCheck.StatusId = 2; // مكتمل
                _inventoryCheck.StatusText = "مكتمل";
                _inventoryCheck.StatusColor = new SolidColorBrush(Colors.Green);
                
                MessageBox.Show("تم إكمال عملية الجرد بنجاح", "تم الإكمال", MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // حفظ بيانات الجرد (سيتم استبداله بالحفظ في قاعدة البيانات)
            MessageBox.Show("تم حفظ بيانات الجرد بنجاح", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إلغاء عملية الجرد؟ سيتم فقدان جميع التغييرات غير المحفوظة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                DialogResult = false;
                Close();
            }
        }
    }
}
