<Window x:Class="GoPOS.UI.Views.InstallmentPlanWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إعداد خطة الأقساط" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="8" Margin="0,0,0,20">
            <TextBlock Text="إعداد خطة الأقساط" FontSize="24" FontWeight="Bold" Foreground="White" Padding="15,10" HorizontalAlignment="Center"/>
        </Border>

        <!-- معلومات الطلب والعميل -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- العمود الأيمن - معلومات الطلب -->
            <Border Grid.Column="0" Background="#F5F5F5" CornerRadius="8" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,10,0">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الطلب:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <TextBlock x:Name="OrderIdTextBlock" Grid.Row="0" Grid.Column="1" Text="1001" Margin="10,0,0,15" FontWeight="Bold" Foreground="#2196F3"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="العميل:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <ComboBox x:Name="CustomerComboBox" Grid.Row="1" Grid.Column="1" Margin="10,0,0,15" DisplayMemberPath="Name" SelectionChanged="CustomerComboBox_SelectionChanged" Padding="5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي الطلب:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <TextBlock x:Name="TotalAmountTextBlock" Grid.Row="2" Grid.Column="1" Text="0.00" Margin="10,0,0,15" FontWeight="Bold" Foreground="#4CAF50"/>

                    <TextBlock Grid.Row="3" Grid.Column="0" Text="الدفعة المقدمة:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <Border Grid.Row="3" Grid.Column="1" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="10,0,0,15">
                        <TextBox x:Name="DownPaymentTextBox" BorderThickness="0" Padding="5" TextChanged="DownPaymentTextBox_TextChanged"/>
                    </Border>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="المبلغ المتبقي:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="RemainingAmountTextBlock" Grid.Row="4" Grid.Column="1" Text="0.00" Margin="10,0,0,5" FontWeight="Bold" Foreground="#FF9800"/>
                </Grid>
            </Border>

            <!-- العمود الأيسر - إعدادات الأقساط -->
            <Border Grid.Column="1" Background="#F5F5F5" CornerRadius="8" BorderBrush="#DDDDDD" BorderThickness="1" Margin="10,0,0,0">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="عدد الأقساط:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <ComboBox x:Name="NumberOfInstallmentsComboBox" Grid.Row="0" Grid.Column="1" Margin="10,0,0,15" SelectionChanged="NumberOfInstallmentsComboBox_SelectionChanged" Padding="5">
                        <ComboBoxItem Content="3"/>
                        <ComboBoxItem Content="6"/>
                        <ComboBoxItem Content="9"/>
                        <ComboBoxItem Content="12"/>
                        <ComboBoxItem Content="18"/>
                        <ComboBoxItem Content="24"/>
                    </ComboBox>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="تكرار الدفع:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <ComboBox x:Name="FrequencyComboBox" Grid.Row="1" Grid.Column="1" Margin="10,0,0,15" SelectionChanged="FrequencyComboBox_SelectionChanged" Padding="5">
                        <ComboBoxItem Content="أسبوعي"/>
                        <ComboBoxItem Content="نصف شهري"/>
                        <ComboBoxItem Content="شهري" IsSelected="True"/>
                        <ComboBoxItem Content="ربع سنوي"/>
                    </ComboBox>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ البدء:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <DatePicker x:Name="StartDatePicker" Grid.Row="2" Grid.Column="1" Margin="10,0,0,15" SelectedDateChanged="StartDatePicker_SelectedDateChanged" Padding="5"/>

                    <TextBlock Grid.Row="3" Grid.Column="0" Text="قيمة القسط:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <TextBlock x:Name="InstallmentAmountTextBlock" Grid.Row="3" Grid.Column="1" Text="0.00" Margin="10,0,0,15" FontWeight="Bold" Foreground="#2196F3"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="نسبة الفائدة:" FontWeight="SemiBold" Margin="0,0,0,15"/>
                    <Border Grid.Row="4" Grid.Column="1" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="10,0,0,15">
                        <TextBox x:Name="InterestRateTextBox" BorderThickness="0" Padding="5" TextChanged="InterestRateTextBox_TextChanged" Text="0"/>
                    </Border>

                    <TextBlock Grid.Row="5" Grid.Column="0" Text="يوم الاستحقاق:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="DueDayComboBox" Grid.Row="5" Grid.Column="1" Margin="10,0,0,5" SelectionChanged="DueDayComboBox_SelectionChanged" Padding="5">
                        <ComboBoxItem Content="1"/>
                        <ComboBoxItem Content="5"/>
                        <ComboBoxItem Content="10"/>
                        <ComboBoxItem Content="15"/>
                        <ComboBoxItem Content="20"/>
                        <ComboBoxItem Content="25"/>
                        <ComboBoxItem Content="نفس يوم البدء" IsSelected="True"/>
                    </ComboBox>
                </Grid>
            </Border>
        </Grid>

        <!-- جدول الأقساط -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Border Grid.Row="0" Background="#2196F3" CornerRadius="8,8,0,0" Margin="0,0,0,0">
                <TextBlock Text="جدول الأقساط" FontSize="16" FontWeight="Bold" Foreground="White" Padding="15,10" HorizontalAlignment="Center"/>
            </Border>

            <DataGrid x:Name="InstallmentsDataGrid"
                      Grid.Row="1"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      AlternatingRowBackground="#F5F5F5"
                      BorderBrush="#DDDDDD"
                      BorderThickness="1"
                      RowHeight="35"
                      HeadersVisibility="Column"
                      GridLinesVisibility="Horizontal"
                      HorizontalGridLinesBrush="#EEEEEE">
                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#2196F3"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Padding" Value="10,0"/>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم القسط" Binding="{Binding InstallmentNumber}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat={}{0:N2}}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="Foreground" Value="#2196F3"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat={}{0:yyyy/MM/dd}}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="4" Padding="5,2" Margin="5">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}" Value="0">
                                                    <Setter Property="Background" Value="#FFC107"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="1">
                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="2">
                                                    <Setter Property="Background" Value="#F44336"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Text="{Binding StatusText}"
                                               Foreground="White"
                                               FontWeight="SemiBold"
                                               HorizontalAlignment="Center"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="3" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <Button x:Name="PrintPreviewButton"
                        Width="120"
                        Height="40"
                        Margin="0,0,10,0"
                        Click="PrintPreviewButton_Click"
                        Background="#673AB7"
                        Foreground="White">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🖨️" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="معاينة الطباعة" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="AdjustDatesButton"
                        Width="120"
                        Height="40"
                        Margin="0,0,10,0"
                        Click="AdjustDatesButton_Click"
                        Background="#FF9800"
                        Foreground="White">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📅" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="تعديل التواريخ" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="SaveButton"
                        Width="150"
                        Height="40"
                        Margin="0,0,10,0"
                        Click="SaveButton_Click"
                        Background="#4CAF50"
                        Foreground="White"
                        FontWeight="Bold">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="حفظ خطة الأقساط" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="CancelButton"
                        Width="100"
                        Height="40"
                        Click="CancelButton_Click"
                        Background="#F44336"
                        Foreground="White">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="❌" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
