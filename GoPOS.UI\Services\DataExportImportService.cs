using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Diagnostics;
using ClosedXML.Excel;
using System.Globalization;

namespace GoPOS.UI.Services
{
    /// <summary>
    /// خدمة تصدير واستيراد البيانات
    /// </summary>
    public class DataExportImportService
    {
        private readonly string _connectionString;
        private readonly string _exportPath;

        public DataExportImportService(string connectionString = null, string exportPath = null)
        {
            // تعيين سلسلة الاتصال
            _connectionString = connectionString ?? GetDefaultConnectionString();

            // تعيين مسار التصدير الافتراضي
            _exportPath = exportPath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "GoPOS",
                "Exports");

            // التأكد من وجود المسار
            EnsureDirectoryExists(_exportPath);
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال الافتراضية
        /// </summary>
        private string GetDefaultConnectionString()
        {
            string serverName = "localhost";
            string databaseName = "GoPOS";
            string username = "sa";
            string password = "";
            bool useWindowsAuth = true;

            // قراءة إعدادات قاعدة البيانات من ملف الإعدادات إذا كان موجوداً
            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GoPOS");
            string settingsPath = Path.Combine(appDataPath, "settings.json");
            if (File.Exists(settingsPath))
            {
                try
                {
                    string settingsJson = File.ReadAllText(settingsPath);
                    // يمكن استخدام مكتبة JSON لقراءة الإعدادات
                    // هنا نستخدم طريقة بسيطة للتوضيح
                    if (settingsJson.Contains("\"ServerName\":"))
                    {
                        int start = settingsJson.IndexOf("\"ServerName\":") + 14;
                        int end = settingsJson.IndexOf("\"", start);
                        serverName = settingsJson.Substring(start, end - start);
                    }

                    if (settingsJson.Contains("\"DatabaseName\":"))
                    {
                        int start = settingsJson.IndexOf("\"DatabaseName\":") + 16;
                        int end = settingsJson.IndexOf("\"", start);
                        databaseName = settingsJson.Substring(start, end - start);
                    }

                    if (settingsJson.Contains("\"UseWindowsAuth\":"))
                    {
                        int start = settingsJson.IndexOf("\"UseWindowsAuth\":") + 17;
                        useWindowsAuth = settingsJson.Substring(start, 5).Contains("true");
                    }

                    if (!useWindowsAuth)
                    {
                        if (settingsJson.Contains("\"Username\":"))
                        {
                            int start = settingsJson.IndexOf("\"Username\":") + 12;
                            int end = settingsJson.IndexOf("\"", start);
                            username = settingsJson.Substring(start, end - start);
                        }

                        if (settingsJson.Contains("\"Password\":"))
                        {
                            int start = settingsJson.IndexOf("\"Password\":") + 12;
                            int end = settingsJson.IndexOf("\"", start);
                            password = settingsJson.Substring(start, end - start);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"حدث خطأ أثناء قراءة ملف الإعدادات: {ex.Message}");
                }
            }

            // إنشاء سلسلة الاتصال
            if (useWindowsAuth)
            {
                return $"Server={serverName};Database={databaseName};Integrated Security=True;";
            }
            else
            {
                return $"Server={serverName};Database={databaseName};User Id={username};Password={password};";
            }
        }

        /// <summary>
        /// التأكد من وجود المسار
        /// </summary>
        private void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        /// <summary>
        /// الحصول على قائمة الجداول في قاعدة البيانات
        /// </summary>
        public async Task<List<string>> GetTablesAsync()
        {
            List<string> tables = new List<string>();

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    string query = @"
                        SELECT TABLE_NAME
                        FROM INFORMATION_SCHEMA.TABLES
                        WHERE TABLE_TYPE = 'BASE TABLE'
                        ORDER BY TABLE_NAME";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                tables.Add(reader.GetString(0));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء الحصول على قائمة الجداول: {ex.Message}", ex);
            }

            return tables;
        }

        /// <summary>
        /// تصدير جدول إلى ملف Excel
        /// </summary>
        public async Task<string> ExportTableToExcelAsync(string tableName, string fileName = null)
        {
            try
            {
                // التحقق من اسم الجدول
                if (string.IsNullOrWhiteSpace(tableName))
                {
                    throw new ArgumentException("اسم الجدول غير صالح");
                }

                // إنشاء اسم الملف إذا لم يتم توفيره
                if (string.IsNullOrWhiteSpace(fileName))
                {
                    fileName = $"{tableName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                }

                // إضافة امتداد الملف إذا لم يكن موجوداً
                if (!fileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    fileName += ".xlsx";
                }

                // إنشاء مسار الملف
                string filePath = Path.Combine(_exportPath, fileName);

                // الحصول على البيانات من قاعدة البيانات
                DataTable dataTable = await GetTableDataAsync(tableName);

                // تصدير البيانات إلى ملف Excel
                using (XLWorkbook workbook = new XLWorkbook())
                {
                    // إضافة ورقة عمل جديدة
                    var worksheet = workbook.Worksheets.Add(tableName);

                    // إضافة العناوين
                    for (int i = 0; i < dataTable.Columns.Count; i++)
                    {
                        worksheet.Cell(1, i + 1).Value = dataTable.Columns[i].ColumnName;
                        worksheet.Cell(1, i + 1).Style.Font.Bold = true;
                    }

                    // إضافة البيانات
                    for (int row = 0; row < dataTable.Rows.Count; row++)
                    {
                        for (int col = 0; col < dataTable.Columns.Count; col++)
                        {
                            worksheet.Cell(row + 2, col + 1).Value = dataTable.Rows[row][col].ToString();
                        }
                    }

                    // تنسيق الجدول
                    var range = worksheet.Range(1, 1, dataTable.Rows.Count + 1, dataTable.Columns.Count);
                    var table = range.CreateTable();

                    // حفظ الملف
                    workbook.SaveAs(filePath);
                }

                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء تصدير الجدول إلى Excel: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تصدير جدول إلى ملف CSV
        /// </summary>
        public async Task<string> ExportTableToCsvAsync(string tableName, string fileName = null)
        {
            try
            {
                // التحقق من اسم الجدول
                if (string.IsNullOrWhiteSpace(tableName))
                {
                    throw new ArgumentException("اسم الجدول غير صالح");
                }

                // إنشاء اسم الملف إذا لم يتم توفيره
                if (string.IsNullOrWhiteSpace(fileName))
                {
                    fileName = $"{tableName}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                }

                // إضافة امتداد الملف إذا لم يكن موجوداً
                if (!fileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
                {
                    fileName += ".csv";
                }

                // إنشاء مسار الملف
                string filePath = Path.Combine(_exportPath, fileName);

                // الحصول على البيانات من قاعدة البيانات
                DataTable dataTable = await GetTableDataAsync(tableName);

                // تصدير البيانات إلى ملف CSV
                using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // كتابة العناوين
                    for (int i = 0; i < dataTable.Columns.Count; i++)
                    {
                        writer.Write(dataTable.Columns[i].ColumnName);
                        if (i < dataTable.Columns.Count - 1)
                        {
                            writer.Write(",");
                        }
                    }
                    writer.WriteLine();

                    // كتابة البيانات
                    foreach (DataRow row in dataTable.Rows)
                    {
                        for (int i = 0; i < dataTable.Columns.Count; i++)
                        {
                            string value = row[i].ToString();
                            // معالجة القيم التي تحتوي على فواصل
                            if (value.Contains(",") || value.Contains("\"") || value.Contains("\n"))
                            {
                                value = $"\"{value.Replace("\"", "\"\"")}\"";
                            }
                            writer.Write(value);
                            if (i < dataTable.Columns.Count - 1)
                            {
                                writer.Write(",");
                            }
                        }
                        writer.WriteLine();
                    }
                }

                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء تصدير الجدول إلى CSV: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على بيانات جدول
        /// </summary>
        public async Task<DataTable> GetTableDataAsync(string tableName)
        {
            DataTable dataTable = new DataTable();

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // تحديد عدد الصفوف للمعاينة (الحد الأقصى 1000 صف)
                    string query = $"SELECT TOP 1000 * FROM [{tableName}]";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء الحصول على بيانات الجدول: {ex.Message}", ex);
            }

            return dataTable;
        }

        /// <summary>
        /// استيراد بيانات من ملف Excel إلى جدول
        /// </summary>
        public async Task<int> ImportExcelToTableAsync(string filePath, string tableName, bool truncateTable = false)
        {
            try
            {
                // التحقق من وجود الملف
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("ملف Excel غير موجود", filePath);
                }

                // التحقق من اسم الجدول
                if (string.IsNullOrWhiteSpace(tableName))
                {
                    throw new ArgumentException("اسم الجدول غير صالح");
                }

                // قراءة بيانات ملف Excel
                DataTable dataTable = new DataTable();

                using (XLWorkbook workbook = new XLWorkbook(filePath))
                {
                    // استخدام الورقة الأولى
                    var worksheet = workbook.Worksheet(1);
                    var firstRow = worksheet.FirstRowUsed();

                    // قراءة أسماء الأعمدة
                    int columnCount = worksheet.LastColumnUsed().ColumnNumber();
                    for (int i = 1; i <= columnCount; i++)
                    {
                        string columnName = firstRow.Cell(i).Value.ToString();
                        dataTable.Columns.Add(columnName);
                    }

                    // قراءة البيانات
                    var rows = worksheet.RowsUsed().Skip(1); // تخطي الصف الأول (العناوين)
                    foreach (var row in rows)
                    {
                        DataRow dataRow = dataTable.NewRow();
                        for (int i = 1; i <= columnCount; i++)
                        {
                            dataRow[i - 1] = row.Cell(i).Value.ToString();
                        }
                        dataTable.Rows.Add(dataRow);
                    }
                }

                // استيراد البيانات إلى قاعدة البيانات
                return await ImportDataToTableAsync(dataTable, tableName, truncateTable);
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء استيراد ملف Excel: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// استيراد بيانات من ملف CSV إلى جدول
        /// </summary>
        public async Task<int> ImportCsvToTableAsync(string filePath, string tableName, bool truncateTable = false)
        {
            try
            {
                // التحقق من وجود الملف
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("ملف CSV غير موجود", filePath);
                }

                // التحقق من اسم الجدول
                if (string.IsNullOrWhiteSpace(tableName))
                {
                    throw new ArgumentException("اسم الجدول غير صالح");
                }

                // قراءة بيانات ملف CSV
                DataTable dataTable = new DataTable();

                using (StreamReader reader = new StreamReader(filePath, Encoding.UTF8))
                {
                    // قراءة السطر الأول (العناوين)
                    string headerLine = reader.ReadLine();
                    if (headerLine == null)
                    {
                        throw new Exception("ملف CSV فارغ");
                    }

                    // تقسيم العناوين
                    string[] headers = ParseCsvLine(headerLine);
                    foreach (string header in headers)
                    {
                        dataTable.Columns.Add(header);
                    }

                    // قراءة البيانات
                    while (!reader.EndOfStream)
                    {
                        string line = reader.ReadLine();
                        if (string.IsNullOrWhiteSpace(line))
                        {
                            continue;
                        }

                        string[] values = ParseCsvLine(line);
                        DataRow dataRow = dataTable.NewRow();
                        for (int i = 0; i < values.Length && i < headers.Length; i++)
                        {
                            dataRow[i] = values[i];
                        }
                        dataTable.Rows.Add(dataRow);
                    }
                }

                // استيراد البيانات إلى قاعدة البيانات
                return await ImportDataToTableAsync(dataTable, tableName, truncateTable);
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء استيراد ملف CSV: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحليل سطر CSV
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            List<string> result = new List<string>();
            bool inQuotes = false;
            StringBuilder field = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '\"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '\"')
                    {
                        // مزدوج مزدوج داخل اقتباس
                        field.Append('\"');
                        i++;
                    }
                    else
                    {
                        // تبديل حالة الاقتباس
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // نهاية الحقل
                    result.Add(field.ToString());
                    field.Clear();
                }
                else
                {
                    // حرف عادي
                    field.Append(c);
                }
            }

            // إضافة الحقل الأخير
            result.Add(field.ToString());

            return result.ToArray();
        }

        /// <summary>
        /// استيراد بيانات إلى جدول
        /// </summary>
        private async Task<int> ImportDataToTableAsync(DataTable dataTable, string tableName, bool truncateTable)
        {
            int rowsAffected = 0;

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // حذف البيانات الموجودة إذا تم تحديد ذلك
                    if (truncateTable)
                    {
                        string truncateQuery = $"TRUNCATE TABLE [{tableName}]";
                        using (SqlCommand command = new SqlCommand(truncateQuery, connection))
                        {
                            await command.ExecuteNonQueryAsync();
                        }
                    }

                    // الحصول على هيكل الجدول
                    DataTable schemaTable = await GetTableSchemaAsync(tableName, connection);

                    // إنشاء أمر الإدراج
                    StringBuilder insertQuery = new StringBuilder($"INSERT INTO [{tableName}] (");
                    StringBuilder valuesQuery = new StringBuilder("VALUES (");

                    // إضافة أسماء الأعمدة وقيم المعلمات
                    List<string> columnNames = new List<string>();
                    foreach (DataColumn column in dataTable.Columns)
                    {
                        // التحقق من وجود العمود في الجدول
                        if (schemaTable.AsEnumerable().Any(r => string.Equals(r.Field<string>("ColumnName"), column.ColumnName, StringComparison.OrdinalIgnoreCase)))
                        {
                            columnNames.Add(column.ColumnName);
                            insertQuery.Append($"[{column.ColumnName}], ");
                            valuesQuery.Append($"@{column.ColumnName}, ");
                        }
                    }

                    // إزالة الفاصلة الأخيرة
                    insertQuery.Length -= 2;
                    valuesQuery.Length -= 2;

                    insertQuery.Append(") ");
                    valuesQuery.Append(")");

                    string query = insertQuery.ToString() + valuesQuery.ToString();

                    // إدراج البيانات
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // إضافة المعلمات
                        foreach (string columnName in columnNames)
                        {
                            command.Parameters.Add($"@{columnName}", SqlDbType.NVarChar);
                        }

                        // إدراج كل صف
                        foreach (DataRow row in dataTable.Rows)
                        {
                            foreach (string columnName in columnNames)
                            {
                                command.Parameters[$"@{columnName}"].Value = row[columnName] ?? DBNull.Value;
                            }

                            rowsAffected += await command.ExecuteNonQueryAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء استيراد البيانات إلى الجدول: {ex.Message}", ex);
            }

            return rowsAffected;
        }

        /// <summary>
        /// الحصول على هيكل الجدول
        /// </summary>
        private async Task<DataTable> GetTableSchemaAsync(string tableName, SqlConnection connection)
        {
            string query = $"SELECT COLUMN_NAME AS ColumnName, DATA_TYPE AS DataType FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{tableName}'";

            using (SqlCommand command = new SqlCommand(query, connection))
            {
                using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                {
                    DataTable schemaTable = new DataTable();
                    adapter.Fill(schemaTable);
                    return schemaTable;
                }
            }
        }
    }
}
