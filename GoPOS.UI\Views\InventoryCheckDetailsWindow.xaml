<Window x:Class="GoPOS.UI.Views.InventoryCheckDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تفاصيل الجرد" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="تفاصيل الجرد" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- معلومات الجرد -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- رقم الجرد -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الجرد:" VerticalAlignment="Center" Margin="0,5,10,5"/>
            <TextBox x:Name="CheckNumberTextBox" Grid.Row="0" Grid.Column="1" Height="30" Margin="0,5" IsReadOnly="{Binding IsReadOnly}"/>

            <!-- تاريخ الجرد -->
            <TextBlock Grid.Row="0" Grid.Column="2" Text="تاريخ الجرد:" VerticalAlignment="Center" Margin="20,5,10,5"/>
            <DatePicker x:Name="CheckDatePicker" Grid.Row="0" Grid.Column="3" Height="30" Margin="0,5" IsEnabled="{Binding IsEditable}"/>

            <!-- المخزن -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="المخزن:" VerticalAlignment="Center" Margin="0,5,10,5"/>
            <ComboBox x:Name="WarehouseComboBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,5" IsEnabled="{Binding IsEditable}">
                <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                <ComboBoxItem Content="مخزن الفرع 1"/>
                <ComboBoxItem Content="مخزن الفرع 2"/>
            </ComboBox>

            <!-- الحالة -->
            <TextBlock Grid.Row="1" Grid.Column="2" Text="الحالة:" VerticalAlignment="Center" Margin="20,5,10,5"/>
            <ComboBox x:Name="StatusComboBox" Grid.Row="1" Grid.Column="3" Height="30" Margin="0,5" IsEnabled="{Binding IsEditable}">
                <ComboBoxItem Content="مسودة" IsSelected="True"/>
                <ComboBoxItem Content="قيد التنفيذ"/>
                <ComboBoxItem Content="مكتمل"/>
                <ComboBoxItem Content="معتمد"/>
                <ComboBoxItem Content="ملغي"/>
            </ComboBox>

            <!-- ملاحظات -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="ملاحظات:" VerticalAlignment="Top" Margin="0,5,10,5"/>
            <TextBox x:Name="NotesTextBox" Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" Height="60" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,5" IsReadOnly="{Binding IsReadOnly}"/>
        </Grid>

        <!-- قائمة المنتجات -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط أدوات المنتجات -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10" Visibility="{Binding ProductsToolbarVisibility}">
                <Button x:Name="AddProductButton" Content="إضافة منتج" Width="120" Height="30" Margin="0,0,10,0" Click="AddProductButton_Click"/>
                <Button x:Name="RemoveProductButton" Content="إزالة المنتج" Width="120" Height="30" Margin="0,0,10,0" Click="RemoveProductButton_Click"/>
                <Button x:Name="ImportProductsButton" Content="استيراد المنتجات" Width="120" Height="30" Click="ImportProductsButton_Click"/>
            </StackPanel>

            <!-- جدول المنتجات -->
            <DataGrid x:Name="ProductsDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="{Binding IsReadOnly}" SelectionMode="Single" SelectionChanged="ProductsDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                    <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*"/>
                    <DataGridTextColumn Header="الكمية في النظام" Binding="{Binding SystemQuantity}" Width="120"/>
                    <DataGridTextColumn Header="الكمية الفعلية" Binding="{Binding ActualQuantity}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding DifferenceColor}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الفرق" Binding="{Binding Difference}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding DifferenceColor}"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridCheckBoxColumn Header="تم الجرد" Binding="{Binding IsChecked}" Width="80"/>
                    <DataGridTemplateColumn Header="ملاحظات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Notes}" TextWrapping="Wrap"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}" TextWrapping="Wrap"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="حفظ" Width="100" Height="40" Margin="0,0,10,0" Click="SaveButton_Click" Visibility="{Binding SaveButtonVisibility}"/>
            <Button x:Name="CloseButton" Content="إغلاق" Width="100" Height="40" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
