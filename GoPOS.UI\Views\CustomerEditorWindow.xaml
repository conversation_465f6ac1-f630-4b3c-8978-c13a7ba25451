<Window x:Class="GoPOS.UI.Views.CustomerEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تحرير العميل" Height="450" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock x:Name="TitleTextBlock" Grid.Row="0" Text="إضافة عميل جديد" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <StackPanel Grid.Row="1">
            <TextBlock Text="الاسم:" Margin="0,0,0,5"/>
            <TextBox x:Name="NameTextBox" Height="30" Margin="0,0,0,15"/>

            <TextBlock Text="رقم الهاتف:" Margin="0,0,0,5"/>
            <TextBox x:Name="PhoneTextBox" Height="30" Margin="0,0,0,15"/>

            <TextBlock Text="البريد الإلكتروني:" Margin="0,0,0,5"/>
            <TextBox x:Name="EmailTextBox" Height="30" Margin="0,0,0,15"/>

            <TextBlock Text="العنوان:" Margin="0,0,0,5"/>
            <TextBox x:Name="AddressTextBox" Height="40" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>

            <TextBlock Text="ملاحظات:" Margin="0,0,0,5"/>
            <TextBox x:Name="NotesTextBox" Height="40" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,15"/>
        </StackPanel>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="حفظ" Width="100" Height="40" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="40" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
