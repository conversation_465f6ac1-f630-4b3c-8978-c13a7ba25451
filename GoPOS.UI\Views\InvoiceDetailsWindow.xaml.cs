using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using GoPOS.Core.Models;
using GoPOS.UI.Services;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InvoiceDetailsWindow.xaml
    /// </summary>
    public partial class InvoiceDetailsWindow : Window
    {
        private InvoiceViewModel _invoice;
        private ObservableCollection<InvoiceItemViewModel> _invoiceItems;
        private ObservableCollection<InvoiceInstallmentViewModel> _installments;

        public InvoiceDetailsWindow(InvoiceViewModel invoice)
        {
            InitializeComponent();
            _invoice = invoice ?? throw new ArgumentNullException(nameof(invoice), "الفاتورة غير موجودة");

            // تهيئة البيانات
            InitializeData();
        }

        private void InitializeData()
        {
            try
            {
                // تعيين عنوان النافذة
                Title = $"تفاصيل الفاتورة رقم {_invoice.InvoiceNumber}";

                // تعيين معلومات الفاتورة
                InvoiceNumberText.Text = $"فاتورة رقم: {_invoice.InvoiceNumber}";
                InvoiceStatusText.Text = $"الحالة: {_invoice.StatusText}";

                // تعيين قيم معلومات الفاتورة
                InvoiceNumberValueText.Text = _invoice.InvoiceNumber;
                InvoiceDateValueText.Text = _invoice.Date.ToString("yyyy/MM/dd");
                InvoiceStatusValueText.Text = _invoice.StatusText;
                PaymentMethodValueText.Text = _invoice.PaymentMethodText;
                UserNameValueText.Text = _invoice.UserName;
                NotesValueText.Text = _invoice.Notes ?? "لا توجد ملاحظات";

                // تعيين معلومات العميل
                CustomerNameValueText.Text = _invoice.CustomerName;
                CustomerIdValueText.Text = _invoice.CustomerId.ToString();
                PaymentStatusValueText.Text = _invoice.IsPaid ? "مدفوعة بالكامل" : "غير مدفوعة بالكامل";
                InvoiceTypeValueText.Text = _invoice.IsInstallment ? "فاتورة أقساط" : "فاتورة عادية";

                // تعيين معلومات الدفع
                SubtotalValueText.Text = _invoice.Subtotal.ToString("N2");
                TaxValueText.Text = _invoice.TaxAmount.ToString("N2");
                TotalValueText.Text = _invoice.Total.ToString("N2");
                PaidAmountValueText.Text = _invoice.PaidAmount.ToString("N2");
                RemainingAmountValueText.Text = _invoice.RemainingAmount.ToString("N2");

                // تعيين معلومات الخصم
                string discountText = _invoice.DiscountAmount.ToString("N2");
                if (_invoice.IsDiscountPercentage && _invoice.DiscountPercentage > 0)
                {
                    discountText += $" ({_invoice.DiscountPercentage}%)";
                }
                DiscountValueText.Text = discountText;

                // تهيئة عناصر الفاتورة
                _invoiceItems = new ObservableCollection<InvoiceItemViewModel>();
                InvoiceItemsDataGrid.ItemsSource = _invoiceItems;

                // إضافة عناصر الفاتورة (محاكاة)
                LoadInvoiceItems();

                // إذا كانت فاتورة أقساط، تهيئة معلومات الأقساط
                if (_invoice.IsInstallment)
                {
                    InstallmentInfoPanel.Visibility = Visibility.Visible;
                    _installments = new ObservableCollection<InvoiceInstallmentViewModel>();
                    InstallmentsDataGrid.ItemsSource = _installments;

                    // إضافة الأقساط (محاكاة)
                    LoadInstallments();
                }
                else
                {
                    InstallmentInfoPanel.Visibility = Visibility.Collapsed;
                }

                // تعيين حالة زر التعديل
                EditButton.Visibility = _invoice.CanEdit;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadInvoiceItems()
        {
            // محاكاة عناصر الفاتورة
            List<InvoiceItemViewModel> items = new List<InvoiceItemViewModel>();

            // إضافة عناصر محاكاة بناءً على إجمالي الفاتورة
            if (_invoice.Total > 0)
            {
                // العنصر الأول
                items.Add(new InvoiceItemViewModel
                {
                    Index = 1,
                    ProductId = 1,
                    ProductName = "منتج 1",
                    Barcode = "1234567890",
                    UnitPrice = 50.00m,
                    Quantity = 2,
                    DiscountAmount = 0,
                    TaxAmount = 15.00m,
                    Total = 115.00m
                });

                // العنصر الثاني (إذا كان المجموع أكبر من 150)
                if (_invoice.Total > 150)
                {
                    items.Add(new InvoiceItemViewModel
                    {
                        Index = 2,
                        ProductId = 2,
                        ProductName = "منتج 2",
                        Barcode = "0987654321",
                        UnitPrice = 25.00m,
                        Quantity = 1,
                        DiscountAmount = 0,
                        TaxAmount = 3.75m,
                        Total = 28.75m
                    });
                }

                // العنصر الثالث (إذا كان المجموع أكبر من 300)
                if (_invoice.Total > 300)
                {
                    items.Add(new InvoiceItemViewModel
                    {
                        Index = 3,
                        ProductId = 3,
                        ProductName = "منتج 3",
                        Barcode = "5678901234",
                        UnitPrice = 100.00m,
                        Quantity = 1,
                        DiscountAmount = 10.00m,
                        TaxAmount = 13.50m,
                        Total = 103.50m
                    });
                }
            }

            // إضافة العناصر إلى القائمة
            foreach (var item in items)
            {
                _invoiceItems.Add(item);
            }
        }

        private void LoadInstallments()
        {
            // محاكاة الأقساط
            if (_invoice.IsInstallment)
            {
                // عدد الأقساط (3 أقساط)
                int installmentsCount = 3;
                decimal installmentAmount = _invoice.Total / installmentsCount;

                for (int i = 0; i < installmentsCount; i++)
                {
                    bool isPaid = i == 0; // القسط الأول مدفوع فقط

                    _installments.Add(new InvoiceInstallmentViewModel
                    {
                        Index = i + 1,
                        DueDate = _invoice.Date.AddMonths(i),
                        Amount = installmentAmount,
                        Status = isPaid ? InstallmentStatus.Paid : InstallmentStatus.Pending,
                        StatusText = isPaid ? "مدفوع" : "قيد الانتظار",
                        PaymentDate = isPaid ? _invoice.Date : null,
                        Notes = isPaid ? "تم الدفع" : "لم يتم الدفع بعد"
                    });
                }
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // طباعة الفاتورة باستخدام خدمة الطباعة
                PrintService.PrintInvoice(_invoice);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من إمكانية تعديل الفاتورة
                if (_invoice.CanEdit != Visibility.Visible)
                {
                    MessageBox.Show("لا يمكن تعديل هذه الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // محاكاة تعديل الفاتورة
                MessageBox.Show($"سيتم فتح شاشة تعديل الفاتورة رقم {_invoice.InvoiceNumber}", "تعديل الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);

                // هنا يمكن إضافة كود لفتح شاشة نقطة البيع لتعديل الفاتورة
                // MainWindow mainWindow = Window.GetWindow(this) as MainWindow;
                // mainWindow?.LoadView(new POSTerminalView(_invoice));

                // إغلاق النافذة الحالية
                DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ReturnInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من إمكانية إرجاع الفاتورة
                if (_invoice.Status != InvoiceStatus.Completed)
                {
                    MessageBox.Show("لا يمكن إرجاع هذه الفاتورة. يمكن إرجاع الفواتير المكتملة فقط.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // فتح نافذة إرجاع الفاتورة
                ReturnInvoiceWindow returnWindow = new ReturnInvoiceWindow(_invoice);
                returnWindow.Owner = this;

                // عرض النافذة وانتظار النتيجة
                bool? result = returnWindow.ShowDialog();

                // إذا تم إنشاء فاتورة مرتجعة بنجاح
                if (result == true)
                {
                    // إغلاق النافذة الحالية مع إرجاع نتيجة نجاح
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إرجاع الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة
            DialogResult = false;
            Close();
        }
    }

    /// <summary>
    /// نموذج عرض عنصر الفاتورة
    /// </summary>
    public class InvoiceItemViewModel
    {
        public int Index { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string Barcode { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Quantity { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal Total { get; set; }
    }

    /// <summary>
    /// نموذج عرض القسط في الفاتورة
    /// </summary>
    public class InvoiceInstallmentViewModel
    {
        public int Index { get; set; }
        public DateTime DueDate { get; set; }
        public decimal Amount { get; set; }
        public InstallmentStatus Status { get; set; }
        public string StatusText { get; set; }
        public DateTime? PaymentDate { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// حالات القسط
    /// </summary>
    public enum InstallmentStatus
    {
        /// <summary>
        /// قيد الانتظار
        /// </summary>
        Pending = 1,

        /// <summary>
        /// مدفوع
        /// </summary>
        Paid = 2,

        /// <summary>
        /// متأخر
        /// </summary>
        Late = 3,

        /// <summary>
        /// ملغى
        /// </summary>
        Cancelled = 4
    }
}
