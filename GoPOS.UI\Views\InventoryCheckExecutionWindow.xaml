<Window x:Class="GoPOS.UI.Views.InventoryCheckExecutionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تنفيذ الجرد" Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="تنفيذ الجرد" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- معلومات الجرد -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- رقم الجرد -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الجرد:" VerticalAlignment="Center" Margin="0,5,10,5"/>
            <TextBox x:Name="CheckNumberTextBox" Grid.Row="0" Grid.Column="1" Height="30" Margin="0,5" IsReadOnly="True"/>

            <!-- تاريخ الجرد -->
            <TextBlock Grid.Row="0" Grid.Column="2" Text="تاريخ الجرد:" VerticalAlignment="Center" Margin="20,5,10,5"/>
            <TextBox x:Name="CheckDateTextBox" Grid.Row="0" Grid.Column="3" Height="30" Margin="0,5" IsReadOnly="True"/>

            <!-- المخزن -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="المخزن:" VerticalAlignment="Center" Margin="0,5,10,5"/>
            <TextBox x:Name="WarehouseTextBox" Grid.Row="1" Grid.Column="1" Height="30" Margin="0,5" IsReadOnly="True"/>

            <!-- التقدم -->
            <TextBlock Grid.Row="1" Grid.Column="2" Text="التقدم:" VerticalAlignment="Center" Margin="20,5,10,5"/>
            <Grid Grid.Row="1" Grid.Column="3" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <ProgressBar x:Name="ProgressBar" Grid.Column="0" Height="20" Minimum="0" Maximum="100" Value="0"/>
                <TextBlock x:Name="ProgressTextBlock" Grid.Column="1" Text="0%" Margin="5,0,0,0" VerticalAlignment="Center"/>
            </Grid>
        </Grid>

        <!-- قائمة المنتجات -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط أدوات المنتجات -->
            <Grid Grid.Row="0" Margin="0,0,0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="مسح الباركود:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBox x:Name="BarcodeTextBox" Width="200" Height="30" KeyDown="BarcodeTextBox_KeyDown"/>
                    <Button x:Name="ScanButton" Content="مسح" Width="80" Height="30" Margin="5,0,0,0" Click="ScanButton_Click"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <ComboBox x:Name="FilterComboBox" Width="150" Height="30" Margin="0,0,5,0" SelectionChanged="FilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="الكل" IsSelected="True"/>
                        <ComboBoxItem Content="تم الجرد"/>
                        <ComboBoxItem Content="لم يتم الجرد"/>
                        <ComboBoxItem Content="يوجد فرق"/>
                    </ComboBox>
                    <TextBox x:Name="SearchTextBox" Width="200" Height="30" Margin="0,0,5,0" KeyDown="SearchTextBox_KeyDown"/>
                    <Button x:Name="SearchButton" Content="بحث" Width="80" Height="30" Click="SearchButton_Click"/>
                </StackPanel>
            </Grid>

            <!-- جدول المنتجات -->
            <DataGrid x:Name="ProductsDataGrid" Grid.Row="1" AutoGenerateColumns="False" SelectionMode="Single" SelectionChanged="ProductsDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*" IsReadOnly="True"/>
                    <DataGridTextColumn Header="الكمية في النظام" Binding="{Binding SystemQuantity}" Width="120" IsReadOnly="True"/>
                    <DataGridTemplateColumn Header="الكمية الفعلية" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding ActualQuantity}" Foreground="{Binding DifferenceColor}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <TextBox Text="{Binding ActualQuantity, UpdateSourceTrigger=PropertyChanged}" PreviewTextInput="NumberValidationTextBox" TextChanged="ActualQuantityTextBox_TextChanged" Tag="{Binding}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="الفرق" Binding="{Binding Difference}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="{Binding DifferenceColor}"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridCheckBoxColumn Header="تم الجرد" Binding="{Binding IsChecked}" Width="80"/>
                    <DataGridTemplateColumn Header="ملاحظات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Notes}" TextWrapping="Wrap"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                        <DataGridTemplateColumn.CellEditingTemplate>
                            <DataTemplate>
                                <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}" TextWrapping="Wrap"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellEditingTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button x:Name="CompleteButton" Content="إكمال الجرد" Width="120" Height="40" Margin="0,0,10,0" Click="CompleteButton_Click"/>
            <Button x:Name="SaveButton" Content="حفظ" Width="100" Height="40" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="40" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
