using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using Microsoft.VisualBasic;
using System.Threading.Tasks;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for ProductEditorWindow.xaml
    /// </summary>
    public partial class ProductEditorWindow : Window
    {
        private ProductItemViewModel _product;
        private bool _isEditMode;
        private string _imagePath = string.Empty;

        // مستودعات قاعدة البيانات
        private DatabaseContext _dbContext;
        private ProductRepository _productRepository;
        private CategoryRepository _categoryRepository;

        public ProductEditorWindow()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _productRepository = new ProductRepository(_dbContext);
            _categoryRepository = new CategoryRepository(_dbContext);

            _isEditMode = false;
            _product = new ProductItemViewModel
            {
                Name = "",
                Code = "",
                Barcode = "",
                CategoryName = "",
                CostPrice = 0,
                Price = 0,
                DiscountPrice = null,
                MinimumQuantity = 0,
                IsAvailable = true
            };
            LoadCategoriesAsync();

            // إضافة معالجات الأحداث للحقول
            CostPriceTextBox.TextChanged += PriceTextBox_TextChanged;
            PriceTextBox.TextChanged += PriceTextBox_TextChanged;
        }

        public ProductEditorWindow(ProductItemViewModel product)
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _productRepository = new ProductRepository(_dbContext);
            _categoryRepository = new CategoryRepository(_dbContext);

            _isEditMode = true;
            _product = product;
            TitleTextBlock.Text = "تعديل المنتج";
            LoadCategoriesAsync();
            LoadProductData();

            // إضافة معالجات الأحداث للحقول
            CostPriceTextBox.TextChanged += PriceTextBox_TextChanged;
            PriceTextBox.TextChanged += PriceTextBox_TextChanged;
        }

        private async void LoadCategoriesAsync()
        {
            try
            {
                // تحميل الفئات من قاعدة البيانات
                var categories = await _categoryRepository.GetAllAsync();
                var categoryViewModels = new List<CategoryViewModel>();

                foreach (var category in categories)
                {
                    categoryViewModels.Add(new CategoryViewModel
                    {
                        Id = category.Id,
                        Name = category.Name
                    });
                }

                CategoryComboBox.ItemsSource = categoryViewModels;

                // تحديد الفئة الأولى افتراضيًا للمنتجات الجديدة
                if (!_isEditMode && categoryViewModels.Count > 0)
                {
                    CategoryComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                // في حالة الخطأ، استخدم فئات افتراضية
                var defaultCategories = new List<CategoryViewModel>
                {
                    new CategoryViewModel { Id = 1, Name = "مشروبات" },
                    new CategoryViewModel { Id = 2, Name = "وجبات سريعة" },
                    new CategoryViewModel { Id = 3, Name = "حلويات" }
                };
                CategoryComboBox.ItemsSource = defaultCategories;

                if (!_isEditMode)
                {
                    CategoryComboBox.SelectedIndex = 0;
                }
            }
        }

        private void LoadProductData()
        {
            try
            {
                // تعبئة حقول النموذج بالبيانات الحالية
                NameTextBox.Text = _product.Name;
                CodeTextBox.Text = _product.Code;
                BarcodeTextBox.Text = _product.Barcode;
                CostPriceTextBox.Text = _product.CostPrice.ToString("N2");
                PriceTextBox.Text = _product.Price.ToString("N2");
                QuantityTextBox.Text = _product.Quantity.ToString();

                // تعبئة الحقول الإضافية
                DescriptionTextBox.Text = _product.Description ?? string.Empty;
                DiscountPriceTextBox.Text = _product.DiscountPrice.HasValue ? _product.DiscountPrice.Value.ToString("N2") : string.Empty;
                MinimumQuantityTextBox.Text = _product.MinimumQuantity.ToString();
                IsAvailableCheckBox.IsChecked = _product.IsAvailable;

                // حساب هامش الربح
                CalculateProfitMargin();

                // تحديد الفئة المناسبة
                foreach (CategoryViewModel category in CategoryComboBox.Items)
                {
                    if (category.Id == _product.CategoryId)
                    {
                        CategoryComboBox.SelectedItem = category;
                        break;
                    }
                }

                // تحميل الصورة إذا كانت متوفرة (سيتم تنفيذه لاحقًا)
                // if (_product.ImageData != null)
                // {
                //     LoadImageFromBytes(_product.ImageData);
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PriceTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            CalculateProfitMargin();
        }

        private void CalculateProfitMargin()
        {
            try
            {
                if (decimal.TryParse(CostPriceTextBox.Text, out decimal costPrice) &&
                    decimal.TryParse(PriceTextBox.Text, out decimal sellingPrice) &&
                    costPrice > 0)
                {
                    decimal profitMargin = ((sellingPrice - costPrice) / costPrice) * 100;
                    ProfitMarginTextBlock.Text = $"هامش الربح: {profitMargin:N2}%";

                    // تغيير لون النص حسب هامش الربح
                    if (profitMargin < 0)
                    {
                        ProfitMarginTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(244, 67, 54)); // أحمر
                    }
                    else if (profitMargin < 10)
                    {
                        ProfitMarginTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 152, 0)); // برتقالي
                    }
                    else
                    {
                        ProfitMarginTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(76, 175, 80)); // أخضر
                    }
                }
                else
                {
                    ProfitMarginTextBlock.Text = "هامش الربح: 0%";
                    ProfitMarginTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(97, 97, 97)); // رمادي
                }
            }
            catch
            {
                ProfitMarginTextBlock.Text = "هامش الربح: 0%";
                ProfitMarginTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(97, 97, 97)); // رمادي
            }
        }

        private void CalculatePriceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (decimal.TryParse(CostPriceTextBox.Text, out decimal costPrice) && costPrice > 0)
                {
                    // فتح نافذة لإدخال نسبة الربح
                    string profitPercentageStr = Microsoft.VisualBasic.Interaction.InputBox("أدخل نسبة الربح المطلوبة (%)", "حساب سعر البيع", "30");

                    if (decimal.TryParse(profitPercentageStr, out decimal profitPercentage))
                    {
                        // حساب سعر البيع بناءً على نسبة الربح
                        decimal sellingPrice = costPrice * (1 + (profitPercentage / 100));

                        // تقريب السعر لأقرب 0.5 أو 0.25 ريال
                        sellingPrice = Math.Ceiling(sellingPrice * 4) / 4;

                        // تحديث حقل سعر البيع
                        PriceTextBox.Text = sellingPrice.ToString("N2");
                    }
                }
                else
                {
                    MessageBox.Show("الرجاء إدخال سعر تكلفة صحيح أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    CostPriceTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حساب سعر البيع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // توليد باركود عشوائي مكون من 13 رقم (EAN-13)
                Random random = new Random();
                string barcode = "6";  // بداية الباركود برقم 6 (للمنتجات المحلية)

                // إضافة 11 رقم عشوائي
                for (int i = 0; i < 11; i++)
                {
                    barcode += random.Next(0, 10).ToString();
                }

                // حساب رقم التحقق (Check Digit)
                int sum = 0;
                for (int i = 0; i < 12; i++)
                {
                    int digit = int.Parse(barcode[i].ToString());
                    sum += (i % 2 == 0) ? digit : digit * 3;
                }

                int checkDigit = (10 - (sum % 10)) % 10;
                barcode += checkDigit.ToString();

                // تحديث حقل الباركود
                BarcodeTextBox.Text = barcode;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء توليد الباركود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SelectImageButton_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "ملفات الصور|*.jpg;*.jpeg;*.png;*.gif|كل الملفات|*.*",
                Title = "اختيار صورة المنتج"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _imagePath = openFileDialog.FileName;
                try
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(_imagePath);
                    bitmap.EndInit();
                    ProductImage.Source = bitmap;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الصورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // تعطيل زر الحفظ لمنع النقر المتكرر
            SaveButton.IsEnabled = false;

            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                {
                    SaveButton.IsEnabled = true;
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // جمع البيانات من النموذج
                _product.Name = NameTextBox.Text.Trim();
                _product.Code = CodeTextBox.Text.Trim();
                _product.Barcode = BarcodeTextBox.Text.Trim();
                _product.CostPrice = decimal.Parse(CostPriceTextBox.Text);
                _product.Price = decimal.Parse(PriceTextBox.Text);
                _product.Quantity = int.Parse(QuantityTextBox.Text);
                _product.Description = DescriptionTextBox.Text.Trim();
                _product.IsAvailable = IsAvailableCheckBox.IsChecked ?? true;

                // تعيين سعر الخصم إذا تم إدخاله
                if (!string.IsNullOrWhiteSpace(DiscountPriceTextBox.Text))
                {
                    _product.DiscountPrice = decimal.Parse(DiscountPriceTextBox.Text);
                }
                else
                {
                    _product.DiscountPrice = null;
                }

                // تعيين الحد الأدنى للكمية
                if (!string.IsNullOrWhiteSpace(MinimumQuantityTextBox.Text))
                {
                    _product.MinimumQuantity = int.Parse(MinimumQuantityTextBox.Text);
                }
                else
                {
                    _product.MinimumQuantity = 0;
                }

                // التأكد من وجود فئة محددة
                if (CategoryComboBox.SelectedItem != null)
                {
                    CategoryViewModel selectedCategory = (CategoryViewModel)CategoryComboBox.SelectedItem;
                    _product.CategoryId = selectedCategory.Id;
                    _product.CategoryName = selectedCategory.Name;
                }
                else
                {
                    throw new InvalidOperationException("لم يتم تحديد فئة للمنتج");
                }

                // حفظ البيانات في قاعدة البيانات
                bool success = await SaveProductToDatabase();

                if (success)
                {
                    if (_isEditMode)
                    {
                        MessageBox.Show("تم تحديث المنتج بنجاح", "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("تمت إضافة المنتج بنجاح", "تمت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                    }

                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("حدث خطأ أثناء حفظ المنتج في قاعدة البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private bool ValidateInput()
        {
            try
            {
                // التحقق من اسم المنتج
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم المنتج", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    NameTextBox.Focus();
                    return false;
                }

                // التحقق من كود المنتج
                if (string.IsNullOrWhiteSpace(CodeTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال كود المنتج", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    CodeTextBox.Focus();
                    return false;
                }

                // التحقق من باركود المنتج
                if (string.IsNullOrWhiteSpace(BarcodeTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال باركود المنتج", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    BarcodeTextBox.Focus();
                    return false;
                }

                // التحقق من سعر التكلفة
                if (string.IsNullOrWhiteSpace(CostPriceTextBox.Text) || !decimal.TryParse(CostPriceTextBox.Text, out decimal costPrice) || costPrice < 0)
                {
                    MessageBox.Show("الرجاء إدخال سعر تكلفة صحيح", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    CostPriceTextBox.Focus();
                    return false;
                }

                // التحقق من سعر البيع
                if (string.IsNullOrWhiteSpace(PriceTextBox.Text) || !decimal.TryParse(PriceTextBox.Text, out decimal price) || price < 0)
                {
                    MessageBox.Show("الرجاء إدخال سعر بيع صحيح", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    PriceTextBox.Focus();
                    return false;
                }

                // التأكد من أن سعر البيع أكبر من سعر التكلفة
                if (price < costPrice)
                {
                    if (MessageBox.Show("سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟", "تحذير", MessageBoxButton.YesNo, MessageBoxImage.Warning) == MessageBoxResult.No)
                    {
                        PriceTextBox.Focus();
                        return false;
                    }
                }

                // التحقق من كمية المنتج
                if (string.IsNullOrWhiteSpace(QuantityTextBox.Text) || !int.TryParse(QuantityTextBox.Text, out int quantity) || quantity < 0)
                {
                    MessageBox.Show("الرجاء إدخال كمية صحيحة", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    QuantityTextBox.Focus();
                    return false;
                }

                // التحقق من فئة المنتج
                if (CategoryComboBox.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار فئة للمنتج", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    CategoryComboBox.Focus();
                    return false;
                }

                // التحقق من سعر الخصم (إذا تم إدخاله)
                if (!string.IsNullOrWhiteSpace(DiscountPriceTextBox.Text))
                {
                    if (!decimal.TryParse(DiscountPriceTextBox.Text, out decimal discountPrice) || discountPrice < 0)
                    {
                        MessageBox.Show("الرجاء إدخال سعر خصم صحيح", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                        DiscountPriceTextBox.Focus();
                        return false;
                    }

                    // التأكد من أن سعر الخصم أقل من السعر الأصلي
                    if (discountPrice >= price)
                    {
                        MessageBox.Show("يجب أن يكون سعر الخصم أقل من السعر الأصلي", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                        DiscountPriceTextBox.Focus();
                        return false;
                    }
                }

                // التحقق من الحد الأدنى للكمية
                if (!string.IsNullOrWhiteSpace(MinimumQuantityTextBox.Text))
                {
                    if (!int.TryParse(MinimumQuantityTextBox.Text, out int minQuantity) || minQuantity < 0)
                    {
                        MessageBox.Show("الرجاء إدخال حد أدنى صحيح للكمية", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                        MinimumQuantityTextBox.Focus();
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التحقق من البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private async Task<bool> SaveProductToDatabase()
        {
            try
            {
                // إنشاء كائن المنتج لقاعدة البيانات
                var product = new Product
                {
                    Name = _product.Name,
                    Code = _product.Code,
                    Barcode = _product.Barcode,
                    CategoryId = _product.CategoryId,
                    CostPrice = _product.CostPrice,
                    Price = _product.Price,
                    Quantity = _product.Quantity,
                    Description = _product.Description,
                    DiscountPrice = _product.DiscountPrice,
                    MinimumQuantity = _product.MinimumQuantity,
                    IsActive = _product.IsAvailable,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                if (_isEditMode)
                {
                    // تحديث المنتج الموجود
                    product.Id = _product.Id;
                    product.UpdatedAt = DateTime.Now;

                    var updatedProduct = await _productRepository.UpdateAsync(product);
                    return updatedProduct != null;
                }
                else
                {
                    // إضافة منتج جديد
                    var addedProduct = await _productRepository.AddAsync(product);
                    if (addedProduct != null)
                    {
                        _product.Id = addedProduct.Id;
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المنتج في قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private int GenerateNewId()
        {
            // توليد معرف عشوائي (سيتم استبداله بمعرف من قاعدة البيانات)
            Random random = new Random();
            return random.Next(100, 1000);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
