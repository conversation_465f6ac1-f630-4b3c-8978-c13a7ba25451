using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج المخزن
    /// </summary>
    public class Warehouse
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(200)]
        public string Location { get; set; }

        [MaxLength(15)]
        public string Phone { get; set; }

        [MaxLength(100)]
        public string Manager { get; set; }

        public bool IsActive { get; set; }

        [MaxLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public ICollection<Product> Products { get; set; }
        public ICollection<InventoryTransaction> SourceTransactions { get; set; }
        public ICollection<InventoryTransaction> DestinationTransactions { get; set; }
    }
}
