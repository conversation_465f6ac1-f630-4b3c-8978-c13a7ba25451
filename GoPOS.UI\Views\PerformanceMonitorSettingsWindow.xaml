<Window x:Class="GoPOS.UI.Views.PerformanceMonitorSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إعدادات مراقبة الأداء" 
        Height="500" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        Background="#F5F5F5"
        Icon="/GoPOS.UI;component/Resources/Images/logo.png">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#BDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="BorderBrush" Value="#BDBDBD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#1565C0" Padding="15">
            <TextBlock Text="إعدادات مراقبة الأداء" FontSize="18" FontWeight="Bold" Foreground="White"/>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                <!-- إعدادات المراقبة -->
                <GroupBox Header="إعدادات المراقبة">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الفترة الزمنية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                <ComboBox x:Name="IntervalMinutesComboBox" Width="80">
                                    <ComboBoxItem Content="1"/>
                                    <ComboBoxItem Content="2"/>
                                    <ComboBoxItem Content="5" IsSelected="True"/>
                                    <ComboBoxItem Content="10"/>
                                    <ComboBoxItem Content="15"/>
                                    <ComboBoxItem Content="30"/>
                                    <ComboBoxItem Content="60"/>
                                </ComboBox>
                                <TextBlock Text="دقيقة" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="تسجيل البيانات:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <CheckBox Grid.Row="1" Grid.Column="1" x:Name="LogPerformanceDataCheckBox" Content="تسجيل بيانات الأداء في قاعدة البيانات" Margin="0,10,0,0" IsChecked="True"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- حدود التنبيهات -->
                <GroupBox Header="حدود التنبيهات">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="وحدة المعالجة المركزية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                <Slider x:Name="CpuThresholdSlider" Width="200" Minimum="50" Maximum="95" Value="80" TickFrequency="5" TickPlacement="BottomRight" IsSnapToTickEnabled="True"/>
                                <TextBlock Text="{Binding ElementName=CpuThresholdSlider, Path=Value, StringFormat={}{0:N0}%}" VerticalAlignment="Center" Margin="10,0,0,0" MinWidth="40"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="الذاكرة المتاحة:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,10,0,0">
                                <ComboBox x:Name="MemoryThresholdComboBox" Width="80">
                                    <ComboBoxItem Content="100"/>
                                    <ComboBoxItem Content="200"/>
                                    <ComboBoxItem Content="300"/>
                                    <ComboBoxItem Content="500" IsSelected="True"/>
                                    <ComboBoxItem Content="1000"/>
                                    <ComboBoxItem Content="2000"/>
                                </ComboBox>
                                <TextBlock Text="ميجابايت" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="استخدام القرص:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,10,0,0">
                                <Slider x:Name="DiskThresholdSlider" Width="200" Minimum="70" Maximum="95" Value="90" TickFrequency="5" TickPlacement="BottomRight" IsSnapToTickEnabled="True"/>
                                <TextBlock Text="{Binding ElementName=DiskThresholdSlider, Path=Value, StringFormat={}{0:N0}%}" VerticalAlignment="Center" Margin="10,0,0,0" MinWidth="40"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="مساحة القرص المتاحة:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Margin="0,10,0,0">
                                <ComboBox x:Name="DiskFreeSpaceThresholdComboBox" Width="80">
                                    <ComboBoxItem Content="1"/>
                                    <ComboBoxItem Content="2"/>
                                    <ComboBoxItem Content="5" IsSelected="True"/>
                                    <ComboBoxItem Content="10"/>
                                    <ComboBoxItem Content="20"/>
                                </ComboBox>
                                <TextBlock Text="جيجابايت" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="إرسال التنبيهات:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <CheckBox Grid.Row="4" Grid.Column="1" x:Name="SendAlertNotificationsCheckBox" Content="إرسال إشعارات بالتنبيهات" Margin="0,10,0,0" IsChecked="True"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- إعدادات التقارير -->
                <GroupBox Header="إعدادات التقارير">
                    <StackPanel>
                        <CheckBox x:Name="GeneratePerformanceReportsCheckBox" Content="إنشاء تقارير أداء دورية" Margin="0,0,0,10"/>
                        
                        <Grid IsEnabled="{Binding ElementName=GeneratePerformanceReportsCheckBox, Path=IsChecked}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الفترة الزمنية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                <ComboBox x:Name="ReportIntervalComboBox" Width="80">
                                    <ComboBoxItem Content="يومي"/>
                                    <ComboBoxItem Content="أسبوعي" IsSelected="True"/>
                                    <ComboBoxItem Content="شهري"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="تنسيق التقرير:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,10,0,0">
                                <ComboBox x:Name="ReportFormatComboBox" Width="80">
                                    <ComboBoxItem Content="PDF" IsSelected="True"/>
                                    <ComboBoxItem Content="Excel"/>
                                    <ComboBoxItem Content="HTML"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- مجلد التقارير -->
                <GroupBox Header="مجلد التقارير">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0" x:Name="ReportsDirectoryTextBox" IsReadOnly="True" Margin="0,0,5,0"/>
                        <Button Grid.Column="1" x:Name="BrowseReportsDirectoryButton" Content="استعراض..." Click="BrowseReportsDirectoryButton_Click" Width="100"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
            <Button x:Name="SaveButton" Content="حفظ" Click="SaveButton_Click" Width="100"/>
            <Button x:Name="CancelButton" Content="إلغاء" Click="CancelButton_Click" Width="100" Background="#9E9E9E"/>
        </StackPanel>
    </Grid>
</Window>
