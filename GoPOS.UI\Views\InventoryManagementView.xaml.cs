using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InventoryManagementView.xaml
    /// </summary>
    public partial class InventoryManagementView : UserControl
    {
        private ObservableCollection<InventoryItemViewModel> _inventoryItems;
        private ObservableCollection<StockMovementViewModel> _stockMovements;
        private ObservableCollection<InventoryAlertViewModel> _inventoryAlerts;
        private InventoryItemViewModel _selectedInventoryItem;

        public InventoryManagementView()
        {
            InitializeComponent();
            InitializeDates();
            LoadInventoryData();
            LoadStockMovements();
            LoadInventoryAlerts();
        }

        private void InitializeDates()
        {
            // تعيين التواريخ الافتراضية (الشهر الحالي)
            DateTime now = DateTime.Now;
            DateTime firstDayOfMonth = new DateTime(now.Year, now.Month, 1);
            DateTime lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            FromDatePicker.SelectedDate = firstDayOfMonth;
            ToDatePicker.SelectedDate = lastDayOfMonth;
        }

        private void LoadInventoryData(string searchText = "", int categoryId = 0, int warehouseId = 0)
        {
            // تحميل بيانات المخزون (سيتم استبدالها بقراءة من قاعدة البيانات)
            _inventoryItems = new ObservableCollection<InventoryItemViewModel>();
            Random random = new Random();

            // بيانات تجريبية
            for (int i = 1; i <= 20; i++)
            {
                int categoryIndex = random.Next(0, 3);
                string categoryName = categoryIndex == 0 ? "مشروبات" : (categoryIndex == 1 ? "وجبات سريعة" : "حلويات");
                int categoryIdValue = categoryIndex + 1;

                int warehouseIndex = random.Next(0, 3);
                string warehouseName = warehouseIndex == 0 ? "المخزن الرئيسي" : (warehouseIndex == 1 ? "مخزن الفرع 1" : "مخزن الفرع 2");
                int warehouseIdValue = warehouseIndex + 1;

                // تخطي العناصر التي لا تتطابق مع معايير التصفية
                if (categoryId > 0 && categoryIdValue != categoryId)
                    continue;

                if (warehouseId > 0 && warehouseIdValue != warehouseId)
                    continue;

                string productName = GetRandomProductName(categoryIndex);

                if (!string.IsNullOrEmpty(searchText) && !productName.Contains(searchText))
                    continue;

                int quantity = random.Next(0, 100);
                int minimumQuantity = random.Next(10, 30);

                // تحديد حالة المخزون
                string statusText;
                SolidColorBrush statusColor;

                if (quantity <= 0)
                {
                    statusText = "نفذ";
                    statusColor = new SolidColorBrush(Colors.Red);
                }
                else if (quantity < minimumQuantity)
                {
                    statusText = "منخفض";
                    statusColor = new SolidColorBrush(Colors.Orange);
                }
                else
                {
                    statusText = "متوفر";
                    statusColor = new SolidColorBrush(Colors.Green);
                }

                _inventoryItems.Add(new InventoryItemViewModel
                {
                    Id = i,
                    Barcode = $"100{i:D4}",
                    Name = productName,
                    CategoryId = categoryIdValue,
                    CategoryName = categoryName,
                    Quantity = quantity,
                    MinimumQuantity = minimumQuantity,
                    WarehouseId = warehouseIdValue,
                    WarehouseName = warehouseName,
                    CostPrice = (decimal)Math.Round(random.Next(5, 50) + random.NextDouble(), 2),
                    SellingPrice = (decimal)Math.Round(random.Next(10, 100) + random.NextDouble(), 2),
                    LastUpdated = DateTime.Now.AddDays(-random.Next(0, 30)),
                    StatusText = statusText,
                    StatusColor = statusColor
                });
            }

            InventoryDataGrid.ItemsSource = _inventoryItems;
            UpdateInventorySummary();
        }

        private string GetRandomProductName(int categoryIndex)
        {
            string[] drinks = { "قهوة", "شاي", "عصير برتقال", "عصير تفاح", "ماء معدني", "مشروب غازي", "عصير مانجو" };
            string[] fastFood = { "برجر", "بيتزا", "ساندويتش", "دجاج مقلي", "بطاطس", "ناجتس", "شاورما" };
            string[] desserts = { "كيك", "آيس كريم", "كوكيز", "دونات", "تشيز كيك", "بودينج", "كنافة" };

            Random random = new Random();

            if (categoryIndex == 0)
                return drinks[random.Next(drinks.Length)];
            else if (categoryIndex == 1)
                return fastFood[random.Next(fastFood.Length)];
            else
                return desserts[random.Next(desserts.Length)];
        }

        private void UpdateInventorySummary()
        {
            int totalProducts = _inventoryItems.Count;
            int totalQuantity = 0;
            int lowStockCount = 0;

            foreach (var item in _inventoryItems)
            {
                totalQuantity += item.Quantity;
                if (item.Quantity < item.MinimumQuantity)
                {
                    lowStockCount++;
                }
            }

            TotalProductsTextBlock.Text = totalProducts.ToString();
            TotalQuantityTextBlock.Text = totalQuantity.ToString();
            LowStockTextBlock.Text = lowStockCount.ToString();
        }

        private void LoadStockMovements(DateTime? fromDate = null, DateTime? toDate = null, int movementType = 0)
        {
            // تحميل حركة المخزون (سيتم استبدالها بقراءة من قاعدة البيانات)
            _stockMovements = new ObservableCollection<StockMovementViewModel>();
            Random random = new Random();

            if (!fromDate.HasValue)
                fromDate = FromDatePicker.SelectedDate ?? DateTime.Now.AddMonths(-1);

            if (!toDate.HasValue)
                toDate = ToDatePicker.SelectedDate ?? DateTime.Now;

            // بيانات تجريبية
            for (int i = 1; i <= 50; i++)
            {
                int typeIndex = random.Next(0, 5);
                string[] typeTexts = { "إضافة مخزون", "تعديل مخزون", "نقل مخزون", "بيع", "مرتجع" };
                string typeText = typeTexts[typeIndex];

                // تخطي الأنواع التي لا تتطابق مع معايير التصفية
                if (movementType > 0 && typeIndex != movementType - 1)
                    continue;

                DateTime date = DateTime.Now.AddDays(-random.Next(0, 30)).AddHours(-random.Next(0, 24));

                // تخطي التواريخ التي لا تتطابق مع معايير التصفية
                if (date < fromDate || date > toDate)
                    continue;

                int quantityBefore = random.Next(10, 100);
                int change = typeIndex == 3 ? -random.Next(1, 10) : random.Next(1, 20); // البيع يقلل المخزون

                if (typeIndex == 1) // تعديل المخزون يمكن أن يكون بالزيادة أو النقصان
                    change = random.Next(-10, 11);

                int quantityAfter = quantityBefore + change;

                int productIndex = random.Next(0, 20);
                string productName = GetRandomProductName(random.Next(0, 3));

                int warehouseIndex = random.Next(0, 3);
                string warehouseName = warehouseIndex == 0 ? "المخزن الرئيسي" : (warehouseIndex == 1 ? "مخزن الفرع 1" : "مخزن الفرع 2");

                _stockMovements.Add(new StockMovementViewModel
                {
                    Id = i,
                    Date = date,
                    TypeText = typeText,
                    ProductName = productName,
                    QuantityBefore = quantityBefore,
                    QuantityAfter = quantityAfter,
                    Change = change,
                    WarehouseName = warehouseName,
                    UserName = "مدير النظام",
                    Notes = typeIndex == 2 ? $"نقل من {warehouseName} إلى مخزن الفرع 3" : ""
                });
            }

            // ترتيب حركة المخزون حسب التاريخ (الأحدث أولاً)
            var sortedMovements = new ObservableCollection<StockMovementViewModel>(
                _stockMovements.OrderByDescending(m => m.Date)
            );

            StockMovementDataGrid.ItemsSource = sortedMovements;
        }

        private void LoadInventoryAlerts()
        {
            // تحميل تنبيهات المخزون (سيتم استبدالها بقراءة من قاعدة البيانات)
            _inventoryAlerts = new ObservableCollection<InventoryAlertViewModel>();
            Random random = new Random();

            // بيانات تجريبية
            for (int i = 1; i <= 10; i++)
            {
                int currentQuantity = random.Next(0, 10);
                int minimumQuantity = random.Next(10, 30);

                string typeText = currentQuantity <= 0 ? "نفاذ المخزون" : "مخزون منخفض";
                SolidColorBrush statusColor = currentQuantity <= 0 ?
                    new SolidColorBrush(Colors.Red) : new SolidColorBrush(Colors.Orange);

                int productIndex = random.Next(0, 20);
                string productName = GetRandomProductName(random.Next(0, 3));

                int warehouseIndex = random.Next(0, 3);
                string warehouseName = warehouseIndex == 0 ? "المخزن الرئيسي" : (warehouseIndex == 1 ? "مخزن الفرع 1" : "مخزن الفرع 2");

                bool isRead = random.Next(0, 2) == 0;

                _inventoryAlerts.Add(new InventoryAlertViewModel
                {
                    Id = i,
                    Date = DateTime.Now.AddDays(-random.Next(0, 7)),
                    TypeText = typeText,
                    ProductName = productName,
                    CurrentQuantity = currentQuantity,
                    MinimumQuantity = minimumQuantity,
                    WarehouseName = warehouseName,
                    IsRead = isRead,
                    StatusColor = isRead ? new SolidColorBrush(Colors.Gray) : statusColor,
                    IsSelected = false
                });
            }

            // ترتيب التنبيهات حسب التاريخ (الأحدث أولاً) والحالة (غير المقروءة أولاً)
            var sortedAlerts = new ObservableCollection<InventoryAlertViewModel>(
                _inventoryAlerts.OrderBy(a => a.IsRead).ThenByDescending(a => a.Date)
            );

            AlertsDataGrid.ItemsSource = sortedAlerts;
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchInventory();
        }

        private void SearchTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                SearchInventory();
            }
        }

        private void SearchInventory()
        {
            string searchText = SearchTextBox.Text.Trim();
            int categoryId = CategoryFilterComboBox.SelectedIndex;
            int warehouseId = WarehouseComboBox.SelectedIndex;

            LoadInventoryData(searchText, categoryId, warehouseId);
        }

        private void CategoryFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SearchInventory();
        }

        private void WarehouseComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SearchInventory();
        }

        private void InventoryDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedInventoryItem = InventoryDataGrid.SelectedItem as InventoryItemViewModel;
            AdjustStockButton.IsEnabled = _selectedInventoryItem != null;
            TransferStockButton.IsEnabled = _selectedInventoryItem != null;
        }

        private void AddStockButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إضافة مخزون
            StockAdjustmentWindow adjustmentWindow = new StockAdjustmentWindow(StockAdjustmentType.Add);
            adjustmentWindow.Owner = Window.GetWindow(this);

            if (adjustmentWindow.ShowDialog() == true)
            {
                // تحديث المخزون بعد الإضافة
                LoadInventoryData();
                LoadStockMovements();
            }
        }

        private void AdjustStockButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInventoryItem == null) return;

            // فتح نافذة تعديل المخزون
            StockAdjustmentWindow adjustmentWindow = new StockAdjustmentWindow(StockAdjustmentType.Adjust, _selectedInventoryItem);
            adjustmentWindow.Owner = Window.GetWindow(this);

            if (adjustmentWindow.ShowDialog() == true)
            {
                // تحديث المخزون بعد التعديل
                LoadInventoryData();
                LoadStockMovements();
            }
        }

        private void TransferStockButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedInventoryItem == null) return;

            // فتح نافذة نقل المخزون
            StockTransferWindow transferWindow = new StockTransferWindow(_selectedInventoryItem);
            transferWindow.Owner = Window.GetWindow(this);

            if (transferWindow.ShowDialog() == true)
            {
                // تحديث المخزون بعد النقل
                LoadInventoryData();
                LoadStockMovements();
            }
        }

        private void ExportInventoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح مربع حوار حفظ الملف
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "ملفات Excel (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv",
                    Title = "تصدير المخزون"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // محاكاة تصدير المخزون
                    // هنا يمكن إضافة الكود الفعلي لتصدير البيانات إلى ملف Excel أو CSV

                    MessageBox.Show("تم تصدير المخزون بنجاح", "تم التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintInventoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // محاكاة طباعة المخزون
                // هنا يمكن إضافة الكود الفعلي للطباعة

                MessageBox.Show("تم إرسال تقرير المخزون إلى الطابعة", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // تحديث حركة المخزون عند تغيير التواريخ
            DateTime? fromDate = FromDatePicker.SelectedDate;
            DateTime? toDate = ToDatePicker.SelectedDate;
            int movementType = MovementTypeComboBox.SelectedIndex;

            LoadStockMovements(fromDate, toDate, movementType);
        }

        private void MovementTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تحديث حركة المخزون عند تغيير نوع الحركة
            DateTime? fromDate = FromDatePicker.SelectedDate;
            DateTime? toDate = ToDatePicker.SelectedDate;
            int movementType = MovementTypeComboBox.SelectedIndex;

            LoadStockMovements(fromDate, toDate, movementType);
        }

        private void MarkAsReadButton_Click(object sender, RoutedEventArgs e)
        {
            // تعليم التنبيهات المحددة كمقروءة
            bool anySelected = false;

            foreach (InventoryAlertViewModel alert in AlertsDataGrid.Items)
            {
                if (alert.IsSelected)
                {
                    anySelected = true;
                    alert.IsRead = true;
                    alert.StatusColor = new SolidColorBrush(Colors.Gray);
                }
            }

            if (!anySelected)
            {
                MessageBox.Show("الرجاء تحديد تنبيه واحد على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // تحديث قائمة التنبيهات
            AlertsDataGrid.Items.Refresh();
        }

        private void DeleteAlertsButton_Click(object sender, RoutedEventArgs e)
        {
            // حذف التنبيهات المحددة
            bool anySelected = false;
            List<InventoryAlertViewModel> selectedAlerts = new List<InventoryAlertViewModel>();

            foreach (InventoryAlertViewModel alert in AlertsDataGrid.Items)
            {
                if (alert.IsSelected)
                {
                    anySelected = true;
                    selectedAlerts.Add(alert);
                }
            }

            if (!anySelected)
            {
                MessageBox.Show("الرجاء تحديد تنبيه واحد على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من حذف التنبيهات المحددة؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // حذف التنبيهات المحددة
                foreach (var alert in selectedAlerts)
                {
                    (_inventoryAlerts as ObservableCollection<InventoryAlertViewModel>).Remove(alert);
                }

                // تحديث قائمة التنبيهات
                AlertsDataGrid.Items.Refresh();
            }
        }

        private void RefreshAlertsButton_Click(object sender, RoutedEventArgs e)
        {
            // تحديث قائمة التنبيهات
            LoadInventoryAlerts();
        }

        private void AddStockFromAlertButton_Click(object sender, RoutedEventArgs e)
        {
            Button button = sender as Button;
            if (button != null)
            {
                InventoryAlertViewModel alert = button.Tag as InventoryAlertViewModel;

                // فتح نافذة إضافة مخزون
                StockAdjustmentWindow adjustmentWindow = new StockAdjustmentWindow(StockAdjustmentType.Add);
                adjustmentWindow.Owner = Window.GetWindow(this);

                if (adjustmentWindow.ShowDialog() == true)
                {
                    // تحديث المخزون بعد الإضافة
                    LoadInventoryData();
                    LoadStockMovements();
                    LoadInventoryAlerts();
                }
            }
        }
    }

    // فئات عرض البيانات
    public class InventoryItemViewModel
    {
        public int Id { get; set; }
        public required string Barcode { get; set; }
        public required string Name { get; set; }
        public int CategoryId { get; set; }
        public required string CategoryName { get; set; }
        public int Quantity { get; set; }
        public int MinimumQuantity { get; set; }
        public int WarehouseId { get; set; }
        public required string WarehouseName { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SellingPrice { get; set; }
        public DateTime LastUpdated { get; set; }
        public required string StatusText { get; set; }
        public required SolidColorBrush StatusColor { get; set; }
    }

    public class StockMovementViewModel
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public required string TypeText { get; set; }
        public required string ProductName { get; set; }
        public int QuantityBefore { get; set; }
        public int QuantityAfter { get; set; }
        public int Change { get; set; }
        public required string WarehouseName { get; set; }
        public required string UserName { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class InventoryAlertViewModel
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public required string TypeText { get; set; }
        public required string ProductName { get; set; }
        public int CurrentQuantity { get; set; }
        public int MinimumQuantity { get; set; }
        public required string WarehouseName { get; set; }
        public bool IsRead { get; set; }
        public required SolidColorBrush StatusColor { get; set; }
        public bool IsSelected { get; set; }
    }

    public enum StockAdjustmentType
    {
        Add,
        Adjust
    }
}
