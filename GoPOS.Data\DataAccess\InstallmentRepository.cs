using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع الأقساط
    /// </summary>
    public class InstallmentRepository
    {
        private readonly DatabaseContext _dbContext;

        /// <summary>
        /// إنشاء مستودع الأقساط
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public InstallmentRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        #region خطط الأقساط

        /// <summary>
        /// الحصول على جميع خطط الأقساط
        /// </summary>
        /// <returns>قائمة بجميع خطط الأقساط</returns>
        public async Task<IEnumerable<InstallmentPlan>> GetAllPlansAsync()
        {
            string query = @"
                SELECT p.*, c.Name AS CustomerName, i.InvoiceNumber
                FROM InstallmentPlans p
                LEFT JOIN Customers c ON p.CustomerId = c.Id
                LEFT JOIN Invoices i ON p.InvoiceId = i.Id
                ORDER BY p.StartDate DESC";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return await ConvertDataTableToInstallmentPlansAsync(dataTable);
        }

        /// <summary>
        /// الحصول على خطة أقساط بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف خطة الأقساط</param>
        /// <returns>خطة الأقساط إذا تم العثور عليها، وإلا فإنه يرجع null</returns>
        public async Task<InstallmentPlan> GetPlanByIdAsync(int id)
        {
            string query = @"
                SELECT p.*, c.Name AS CustomerName, i.InvoiceNumber
                FROM InstallmentPlans p
                LEFT JOIN Customers c ON p.CustomerId = c.Id
                LEFT JOIN Invoices i ON p.InvoiceId = i.Id
                WHERE p.Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            var plans = await ConvertDataTableToInstallmentPlansAsync(dataTable);
            return plans.FirstOrDefault();
        }

        /// <summary>
        /// إضافة خطة أقساط جديدة
        /// </summary>
        /// <param name="plan">خطة الأقساط المراد إضافتها</param>
        /// <returns>معرف خطة الأقساط المضافة</returns>
        public async Task<int> AddPlanAsync(InstallmentPlan plan)
        {
            // بدء المعاملة
            await _dbContext.OpenConnectionAsync();
            SqlTransaction transaction = null;

            try
            {
                // إنشاء المعاملة
                transaction = _dbContext.GetConnection().BeginTransaction();

                // إضافة خطة الأقساط
                string query = @"
                    INSERT INTO InstallmentPlans (CustomerId, InvoiceId, TotalAmount, 
                        DownPayment, RemainingAmount, NumberOfInstallments, 
                        InstallmentAmount, StartDate, EndDate, Status, Notes)
                    VALUES (@CustomerId, @InvoiceId, @TotalAmount, 
                        @DownPayment, @RemainingAmount, @NumberOfInstallments, 
                        @InstallmentAmount, @StartDate, @EndDate, @Status, @Notes);
                    
                    SELECT SCOPE_IDENTITY();";

                SqlCommand command = new SqlCommand(query, _dbContext.GetConnection(), transaction);
                command.Parameters.AddRange(new SqlParameter[]
                {
                    new SqlParameter("@CustomerId", plan.CustomerId),
                    new SqlParameter("@InvoiceId", (object)plan.InvoiceId ?? DBNull.Value),
                    new SqlParameter("@TotalAmount", plan.TotalAmount),
                    new SqlParameter("@DownPayment", plan.DownPayment),
                    new SqlParameter("@RemainingAmount", plan.RemainingAmount),
                    new SqlParameter("@NumberOfInstallments", plan.NumberOfInstallments),
                    new SqlParameter("@InstallmentAmount", plan.InstallmentAmount),
                    new SqlParameter("@StartDate", plan.StartDate),
                    new SqlParameter("@EndDate", plan.EndDate),
                    new SqlParameter("@Status", (int)plan.Status),
                    new SqlParameter("@Notes", (object)plan.Notes ?? DBNull.Value)
                });

                // الحصول على معرف خطة الأقساط المضافة
                int planId = Convert.ToInt32(await command.ExecuteScalarAsync());

                // إضافة الأقساط
                if (plan.Installments != null && plan.Installments.Any())
                {
                    foreach (var installment in plan.Installments)
                    {
                        string installmentQuery = @"
                            INSERT INTO Installments (PlanId, DueDate, Amount, 
                                Status, PaymentDate, Notes)
                            VALUES (@PlanId, @DueDate, @Amount, 
                                @Status, @PaymentDate, @Notes)";

                        SqlCommand installmentCommand = new SqlCommand(installmentQuery, _dbContext.GetConnection(), transaction);
                        installmentCommand.Parameters.AddRange(new SqlParameter[]
                        {
                            new SqlParameter("@PlanId", planId),
                            new SqlParameter("@DueDate", installment.DueDate),
                            new SqlParameter("@Amount", installment.Amount),
                            new SqlParameter("@Status", (int)installment.Status),
                            new SqlParameter("@PaymentDate", (object)installment.PaymentDate ?? DBNull.Value),
                            new SqlParameter("@Notes", (object)installment.Notes ?? DBNull.Value)
                        });

                        await installmentCommand.ExecuteNonQueryAsync();
                    }
                }

                // إتمام المعاملة
                transaction.Commit();

                return planId;
            }
            catch (Exception)
            {
                // التراجع عن المعاملة في حالة حدوث خطأ
                transaction?.Rollback();
                throw;
            }
            finally
            {
                // إغلاق الاتصال
                _dbContext.CloseConnection();
            }
        }

        /// <summary>
        /// تحديث خطة أقساط موجودة
        /// </summary>
        /// <param name="plan">خطة الأقساط المراد تحديثها</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdatePlanAsync(InstallmentPlan plan)
        {
            string query = @"
                UPDATE InstallmentPlans
                SET CustomerId = @CustomerId,
                    InvoiceId = @InvoiceId,
                    TotalAmount = @TotalAmount,
                    DownPayment = @DownPayment,
                    RemainingAmount = @RemainingAmount,
                    NumberOfInstallments = @NumberOfInstallments,
                    InstallmentAmount = @InstallmentAmount,
                    StartDate = @StartDate,
                    EndDate = @EndDate,
                    Status = @Status,
                    Notes = @Notes
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", plan.Id),
                new SqlParameter("@CustomerId", plan.CustomerId),
                new SqlParameter("@InvoiceId", (object)plan.InvoiceId ?? DBNull.Value),
                new SqlParameter("@TotalAmount", plan.TotalAmount),
                new SqlParameter("@DownPayment", plan.DownPayment),
                new SqlParameter("@RemainingAmount", plan.RemainingAmount),
                new SqlParameter("@NumberOfInstallments", plan.NumberOfInstallments),
                new SqlParameter("@InstallmentAmount", plan.InstallmentAmount),
                new SqlParameter("@StartDate", plan.StartDate),
                new SqlParameter("@EndDate", plan.EndDate),
                new SqlParameter("@Status", (int)plan.Status),
                new SqlParameter("@Notes", (object)plan.Notes ?? DBNull.Value)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// حذف خطة أقساط بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف خطة الأقساط المراد حذفها</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> DeletePlanAsync(int id)
        {
            // بدء المعاملة
            await _dbContext.OpenConnectionAsync();
            SqlTransaction transaction = null;

            try
            {
                // إنشاء المعاملة
                transaction = _dbContext.GetConnection().BeginTransaction();

                // حذف الأقساط
                string deleteInstallmentsQuery = "DELETE FROM Installments WHERE PlanId = @Id";
                SqlCommand deleteInstallmentsCommand = new SqlCommand(deleteInstallmentsQuery, _dbContext.GetConnection(), transaction);
                deleteInstallmentsCommand.Parameters.Add(new SqlParameter("@Id", id));
                await deleteInstallmentsCommand.ExecuteNonQueryAsync();

                // حذف خطة الأقساط
                string deletePlanQuery = "DELETE FROM InstallmentPlans WHERE Id = @Id";
                SqlCommand deletePlanCommand = new SqlCommand(deletePlanQuery, _dbContext.GetConnection(), transaction);
                deletePlanCommand.Parameters.Add(new SqlParameter("@Id", id));
                int result = await deletePlanCommand.ExecuteNonQueryAsync();

                // إتمام المعاملة
                transaction.Commit();

                return result;
            }
            catch (Exception)
            {
                // التراجع عن المعاملة في حالة حدوث خطأ
                transaction?.Rollback();
                throw;
            }
            finally
            {
                // إغلاق الاتصال
                _dbContext.CloseConnection();
            }
        }

        /// <summary>
        /// البحث عن خطط أقساط بواسطة العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة بخطط الأقساط للعميل المحدد</returns>
        public async Task<IEnumerable<InstallmentPlan>> GetPlansByCustomerAsync(int customerId)
        {
            string query = @"
                SELECT p.*, c.Name AS CustomerName, i.InvoiceNumber
                FROM InstallmentPlans p
                LEFT JOIN Customers c ON p.CustomerId = c.Id
                LEFT JOIN Invoices i ON p.InvoiceId = i.Id
                WHERE p.CustomerId = @CustomerId
                ORDER BY p.StartDate DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@CustomerId", customerId)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return await ConvertDataTableToInstallmentPlansAsync(dataTable);
        }

        /// <summary>
        /// البحث عن خطط أقساط بواسطة الفاتورة
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <returns>قائمة بخطط الأقساط للفاتورة المحددة</returns>
        public async Task<IEnumerable<InstallmentPlan>> GetPlansByInvoiceAsync(int invoiceId)
        {
            string query = @"
                SELECT p.*, c.Name AS CustomerName, i.InvoiceNumber
                FROM InstallmentPlans p
                LEFT JOIN Customers c ON p.CustomerId = c.Id
                LEFT JOIN Invoices i ON p.InvoiceId = i.Id
                WHERE p.InvoiceId = @InvoiceId
                ORDER BY p.StartDate DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@InvoiceId", invoiceId)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return await ConvertDataTableToInstallmentPlansAsync(dataTable);
        }

        /// <summary>
        /// البحث عن خطط أقساط بواسطة الحالة
        /// </summary>
        /// <param name="status">حالة خطة الأقساط</param>
        /// <returns>قائمة بخطط الأقساط بالحالة المحددة</returns>
        public async Task<IEnumerable<InstallmentPlan>> GetPlansByStatusAsync(InstallmentPlanStatus status)
        {
            string query = @"
                SELECT p.*, c.Name AS CustomerName, i.InvoiceNumber
                FROM InstallmentPlans p
                LEFT JOIN Customers c ON p.CustomerId = c.Id
                LEFT JOIN Invoices i ON p.InvoiceId = i.Id
                WHERE p.Status = @Status
                ORDER BY p.StartDate DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Status", (int)status)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return await ConvertDataTableToInstallmentPlansAsync(dataTable);
        }

        #endregion

        #region الأقساط

        /// <summary>
        /// الحصول على جميع الأقساط لخطة معينة
        /// </summary>
        /// <param name="planId">معرف خطة الأقساط</param>
        /// <returns>قائمة بجميع الأقساط للخطة المحددة</returns>
        public async Task<IEnumerable<Installment>> GetInstallmentsByPlanAsync(int planId)
        {
            string query = @"
                SELECT *
                FROM Installments
                WHERE PlanId = @PlanId
                ORDER BY DueDate";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@PlanId", planId)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToInstallments(dataTable);
        }

        /// <summary>
        /// الحصول على قسط بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف القسط</param>
        /// <returns>القسط إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<Installment> GetInstallmentByIdAsync(int id)
        {
            string query = @"
                SELECT *
                FROM Installments
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToInstallments(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// تحديث قسط
        /// </summary>
        /// <param name="installment">القسط المراد تحديثه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateInstallmentAsync(Installment installment)
        {
            string query = @"
                UPDATE Installments
                SET DueDate = @DueDate,
                    Amount = @Amount,
                    Status = @Status,
                    PaymentDate = @PaymentDate,
                    Notes = @Notes
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", installment.Id),
                new SqlParameter("@DueDate", installment.DueDate),
                new SqlParameter("@Amount", installment.Amount),
                new SqlParameter("@Status", (int)installment.Status),
                new SqlParameter("@PaymentDate", (object)installment.PaymentDate ?? DBNull.Value),
                new SqlParameter("@Notes", (object)installment.Notes ?? DBNull.Value)
            };

            int result = await _dbContext.ExecuteNonQueryAsync(query, parameters);

            // تحديث حالة خطة الأقساط
            await UpdatePlanStatusAsync(installment.PlanId);

            return result;
        }

        /// <summary>
        /// تسديد قسط
        /// </summary>
        /// <param name="installmentId">معرف القسط</param>
        /// <param name="paymentDate">تاريخ التسديد</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> PayInstallmentAsync(int installmentId, DateTime paymentDate, string notes = null)
        {
            string query = @"
                UPDATE Installments
                SET Status = @Status,
                    PaymentDate = @PaymentDate,
                    Notes = @Notes
                WHERE Id = @Id;
                
                SELECT PlanId FROM Installments WHERE Id = @Id;";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", installmentId),
                new SqlParameter("@Status", (int)InstallmentStatus.Paid),
                new SqlParameter("@PaymentDate", paymentDate),
                new SqlParameter("@Notes", (object)notes ?? DBNull.Value)
            };

            // تنفيذ الاستعلام والحصول على معرف الخطة
            object result = await _dbContext.ExecuteScalarAsync(query, parameters);
            int planId = Convert.ToInt32(result);

            // تحديث حالة خطة الأقساط
            await UpdatePlanStatusAsync(planId);

            return 1;
        }

        /// <summary>
        /// الحصول على الأقساط المستحقة
        /// </summary>
        /// <returns>قائمة بالأقساط المستحقة</returns>
        public async Task<IEnumerable<Installment>> GetDueInstallmentsAsync()
        {
            string query = @"
                SELECT i.*, p.CustomerId, c.Name AS CustomerName
                FROM Installments i
                INNER JOIN InstallmentPlans p ON i.PlanId = p.Id
                INNER JOIN Customers c ON p.CustomerId = c.Id
                WHERE i.Status = @Status
                AND i.DueDate <= @Today
                ORDER BY i.DueDate";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Status", (int)InstallmentStatus.Pending),
                new SqlParameter("@Today", DateTime.Today)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToDueInstallments(dataTable);
        }

        /// <summary>
        /// الحصول على الأقساط القادمة
        /// </summary>
        /// <param name="days">عدد الأيام</param>
        /// <returns>قائمة بالأقساط القادمة</returns>
        public async Task<IEnumerable<Installment>> GetUpcomingInstallmentsAsync(int days = 7)
        {
            string query = @"
                SELECT i.*, p.CustomerId, c.Name AS CustomerName
                FROM Installments i
                INNER JOIN InstallmentPlans p ON i.PlanId = p.Id
                INNER JOIN Customers c ON p.CustomerId = c.Id
                WHERE i.Status = @Status
                AND i.DueDate > @Today
                AND i.DueDate <= DATEADD(day, @Days, @Today)
                ORDER BY i.DueDate";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Status", (int)InstallmentStatus.Pending),
                new SqlParameter("@Today", DateTime.Today),
                new SqlParameter("@Days", days)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToDueInstallments(dataTable);
        }

        #endregion

        #region وظائف مساعدة

        /// <summary>
        /// تحديث حالة خطة الأقساط
        /// </summary>
        /// <param name="planId">معرف خطة الأقساط</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        private async Task<int> UpdatePlanStatusAsync(int planId)
        {
            // الحصول على عدد الأقساط الكلي وعدد الأقساط المسددة
            string countQuery = @"
                SELECT 
                    COUNT(*) AS TotalCount,
                    SUM(CASE WHEN Status = @PaidStatus THEN 1 ELSE 0 END) AS PaidCount
                FROM Installments
                WHERE PlanId = @PlanId";

            SqlParameter[] countParameters = new SqlParameter[]
            {
                new SqlParameter("@PlanId", planId),
                new SqlParameter("@PaidStatus", (int)InstallmentStatus.Paid)
            };

            DataTable countResult = await _dbContext.ExecuteQueryAsync(countQuery, countParameters);
            int totalCount = Convert.ToInt32(countResult.Rows[0]["TotalCount"]);
            int paidCount = Convert.ToInt32(countResult.Rows[0]["PaidCount"]);

            // تحديد حالة الخطة
            InstallmentPlanStatus status;
            if (paidCount == 0)
            {
                status = InstallmentPlanStatus.Active;
            }
            else if (paidCount == totalCount)
            {
                status = InstallmentPlanStatus.Completed;
            }
            else
            {
                status = InstallmentPlanStatus.PartiallyPaid;
            }

            // تحديث حالة الخطة
            string updateQuery = @"
                UPDATE InstallmentPlans
                SET Status = @Status
                WHERE Id = @PlanId";

            SqlParameter[] updateParameters = new SqlParameter[]
            {
                new SqlParameter("@PlanId", planId),
                new SqlParameter("@Status", (int)status)
            };

            return await _dbContext.ExecuteNonQueryAsync(updateQuery, updateParameters);
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة خطط أقساط
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة خطط الأقساط</returns>
        private async Task<IEnumerable<InstallmentPlan>> ConvertDataTableToInstallmentPlansAsync(DataTable dataTable)
        {
            List<InstallmentPlan> plans = new List<InstallmentPlan>();

            foreach (DataRow row in dataTable.Rows)
            {
                int planId = Convert.ToInt32(row["Id"]);

                InstallmentPlan plan = new InstallmentPlan
                {
                    Id = planId,
                    CustomerId = Convert.ToInt32(row["CustomerId"]),
                    CustomerName = row["CustomerName"]?.ToString(),
                    TotalAmount = Convert.ToDecimal(row["TotalAmount"]),
                    DownPayment = Convert.ToDecimal(row["DownPayment"]),
                    RemainingAmount = Convert.ToDecimal(row["RemainingAmount"]),
                    NumberOfInstallments = Convert.ToInt32(row["NumberOfInstallments"]),
                    InstallmentAmount = Convert.ToDecimal(row["InstallmentAmount"]),
                    StartDate = Convert.ToDateTime(row["StartDate"]),
                    EndDate = Convert.ToDateTime(row["EndDate"]),
                    Status = (InstallmentPlanStatus)Convert.ToInt32(row["Status"])
                };

                if (row["InvoiceId"] != DBNull.Value)
                {
                    plan.InvoiceId = Convert.ToInt32(row["InvoiceId"]);
                    plan.InvoiceNumber = row["InvoiceNumber"]?.ToString();
                }

                if (row["Notes"] != DBNull.Value)
                {
                    plan.Notes = row["Notes"].ToString();
                }

                // الحصول على الأقساط
                plan.Installments = (await GetInstallmentsByPlanAsync(planId)).ToList();

                plans.Add(plan);
            }

            return plans;
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة أقساط
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة الأقساط</returns>
        private IEnumerable<Installment> ConvertDataTableToInstallments(DataTable dataTable)
        {
            List<Installment> installments = new List<Installment>();

            foreach (DataRow row in dataTable.Rows)
            {
                Installment installment = new Installment
                {
                    Id = Convert.ToInt32(row["Id"]),
                    PlanId = Convert.ToInt32(row["PlanId"]),
                    DueDate = Convert.ToDateTime(row["DueDate"]),
                    Amount = Convert.ToDecimal(row["Amount"]),
                    Status = (InstallmentStatus)Convert.ToInt32(row["Status"])
                };

                if (row["PaymentDate"] != DBNull.Value)
                {
                    installment.PaymentDate = Convert.ToDateTime(row["PaymentDate"]);
                }

                if (row["Notes"] != DBNull.Value)
                {
                    installment.Notes = row["Notes"].ToString();
                }

                installments.Add(installment);
            }

            return installments;
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة أقساط مستحقة
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة الأقساط المستحقة</returns>
        private IEnumerable<Installment> ConvertDataTableToDueInstallments(DataTable dataTable)
        {
            List<Installment> installments = new List<Installment>();

            foreach (DataRow row in dataTable.Rows)
            {
                Installment installment = new Installment
                {
                    Id = Convert.ToInt32(row["Id"]),
                    PlanId = Convert.ToInt32(row["PlanId"]),
                    DueDate = Convert.ToDateTime(row["DueDate"]),
                    Amount = Convert.ToDecimal(row["Amount"]),
                    Status = (InstallmentStatus)Convert.ToInt32(row["Status"]),
                    CustomerId = Convert.ToInt32(row["CustomerId"]),
                    CustomerName = row["CustomerName"]?.ToString()
                };

                if (row["PaymentDate"] != DBNull.Value)
                {
                    installment.PaymentDate = Convert.ToDateTime(row["PaymentDate"]);
                }

                if (row["Notes"] != DBNull.Value)
                {
                    installment.Notes = row["Notes"].ToString();
                }

                installments.Add(installment);
            }

            return installments;
        }

        #endregion
    }
}
