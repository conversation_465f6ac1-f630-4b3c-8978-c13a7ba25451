using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;
using GoPOS.UI.ViewModels;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for CategoriesManagementView.xaml
    /// </summary>
    public partial class CategoriesManagementView : UserControl
    {
        private ObservableCollection<CategoryDisplayViewModel> _categories;
        private CategoryDisplayViewModel _selectedCategory;
        private CategoryRepository _categoryRepository;
        private DatabaseContext _dbContext;

        public CategoriesManagementView()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _categoryRepository = new CategoryRepository(_dbContext);

            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // تهيئة مجموعة الفئات
                _categories = new ObservableCollection<CategoryDisplayViewModel>();

                // تحميل الفئات من قاعدة البيانات
                var categories = await _categoryRepository.GetAllAsync();

                foreach (var category in categories)
                {
                    string colorHex = !string.IsNullOrEmpty(category.Color) ? category.Color : "#808080"; // رمادي افتراضي

                    _categories.Add(new CategoryDisplayViewModel
                    {
                        Id = category.Id,
                        Name = category.Name,
                        Code = $"CAT{category.Id:D4}",
                        Color = colorHex,
                        ColorBrush = GetColorFromHex(colorHex),
                        Description = category.Description,
                        IsActive = category.IsActive,
                        ProductCount = category.ProductCount,
                        ParentId = category.ParentId,
                        ParentName = GetParentCategoryName(category.ParentId, categories)
                    });
                }

                // تعيين مصدر البيانات للجدول
                CategoriesDataGrid.ItemsSource = _categories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private string GetParentCategoryName(int? parentId, IEnumerable<Category> categories)
        {
            if (!parentId.HasValue)
                return string.Empty;

            var parentCategory = categories.FirstOrDefault(c => c.Id == parentId.Value);
            return parentCategory?.Name ?? string.Empty;
        }

        private Color GetColorFromHex(string hexColor)
        {
            try
            {
                return (Color)ColorConverter.ConvertFromString(hexColor);
            }
            catch
            {
                return Colors.Gray;
            }
        }

        private void CategoriesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedCategory = CategoriesDataGrid.SelectedItem as CategoryDisplayViewModel;
            UpdateButtonsState();
        }

        private void UpdateButtonsState()
        {
            bool hasSelection = _selectedCategory != null;
            EditCategoryButton.IsEnabled = hasSelection;
            DeleteCategoryButton.IsEnabled = hasSelection;
            ViewProductsButton.IsEnabled = hasSelection && _selectedCategory.ProductCount > 0;
        }

        private void AddCategoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إضافة فئة جديدة
                CategoryEditorWindow editorWindow = new CategoryEditorWindow();
                editorWindow.Owner = Window.GetWindow(this);
                editorWindow.ShowDialog();

                // إعادة تحميل الفئات بعد الإضافة
                if (editorWindow.DialogResult == true)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة فئة جديدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditCategoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedCategory != null)
                {
                    // تحويل CategoryDisplayViewModel إلى CategoryViewModel
                    CategoryViewModel category = new CategoryViewModel
                    {
                        Id = _selectedCategory.Id,
                        Name = _selectedCategory.Name,
                        Code = _selectedCategory.Code,
                        Color = _selectedCategory.Color,
                        Description = _selectedCategory.Description,
                        IsActive = _selectedCategory.IsActive,
                        ProductCount = _selectedCategory.ProductCount,
                        ParentId = _selectedCategory.ParentId,
                        ParentName = _selectedCategory.ParentName
                    };

                    // فتح نافذة تعديل الفئة
                    CategoryEditorWindow editorWindow = new CategoryEditorWindow(category);
                    editorWindow.Owner = Window.GetWindow(this);
                    editorWindow.ShowDialog();

                    // إعادة تحميل الفئات بعد التعديل
                    if (editorWindow.DialogResult == true)
                    {
                        LoadDataAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteCategoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedCategory != null)
                {
                    // التحقق من وجود منتجات في الفئة
                    if (_selectedCategory.ProductCount > 0)
                    {
                        MessageBox.Show("لا يمكن حذف الفئة لأنها تحتوي على منتجات. قم بنقل المنتجات إلى فئة أخرى أولاً.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // طلب تأكيد الحذف
                    MessageBoxResult result = MessageBox.Show($"هل أنت متأكد من حذف الفئة '{_selectedCategory.Name}'؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            // حذف الفئة من قاعدة البيانات
                            await _categoryRepository.DeleteAsync(_selectedCategory.Id);

                            // حذف الفئة من القائمة
                            _categories.Remove(_selectedCategory);

                            MessageBox.Show("تم حذف الفئة بنجاح", "تم الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"حدث خطأ أثناء حذف الفئة من قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewProductsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedCategory != null)
                {
                    // فتح شاشة إدارة المنتجات مع تصفية حسب الفئة المحددة
                    MessageBox.Show($"سيتم فتح شاشة المنتجات للفئة: {_selectedCategory.Name}", "عرض المنتجات", MessageBoxButton.OK, MessageBoxImage.Information);

                    // هنا يمكن إضافة كود لفتح شاشة إدارة المنتجات مع تصفية حسب الفئة المحددة
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض منتجات الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر لمنع النقر المتكرر
                RefreshButton.IsEnabled = false;

                // إعادة تحميل البيانات
                LoadDataAsync();

                MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين الزر
                RefreshButton.IsEnabled = true;
            }
        }
    }

    // فئة لعرض الفئات في الجدول مع خصائص إضافية للعرض
    public class CategoryDisplayViewModel : CategoryViewModel
    {
        public Color ColorBrush { get; set; }
    }
}
