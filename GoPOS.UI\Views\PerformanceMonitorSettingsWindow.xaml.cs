using System;
using System.IO;
using System.Windows;
using System.Windows.Forms;
using GoPOS.Data.Services;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for PerformanceMonitorSettingsWindow.xaml
    /// </summary>
    public partial class PerformanceMonitorSettingsWindow : Window
    {
        private PerformanceSettings _settings;
        private string _defaultReportsDirectory;

        /// <summary>
        /// إنشاء نافذة إعدادات مراقبة الأداء
        /// </summary>
        /// <param name="settings">إعدادات مراقبة الأداء الحالية</param>
        public PerformanceMonitorSettingsWindow(PerformanceSettings settings = null)
        {
            InitializeComponent();
            
            // تحديد مجلد التقارير الافتراضي
            _defaultReportsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Reports");
            
            // استخدام الإعدادات المقدمة أو إنشاء إعدادات جديدة
            _settings = settings ?? new PerformanceSettings();
            
            // تعبئة الحقول بالإعدادات الحالية
            LoadSettings();
        }

        /// <summary>
        /// تعبئة الحقول بالإعدادات الحالية
        /// </summary>
        private void LoadSettings()
        {
            // الفترة الزمنية
            foreach (System.Windows.Controls.ComboBoxItem item in IntervalMinutesComboBox.Items)
            {
                if (item.Content.ToString() == _settings.IntervalMinutes.ToString())
                {
                    IntervalMinutesComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تسجيل بيانات الأداء
            LogPerformanceDataCheckBox.IsChecked = _settings.LogPerformanceData;
            
            // حد استخدام وحدة المعالجة المركزية
            CpuThresholdSlider.Value = _settings.CpuThresholdPercent;
            
            // حد الذاكرة المتاحة
            foreach (System.Windows.Controls.ComboBoxItem item in MemoryThresholdComboBox.Items)
            {
                if (item.Content.ToString() == _settings.MemoryThresholdMB.ToString())
                {
                    MemoryThresholdComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // حد استخدام القرص
            DiskThresholdSlider.Value = _settings.DiskThresholdPercent;
            
            // حد مساحة القرص المتاحة
            foreach (System.Windows.Controls.ComboBoxItem item in DiskFreeSpaceThresholdComboBox.Items)
            {
                if (item.Content.ToString() == _settings.DiskFreeSpaceThresholdGB.ToString())
                {
                    DiskFreeSpaceThresholdComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // إرسال إشعارات بالتنبيهات
            SendAlertNotificationsCheckBox.IsChecked = _settings.SendAlertNotifications;
            
            // إنشاء تقارير أداء دورية
            GeneratePerformanceReportsCheckBox.IsChecked = _settings.GeneratePerformanceReports;
            
            // الفترة الزمنية للتقارير
            if (_settings.ReportInterval == ReportInterval.Daily)
            {
                ReportIntervalComboBox.SelectedIndex = 0;
            }
            else if (_settings.ReportInterval == ReportInterval.Weekly)
            {
                ReportIntervalComboBox.SelectedIndex = 1;
            }
            else if (_settings.ReportInterval == ReportInterval.Monthly)
            {
                ReportIntervalComboBox.SelectedIndex = 2;
            }
            
            // تنسيق التقرير
            if (_settings.ReportFormat == ReportFormat.PDF)
            {
                ReportFormatComboBox.SelectedIndex = 0;
            }
            else if (_settings.ReportFormat == ReportFormat.Excel)
            {
                ReportFormatComboBox.SelectedIndex = 1;
            }
            else if (_settings.ReportFormat == ReportFormat.HTML)
            {
                ReportFormatComboBox.SelectedIndex = 2;
            }
            
            // مجلد التقارير
            ReportsDirectoryTextBox.Text = string.IsNullOrEmpty(_settings.ReportsDirectory) ? 
                _defaultReportsDirectory : _settings.ReportsDirectory;
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void SaveSettings()
        {
            // الفترة الزمنية
            if (IntervalMinutesComboBox.SelectedItem != null)
            {
                _settings.IntervalMinutes = int.Parse(
                    ((System.Windows.Controls.ComboBoxItem)IntervalMinutesComboBox.SelectedItem).Content.ToString());
            }
            
            // تسجيل بيانات الأداء
            _settings.LogPerformanceData = LogPerformanceDataCheckBox.IsChecked ?? true;
            
            // حد استخدام وحدة المعالجة المركزية
            _settings.CpuThresholdPercent = (float)CpuThresholdSlider.Value;
            
            // حد الذاكرة المتاحة
            if (MemoryThresholdComboBox.SelectedItem != null)
            {
                _settings.MemoryThresholdMB = float.Parse(
                    ((System.Windows.Controls.ComboBoxItem)MemoryThresholdComboBox.SelectedItem).Content.ToString());
            }
            
            // حد استخدام القرص
            _settings.DiskThresholdPercent = (float)DiskThresholdSlider.Value;
            
            // حد مساحة القرص المتاحة
            if (DiskFreeSpaceThresholdComboBox.SelectedItem != null)
            {
                _settings.DiskFreeSpaceThresholdGB = float.Parse(
                    ((System.Windows.Controls.ComboBoxItem)DiskFreeSpaceThresholdComboBox.SelectedItem).Content.ToString());
            }
            
            // إرسال إشعارات بالتنبيهات
            _settings.SendAlertNotifications = SendAlertNotificationsCheckBox.IsChecked ?? true;
            
            // إنشاء تقارير أداء دورية
            _settings.GeneratePerformanceReports = GeneratePerformanceReportsCheckBox.IsChecked ?? false;
            
            // الفترة الزمنية للتقارير
            if (ReportIntervalComboBox.SelectedIndex == 0)
            {
                _settings.ReportInterval = ReportInterval.Daily;
            }
            else if (ReportIntervalComboBox.SelectedIndex == 1)
            {
                _settings.ReportInterval = ReportInterval.Weekly;
            }
            else if (ReportIntervalComboBox.SelectedIndex == 2)
            {
                _settings.ReportInterval = ReportInterval.Monthly;
            }
            
            // تنسيق التقرير
            if (ReportFormatComboBox.SelectedIndex == 0)
            {
                _settings.ReportFormat = ReportFormat.PDF;
            }
            else if (ReportFormatComboBox.SelectedIndex == 1)
            {
                _settings.ReportFormat = ReportFormat.Excel;
            }
            else if (ReportFormatComboBox.SelectedIndex == 2)
            {
                _settings.ReportFormat = ReportFormat.HTML;
            }
            
            // مجلد التقارير
            _settings.ReportsDirectory = ReportsDirectoryTextBox.Text;
            
            // التحقق من وجود مجلد التقارير وإنشائه إذا لم يكن موجودًا
            if (!string.IsNullOrEmpty(_settings.ReportsDirectory) && !Directory.Exists(_settings.ReportsDirectory))
            {
                try
                {
                    Directory.CreateDirectory(_settings.ReportsDirectory);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show(
                        $"حدث خطأ أثناء إنشاء مجلد التقارير: {ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// معالجة حدث النقر على زر الحفظ
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ الإعدادات
                SaveSettings();
                
                // إغلاق النافذة
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة حدث النقر على زر الإلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// معالجة حدث النقر على زر استعراض مجلد التقارير
        /// </summary>
        private void BrowseReportsDirectoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مربع حوار اختيار المجلد
                FolderBrowserDialog folderDialog = new FolderBrowserDialog();
                folderDialog.Description = "اختر مجلد التقارير";
                folderDialog.ShowNewFolderButton = true;
                
                // تعيين المجلد الافتراضي
                if (!string.IsNullOrEmpty(ReportsDirectoryTextBox.Text) && Directory.Exists(ReportsDirectoryTextBox.Text))
                {
                    folderDialog.SelectedPath = ReportsDirectoryTextBox.Text;
                }
                else
                {
                    folderDialog.SelectedPath = _defaultReportsDirectory;
                }
                
                // عرض مربع الحوار
                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    ReportsDirectoryTextBox.Text = folderDialog.SelectedPath;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء اختيار المجلد: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على إعدادات مراقبة الأداء
        /// </summary>
        public PerformanceSettings GetSettings()
        {
            return _settings;
        }
    }

    /// <summary>
    /// الفترة الزمنية للتقارير
    /// </summary>
    public enum ReportInterval
    {
        /// <summary>
        /// يومي
        /// </summary>
        Daily,
        
        /// <summary>
        /// أسبوعي
        /// </summary>
        Weekly,
        
        /// <summary>
        /// شهري
        /// </summary>
        Monthly
    }

    /// <summary>
    /// تنسيق التقرير
    /// </summary>
    public enum ReportFormat
    {
        /// <summary>
        /// PDF
        /// </summary>
        PDF,
        
        /// <summary>
        /// Excel
        /// </summary>
        Excel,
        
        /// <summary>
        /// HTML
        /// </summary>
        HTML
    }
}
