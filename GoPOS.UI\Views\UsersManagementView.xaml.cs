using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GoPOS.Core.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for UsersManagementView.xaml
    /// </summary>
    public partial class UsersManagementView : UserControl
    {
        private ObservableCollection<UserViewModel> _users = new ObservableCollection<UserViewModel>();
        private UserViewModel? _selectedUser;

        public UsersManagementView()
        {
            InitializeComponent();

            // إضافة معالجات الأحداث
            UsersDataGrid.SelectionChanged += UsersDataGrid_SelectionChanged;
            AddUserButton.Click += AddUserButton_Click;
            EditUserButton.Click += EditUserButton_Click;
            DeleteUserButton.Click += DeleteUserButton_Click;
            ResetPasswordButton.Click += ResetPasswordButton_Click;
            RefreshButton.Click += RefreshButton_Click;

            // تحميل البيانات
            LoadData();

            // تحديث حالة الأزرار
            UpdateButtonsState();
        }

        private void LoadData()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // تحميل بيانات المستخدمين من قاعدة البيانات
                _users = new ObservableCollection<UserViewModel>
                {
                    new UserViewModel
                    {
                        Id = 1,
                        UserName = "admin",
                        FullName = "مدير النظام",
                        Email = "<EMAIL>",
                        Role = "مدير النظام",
                        RoleEnum = UserRole.Administrator,
                        IsActive = true,
                        LastLogin = DateTime.Now.AddDays(-1),
                        Permissions = new UserPermissions
                        {
                            CanManageSales = true,
                            CanManageInvoices = true,
                            CanReturnInvoices = true,
                            CanManageProducts = true,
                            CanManageCategories = true,
                            CanManageInventory = true,
                            CanManageUsers = true,
                            CanViewReports = true,
                            CanManageSettings = true
                        }
                    },
                    new UserViewModel
                    {
                        Id = 2,
                        UserName = "manager",
                        FullName = "مدير المبيعات",
                        Email = "<EMAIL>",
                        Role = "مدير",
                        RoleEnum = UserRole.Manager,
                        IsActive = true,
                        LastLogin = DateTime.Now.AddDays(-2),
                        Permissions = new UserPermissions
                        {
                            CanManageSales = true,
                            CanManageInvoices = true,
                            CanReturnInvoices = true,
                            CanManageProducts = true,
                            CanManageCategories = true,
                            CanManageInventory = true,
                            CanManageUsers = false,
                            CanViewReports = true,
                            CanManageSettings = false
                        }
                    },
                    new UserViewModel
                    {
                        Id = 3,
                        UserName = "cashier1",
                        FullName = "كاشير 1",
                        Email = "<EMAIL>",
                        Role = "كاشير",
                        RoleEnum = UserRole.Cashier,
                        IsActive = true,
                        LastLogin = DateTime.Now.AddHours(-5),
                        Permissions = new UserPermissions
                        {
                            CanManageSales = true,
                            CanManageInvoices = true,
                            CanReturnInvoices = false,
                            CanManageProducts = false,
                            CanManageCategories = false,
                            CanManageInventory = false,
                            CanManageUsers = false,
                            CanViewReports = false,
                            CanManageSettings = false
                        }
                    },
                    new UserViewModel
                    {
                        Id = 4,
                        UserName = "cashier2",
                        FullName = "كاشير 2",
                        Email = "<EMAIL>",
                        Role = "كاشير",
                        RoleEnum = UserRole.Cashier,
                        IsActive = false,
                        LastLogin = null,
                        Permissions = new UserPermissions
                        {
                            CanManageSales = true,
                            CanManageInvoices = true,
                            CanReturnInvoices = false,
                            CanManageProducts = false,
                            CanManageCategories = false,
                            CanManageInventory = false,
                            CanManageUsers = false,
                            CanViewReports = false,
                            CanManageSettings = false
                        }
                    }
                };

                // ربط البيانات بالجدول
                UsersDataGrid.ItemsSource = _users;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void UsersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedUser = UsersDataGrid.SelectedItem as UserViewModel;
            UpdateButtonsState();
        }

        private void UpdateButtonsState()
        {
            bool hasSelection = _selectedUser != null;
            EditUserButton.IsEnabled = hasSelection;
            DeleteUserButton.IsEnabled = hasSelection;
            ResetPasswordButton.IsEnabled = hasSelection;
        }

        private void AddUserButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إضافة مستخدم جديد
                UserEditorWindow editorWindow = new UserEditorWindow();
                editorWindow.Owner = Window.GetWindow(this);
                editorWindow.ShowDialog();

                // إعادة تحميل البيانات بعد الإضافة
                if (editorWindow.DialogResult == true)
                {
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة مستخدم جديد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditUserButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedUser != null)
                {
                    // فتح نافذة تعديل المستخدم
                    UserEditorWindow editorWindow = new UserEditorWindow(_selectedUser);
                    editorWindow.Owner = Window.GetWindow(this);
                    editorWindow.ShowDialog();

                    // إعادة تحميل البيانات بعد التعديل
                    if (editorWindow.DialogResult == true)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteUserButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedUser != null)
                {
                    // التحقق من عدم حذف المستخدم الحالي
                    if (_selectedUser.UserName.ToLower() == "admin")
                    {
                        MessageBox.Show("لا يمكن حذف المستخدم الرئيسي للنظام", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // طلب تأكيد الحذف
                    MessageBoxResult result = MessageBox.Show($"هل أنت متأكد من حذف المستخدم '{_selectedUser.UserName}'؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        // حذف المستخدم (سيتم استبداله بالحذف من قاعدة البيانات)
                        _users.Remove(_selectedUser);
                        MessageBox.Show("تم حذف المستخدم بنجاح", "تم الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                        UpdateButtonsState();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedUser != null)
                {
                    // طلب تأكيد إعادة تعيين كلمة المرور
                    MessageBoxResult result = MessageBox.Show($"هل أنت متأكد من إعادة تعيين كلمة المرور للمستخدم '{_selectedUser.UserName}'؟", "تأكيد إعادة تعيين كلمة المرور", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        // إعادة تعيين كلمة المرور (سيتم استبداله بالتحديث في قاعدة البيانات)
                        string newPassword = GenerateRandomPassword();
                        MessageBox.Show($"تم إعادة تعيين كلمة المرور للمستخدم '{_selectedUser.UserName}'\nكلمة المرور الجديدة: {newPassword}", "تم إعادة تعيين كلمة المرور", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إعادة تعيين كلمة المرور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateRandomPassword()
        {
            // توليد كلمة مرور عشوائية
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            char[] password = new char[8];
            for (int i = 0; i < 8; i++)
            {
                password[i] = chars[random.Next(chars.Length)];
            }
            return new string(password);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر لمنع النقر المتكرر
                RefreshButton.IsEnabled = false;

                // إعادة تحميل البيانات
                LoadData();

                MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين الزر
                RefreshButton.IsEnabled = true;
            }
        }
    }
}
