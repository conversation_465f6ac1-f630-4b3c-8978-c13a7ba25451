using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع الفئات
    /// </summary>
    public class CategoryRepository : IRepository<Category>
    {
        private readonly DatabaseContext _dbContext;

        /// <summary>
        /// إنشاء مستودع الفئات
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public CategoryRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        /// <summary>
        /// الحصول على جميع الفئات
        /// </summary>
        /// <returns>قائمة بجميع الفئات</returns>
        public async Task<IEnumerable<Category>> GetAllAsync()
        {
            string query = @"
                SELECT c.*, COUNT(p.Id) AS ProductCount
                FROM Categories c
                LEFT JOIN Products p ON c.Id = p.CategoryId
                GROUP BY c.Id, c.Name, c.Description, c.Color, c.ParentId, c.IsActive
                ORDER BY c.Name";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return ConvertDataTableToCategories(dataTable);
        }

        /// <summary>
        /// الحصول على فئة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الفئة</param>
        /// <returns>الفئة إذا تم العثور عليها، وإلا فإنه يرجع null</returns>
        public async Task<Category> GetByIdAsync(int id)
        {
            string query = @"
                SELECT c.*, COUNT(p.Id) AS ProductCount
                FROM Categories c
                LEFT JOIN Products p ON c.Id = p.CategoryId
                WHERE c.Id = @Id
                GROUP BY c.Id, c.Name, c.Description, c.Color, c.ParentId, c.IsActive";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToCategories(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// إضافة فئة جديدة
        /// </summary>
        /// <param name="entity">الفئة المراد إضافتها</param>
        /// <returns>معرف الفئة المضافة</returns>
        public async Task<int> AddAsync(Category entity)
        {
            string query = @"
                INSERT INTO Categories (Name, Description, Color, ParentId, IsActive)
                VALUES (@Name, @Description, @Color, @ParentId, @IsActive);
                SELECT SCOPE_IDENTITY();";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Name", entity.Name),
                new SqlParameter("@Description", (object)entity.Description ?? DBNull.Value),
                new SqlParameter("@Color", (object)entity.Color ?? DBNull.Value),
                new SqlParameter("@ParentId", (object)entity.ParentId ?? DBNull.Value),
                new SqlParameter("@IsActive", entity.IsActive)
            };

            object result = await _dbContext.ExecuteScalarAsync(query, parameters);
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// تحديث فئة موجودة
        /// </summary>
        /// <param name="entity">الفئة المراد تحديثها</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateAsync(Category entity)
        {
            string query = @"
                UPDATE Categories
                SET Name = @Name,
                    Description = @Description,
                    Color = @Color,
                    ParentId = @ParentId,
                    IsActive = @IsActive
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", entity.Id),
                new SqlParameter("@Name", entity.Name),
                new SqlParameter("@Description", (object)entity.Description ?? DBNull.Value),
                new SqlParameter("@Color", (object)entity.Color ?? DBNull.Value),
                new SqlParameter("@ParentId", (object)entity.ParentId ?? DBNull.Value),
                new SqlParameter("@IsActive", entity.IsActive)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// حذف فئة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف الفئة المراد حذفها</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> DeleteAsync(int id)
        {
            // التحقق من عدم وجود منتجات مرتبطة بالفئة
            string checkQuery = @"
                SELECT COUNT(*)
                FROM Products
                WHERE CategoryId = @Id";

            SqlParameter[] checkParameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            object result = await _dbContext.ExecuteScalarAsync(checkQuery, checkParameters);
            int productCount = Convert.ToInt32(result);

            if (productCount > 0)
            {
                throw new InvalidOperationException("لا يمكن حذف الفئة لأنها تحتوي على منتجات");
            }

            // التحقق من عدم وجود فئات فرعية
            string checkSubcategoriesQuery = @"
                SELECT COUNT(*)
                FROM Categories
                WHERE ParentId = @Id";

            object subcategoriesResult = await _dbContext.ExecuteScalarAsync(checkSubcategoriesQuery, checkParameters);
            int subcategoriesCount = Convert.ToInt32(subcategoriesResult);

            if (subcategoriesCount > 0)
            {
                throw new InvalidOperationException("لا يمكن حذف الفئة لأنها تحتوي على فئات فرعية");
            }

            // حذف الفئة
            string query = "DELETE FROM Categories WHERE Id = @Id";
            return await _dbContext.ExecuteNonQueryAsync(query, checkParameters);
        }

        /// <summary>
        /// البحث عن فئات بناءً على معايير معينة
        /// </summary>
        /// <param name="predicate">دالة التصفية</param>
        /// <returns>قائمة بالفئات التي تطابق معايير البحث</returns>
        public async Task<IEnumerable<Category>> FindAsync(Func<Category, bool> predicate)
        {
            var categories = await GetAllAsync();
            return categories.Where(predicate);
        }

        /// <summary>
        /// البحث عن فئات بواسطة النص
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة بالفئات التي تطابق نص البحث</returns>
        public async Task<IEnumerable<Category>> SearchAsync(string searchText)
        {
            string query = @"
                SELECT c.*, COUNT(p.Id) AS ProductCount
                FROM Categories c
                LEFT JOIN Products p ON c.Id = p.CategoryId
                WHERE c.Name LIKE @SearchText
                OR c.Description LIKE @SearchText
                GROUP BY c.Id, c.Name, c.Description, c.Color, c.ParentId, c.IsActive
                ORDER BY c.Name";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@SearchText", $"%{searchText}%")
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToCategories(dataTable);
        }

        /// <summary>
        /// الحصول على الفئات الرئيسية
        /// </summary>
        /// <returns>قائمة بالفئات الرئيسية</returns>
        public async Task<IEnumerable<Category>> GetMainCategoriesAsync()
        {
            string query = @"
                SELECT c.*, COUNT(p.Id) AS ProductCount
                FROM Categories c
                LEFT JOIN Products p ON c.Id = p.CategoryId
                WHERE c.ParentId IS NULL
                GROUP BY c.Id, c.Name, c.Description, c.Color, c.ParentId, c.IsActive
                ORDER BY c.Name";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return ConvertDataTableToCategories(dataTable);
        }

        /// <summary>
        /// الحصول على الفئات الفرعية لفئة معينة
        /// </summary>
        /// <param name="parentId">معرف الفئة الأب</param>
        /// <returns>قائمة بالفئات الفرعية</returns>
        public async Task<IEnumerable<Category>> GetSubcategoriesAsync(int parentId)
        {
            string query = @"
                SELECT c.*, COUNT(p.Id) AS ProductCount
                FROM Categories c
                LEFT JOIN Products p ON c.Id = p.CategoryId
                WHERE c.ParentId = @ParentId
                GROUP BY c.Id, c.Name, c.Description, c.Color, c.ParentId, c.IsActive
                ORDER BY c.Name";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@ParentId", parentId)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToCategories(dataTable);
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة فئات
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة الفئات</returns>
        private IEnumerable<Category> ConvertDataTableToCategories(DataTable dataTable)
        {
            List<Category> categories = new List<Category>();

            foreach (DataRow row in dataTable.Rows)
            {
                Category category = new Category
                {
                    Id = Convert.ToInt32(row["Id"]),
                    Name = row["Name"].ToString(),
                    IsActive = Convert.ToBoolean(row["IsActive"]),
                    ProductCount = Convert.ToInt32(row["ProductCount"])
                };

                if (row["Description"] != DBNull.Value)
                {
                    category.Description = row["Description"].ToString();
                }

                if (row["Color"] != DBNull.Value)
                {
                    category.Color = row["Color"].ToString();
                }

                if (row["ParentId"] != DBNull.Value)
                {
                    category.ParentId = Convert.ToInt32(row["ParentId"]);
                }

                categories.Add(category);
            }

            return categories;
        }
    }
}
