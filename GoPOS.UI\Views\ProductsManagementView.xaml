<UserControl x:Class="GoPOS.UI.Views.ProductsManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <Button x:Name="AddProductButton" Content="إضافة منتج" Width="120" Height="40" Margin="0,0,10,0" Click="AddProductButton_Click"/>
                <Button x:Name="EditProductButton" Content="تعديل" Width="80" Height="40" Margin="0,0,10,0" Click="EditProductButton_Click"/>
                <Button x:Name="DeleteProductButton" Content="حذف" Width="80" Height="40" Background="#F44336" Foreground="White" Click="DeleteProductButton_Click"/>
            </StackPanel>

            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <TextBox x:Name="SearchTextBox" Width="200" Height="40" Padding="5" Margin="0,0,5,0" KeyDown="SearchTextBox_KeyDown"/>
                <Button x:Name="SearchButton" Content="بحث" Width="80" Height="40" Click="SearchButton_Click"/>
            </StackPanel>
        </Grid>

        <!-- جدول المنتجات -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة الفئات -->
            <Grid Grid.Column="0" Margin="0,0,10,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="الفئات" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                <ListView x:Name="CategoriesListView" Grid.Row="1" BorderThickness="1" BorderBrush="#DDDDDD" SelectionChanged="CategoriesListView_SelectionChanged">
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Name}" Padding="5"/>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>
            </Grid>

            <!-- قائمة المنتجات -->
            <DataGrid x:Name="ProductsDataGrid" Grid.Column="1" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectionChanged="ProductsDataGrid_SelectionChanged"
                      AlternatingRowBackground="#F5F5F5" BorderBrush="#DDDDDD" BorderThickness="1" RowHeight="35" HeadersVisibility="Column" GridLinesVisibility="Horizontal" HorizontalGridLinesBrush="#EEEEEE">
                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#2196F3"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Padding" Value="10,0"/>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="كود المنتج" Binding="{Binding Code}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="سعر التكلفة" Binding="{Binding CostPrice, StringFormat={}{0:N2}}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="Foreground" Value="#F44336"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="سعر البيع" Binding="{Binding Price, StringFormat={}{0:N2}}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="Foreground" Value="#4CAF50"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>
    </Grid>
</UserControl>
