using System;

namespace GoPOS.Data.Models
{
    /// <summary>
    /// دور المستخدم
    /// </summary>
    public enum UserRole
    {
        /// <summary>
        /// مدير
        /// </summary>
        Admin = 0,

        /// <summary>
        /// مشرف
        /// </summary>
        Supervisor = 1,

        /// <summary>
        /// كاشير
        /// </summary>
        Cashier = 2,

        /// <summary>
        /// محاسب
        /// </summary>
        Accountant = 3,

        /// <summary>
        /// مخزن
        /// </summary>
        Inventory = 4,

        /// <summary>
        /// مطور (الشركة المطورة للبرنامج)
        /// </summary>
        Developer = 99
    }

    /// <summary>
    /// نموذج المستخدم
    /// </summary>
    public class User
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        public string PasswordHash { get; set; }

        /// <summary>
        /// الاسم الكامل
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// دور المستخدم
        /// </summary>
        public UserRole Role { get; set; }

        /// <summary>
        /// هل المستخدم نشط
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// آخر تسجيل دخول
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// صلاحيات المستخدم
        /// </summary>
        public UserPermissions Permissions { get; set; }
    }

    /// <summary>
    /// صلاحيات المستخدم
    /// </summary>
    public class UserPermissions
    {
        /// <summary>
        /// إدارة المبيعات
        /// </summary>
        public bool CanManageSales { get; set; } = true;

        /// <summary>
        /// إدارة الفواتير
        /// </summary>
        public bool CanManageInvoices { get; set; } = true;

        /// <summary>
        /// إرجاع الفواتير
        /// </summary>
        public bool CanReturnInvoices { get; set; } = true;

        /// <summary>
        /// إدارة المنتجات
        /// </summary>
        public bool CanManageProducts { get; set; } = true;

        /// <summary>
        /// إدارة الفئات
        /// </summary>
        public bool CanManageCategories { get; set; } = true;

        /// <summary>
        /// إدارة المخزون
        /// </summary>
        public bool CanManageInventory { get; set; } = true;

        /// <summary>
        /// إدارة المستخدمين
        /// </summary>
        public bool CanManageUsers { get; set; } = true;

        /// <summary>
        /// عرض التقارير
        /// </summary>
        public bool CanViewReports { get; set; } = true;

        /// <summary>
        /// إدارة الإعدادات
        /// </summary>
        public bool CanManageSettings { get; set; } = true;

        /// <summary>
        /// صلاحية الصيانة (للمطورين فقط)
        /// </summary>
        public bool CanPerformMaintenance { get; set; } = false;

        /// <summary>
        /// صلاحية النسخ الاحتياطي واستعادة البيانات (للمطورين فقط)
        /// </summary>
        public bool CanManageBackups { get; set; } = false;

        /// <summary>
        /// صلاحية الوصول المباشر لقاعدة البيانات (للمطورين فقط)
        /// </summary>
        public bool CanAccessDatabase { get; set; } = false;
    }
}
