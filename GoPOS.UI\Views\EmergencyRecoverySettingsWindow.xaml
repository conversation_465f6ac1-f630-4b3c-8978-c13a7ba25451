<Window x:Class="GoPOS.UI.Views.EmergencyRecoverySettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إعدادات استعادة الطوارئ" 
        Height="450" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        Background="#F5F5F5"
        Icon="/GoPOS.UI;component/Resources/Images/logo.png">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#BDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="BorderBrush" Value="#BDBDBD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#1565C0" Padding="15">
            <TextBlock Text="إعدادات استعادة الطوارئ" FontSize="18" FontWeight="Bold" Foreground="White"/>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                <!-- إعدادات النسخ الاحتياطي للطوارئ -->
                <GroupBox Header="النسخ الاحتياطي للطوارئ">
                    <StackPanel>
                        <CheckBox x:Name="EnableEmergencyBackupCheckBox" Content="تمكين النسخ الاحتياطي للطوارئ" Margin="0,0,0,10" IsChecked="True"/>
                        
                        <Grid IsEnabled="{Binding ElementName=EnableEmergencyBackupCheckBox, Path=IsChecked}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الفترة الزمنية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                <ComboBox x:Name="EmergencyBackupIntervalComboBox" Width="80">
                                    <ComboBoxItem Content="1"/>
                                    <ComboBoxItem Content="3"/>
                                    <ComboBoxItem Content="7" IsSelected="True"/>
                                    <ComboBoxItem Content="14"/>
                                    <ComboBoxItem Content="30"/>
                                </ComboBox>
                                <TextBlock Text="يوم" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="عدد النسخ:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,10,0,0">
                                <ComboBox x:Name="EmergencyBackupCountComboBox" Width="80">
                                    <ComboBoxItem Content="1"/>
                                    <ComboBoxItem Content="2"/>
                                    <ComboBoxItem Content="3" IsSelected="True"/>
                                    <ComboBoxItem Content="5"/>
                                </ComboBox>
                                <TextBlock Text="نسخة" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="النسخ السحابي:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <CheckBox Grid.Row="2" Grid.Column="1" x:Name="EnableCloudBackupCheckBox" Content="تمكين النسخ الاحتياطي السحابي" Margin="0,10,0,0"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- إعدادات النسخ الاحتياطي الكامل للنظام -->
                <GroupBox Header="النسخ الاحتياطي الكامل للنظام">
                    <StackPanel>
                        <CheckBox x:Name="EnableFullSystemBackupCheckBox" Content="تمكين النسخ الاحتياطي الكامل للنظام" Margin="0,0,0,10"/>
                        
                        <Grid IsEnabled="{Binding ElementName=EnableFullSystemBackupCheckBox, Path=IsChecked}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الفترة الزمنية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                <ComboBox x:Name="FullSystemBackupIntervalComboBox" Width="80">
                                    <ComboBoxItem Content="7"/>
                                    <ComboBoxItem Content="14"/>
                                    <ComboBoxItem Content="30" IsSelected="True"/>
                                    <ComboBoxItem Content="60"/>
                                    <ComboBoxItem Content="90"/>
                                </ComboBox>
                                <TextBlock Text="يوم" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="وقت النسخ:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <ComboBox Grid.Row="1" Grid.Column="1" x:Name="FullSystemBackupTimeComboBox" Width="120" HorizontalAlignment="Left" Margin="0,10,0,0">
                                <ComboBoxItem Content="منتصف الليل" IsSelected="True"/>
                                <ComboBoxItem Content="3:00 صباحًا"/>
                                <ComboBoxItem Content="6:00 صباحًا"/>
                                <ComboBoxItem Content="12:00 ظهرًا"/>
                                <ComboBoxItem Content="6:00 مساءً"/>
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- مجلدات النسخ الاحتياطي -->
                <GroupBox Header="مجلدات النسخ الاحتياطي">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="مجلد الطوارئ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="EmergencyDirectoryTextBox" IsReadOnly="True" Margin="0,0,5,0"/>
                        <Button Grid.Row="0" Grid.Column="2" x:Name="BrowseEmergencyDirectoryButton" Content="استعراض..." Click="BrowseEmergencyDirectoryButton_Click" Width="100"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="مجلد السحابة:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                        <TextBox Grid.Row="1" Grid.Column="1" x:Name="CloudDirectoryTextBox" IsReadOnly="True" Margin="0,10,5,0"/>
                        <Button Grid.Row="1" Grid.Column="2" x:Name="BrowseCloudDirectoryButton" Content="استعراض..." Click="BrowseCloudDirectoryButton_Click" Width="100" Margin="0,10,0,0"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
            <Button x:Name="SaveButton" Content="حفظ" Click="SaveButton_Click" Width="100"/>
            <Button x:Name="CancelButton" Content="إلغاء" Click="CancelButton_Click" Width="100" Background="#9E9E9E"/>
        </StackPanel>
    </Grid>
</Window>
