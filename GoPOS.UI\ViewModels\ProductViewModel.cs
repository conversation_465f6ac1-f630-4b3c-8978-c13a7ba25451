using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GoPOS.UI.ViewModels
{
    /// <summary>
    /// نموذج عرض المنتج
    /// </summary>
    public class ProductViewModel : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private string _barcode = string.Empty;
        private int? _categoryId;
        private string _categoryName = string.Empty;
        private decimal _price;
        private decimal _costPrice;
        private decimal _quantity;
        private decimal _minQuantity;
        private bool _isActive = true;
        private string _imagePath = string.Empty;

        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// اسم المنتج
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// وصف المنتج
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// باركود المنتج
        /// </summary>
        public string Barcode
        {
            get => _barcode;
            set
            {
                if (_barcode != value)
                {
                    _barcode = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// معرف الفئة
        /// </summary>
        public int? CategoryId
        {
            get => _categoryId;
            set
            {
                if (_categoryId != value)
                {
                    _categoryId = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string CategoryName
        {
            get => _categoryName;
            set
            {
                if (_categoryName != value)
                {
                    _categoryName = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// سعر البيع
        /// </summary>
        public decimal Price
        {
            get => _price;
            set
            {
                if (_price != value)
                {
                    _price = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// سعر التكلفة
        /// </summary>
        public decimal CostPrice
        {
            get => _costPrice;
            set
            {
                if (_costPrice != value)
                {
                    _costPrice = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// الكمية المتوفرة
        /// </summary>
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// الحد الأدنى للكمية
        /// </summary>
        public decimal MinQuantity
        {
            get => _minQuantity;
            set
            {
                if (_minQuantity != value)
                {
                    _minQuantity = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// هل المنتج نشط
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// مسار الصورة
        /// </summary>
        public string ImagePath
        {
            get => _imagePath;
            set
            {
                if (_imagePath != value)
                {
                    _imagePath = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حدث تغيير الخاصية
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// إشعار بتغيير الخاصية
        /// </summary>
        /// <param name="propertyName">اسم الخاصية</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
