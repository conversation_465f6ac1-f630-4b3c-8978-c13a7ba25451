<Window x:Class="GoPOS.UI.Views.AutoBackupSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إعدادات النسخ الاحتياطي التلقائي" 
        Height="450" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        Background="#F5F5F5"
        Icon="/GoPOS.UI;component/Resources/Images/logo.png">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#BDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="BorderBrush" Value="#BDBDBD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#1565C0" Padding="15">
            <TextBlock Text="إعدادات النسخ الاحتياطي التلقائي" FontSize="18" FontWeight="Bold" Foreground="White"/>
        </Border>
        
        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                <!-- تمكين النسخ الاحتياطي التلقائي -->
                <GroupBox Header="النسخ الاحتياطي التلقائي">
                    <StackPanel>
                        <CheckBox x:Name="EnableAutoBackupCheckBox" Content="تمكين النسخ الاحتياطي التلقائي" Margin="0,0,0,10"/>
                        
                        <Grid IsEnabled="{Binding ElementName=EnableAutoBackupCheckBox, Path=IsChecked}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الفترة الزمنية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                                <ComboBox x:Name="IntervalHoursComboBox" Width="80">
                                    <ComboBoxItem Content="1"/>
                                    <ComboBoxItem Content="2"/>
                                    <ComboBoxItem Content="4"/>
                                    <ComboBoxItem Content="6"/>
                                    <ComboBoxItem Content="12" IsSelected="True"/>
                                    <ComboBoxItem Content="24"/>
                                    <ComboBoxItem Content="48"/>
                                    <ComboBoxItem Content="72"/>
                                </ComboBox>
                                <TextBlock Text="ساعة" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="تأخير البدء:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <CheckBox Grid.Row="1" Grid.Column="1" x:Name="DelayStartupCheckBox" Content="تأخير النسخ الاحتياطي الأول حتى انقضاء الفترة الزمنية" Margin="0,10,0,0" IsChecked="True"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- إدارة النسخ الاحتياطية القديمة -->
                <GroupBox Header="إدارة النسخ الاحتياطية القديمة">
                    <StackPanel IsEnabled="{Binding ElementName=EnableAutoBackupCheckBox, Path=IsChecked}">
                        <CheckBox x:Name="DeleteOldBackupsCheckBox" Content="حذف النسخ الاحتياطية القديمة تلقائيًا" Margin="0,0,0,10" IsChecked="True"/>
                        
                        <Grid IsEnabled="{Binding ElementName=DeleteOldBackupsCheckBox, Path=IsChecked}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="الاحتفاظ بآخر:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <ComboBox x:Name="KeepBackupsCountComboBox" Width="80">
                                    <ComboBoxItem Content="3"/>
                                    <ComboBoxItem Content="5"/>
                                    <ComboBoxItem Content="7" IsSelected="True"/>
                                    <ComboBoxItem Content="10"/>
                                    <ComboBoxItem Content="15"/>
                                    <ComboBoxItem Content="30"/>
                                </ComboBox>
                                <TextBlock Text="نسخة احتياطية" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- إشعارات النسخ الاحتياطي -->
                <GroupBox Header="إشعارات النسخ الاحتياطي">
                    <StackPanel IsEnabled="{Binding ElementName=EnableAutoBackupCheckBox, Path=IsChecked}">
                        <CheckBox x:Name="SendEmailNotificationCheckBox" Content="إرسال إشعار بالبريد الإلكتروني بعد النسخ الاحتياطي" Margin="0,0,0,10"/>
                        
                        <Grid IsEnabled="{Binding ElementName=SendEmailNotificationCheckBox, Path=IsChecked}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="البريد الإلكتروني:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBox Grid.Column="1" x:Name="NotificationEmailTextBox" Margin="0,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- مجلد النسخ الاحتياطي -->
                <GroupBox Header="مجلد النسخ الاحتياطي">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0" x:Name="BackupDirectoryTextBox" IsReadOnly="True" Margin="0,0,5,0"/>
                        <Button Grid.Column="1" x:Name="BrowseDirectoryButton" Content="استعراض..." Click="BrowseDirectoryButton_Click" Width="100"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
            <Button x:Name="SaveButton" Content="حفظ" Click="SaveButton_Click" Width="100"/>
            <Button x:Name="CancelButton" Content="إلغاء" Click="CancelButton_Click" Width="100" Background="#9E9E9E"/>
        </StackPanel>
    </Grid>
</Window>
