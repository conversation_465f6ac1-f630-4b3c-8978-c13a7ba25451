<UserControl x:Class="GoPOS.UI.Views.ExpensesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والأدوات -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="إدارة المنصرفات" FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text="🧾" FontSize="24" Margin="10,0,0,0" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddExpenseButton" Content="إضافة منصرف جديد" Padding="15,8" Margin="0,0,10,0" Background="#4CAF50" Foreground="White" Click="AddExpenseButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="+" FontSize="16" FontWeight="Bold" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة منصرف جديد" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="RefreshButton" Content="تحديث" Padding="15,8" Background="#FF9800" Foreground="White" Click="RefreshButton_Click">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة المنصرفات -->
            <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" Margin="10" CornerRadius="4">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط البحث والتصفية -->
                    <Grid Grid.Row="0" Background="#F5F5F5" Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Background="White">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox x:Name="SearchTextBox" Grid.Column="0" Padding="10,5" BorderThickness="0" KeyDown="SearchTextBox_KeyDown"/>
                                <Button x:Name="SearchButton" Grid.Column="1" Content="🔍" Width="40" BorderThickness="0" Background="Transparent" Click="SearchButton_Click"/>
                            </Grid>
                        </Border>

                        <ComboBox x:Name="FilterTypeComboBox" Grid.Column="1" Width="150" Margin="10,0,0,0" Padding="10,5" SelectionChanged="FilterTypeComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                            <ComboBoxItem Content="مصاريف تشغيلية"/>
                            <ComboBoxItem Content="رواتب"/>
                            <ComboBoxItem Content="إيجار"/>
                            <ComboBoxItem Content="مرافق"/>
                            <ComboBoxItem Content="مشتريات"/>
                            <ComboBoxItem Content="أخرى"/>
                        </ComboBox>

                        <DatePicker x:Name="FilterDatePicker" Grid.Column="2" Width="120" Margin="10,0,0,0" SelectedDateChanged="FilterDatePicker_SelectedDateChanged"/>
                    </Grid>

                    <!-- جدول المنصرفات -->
                    <DataGrid x:Name="ExpensesDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True"
                              BorderThickness="0" GridLinesVisibility="Horizontal" AlternatingRowBackground="#F5F5F5"
                              CanUserSortColumns="True" SelectionMode="Single" SelectionChanged="ExpensesDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم" Binding="{Binding Id}" Width="60"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat={}{0:yyyy/MM/dd}}" Width="100"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat={}{0:N2}}" Width="100"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="120"/>
                            <DataGridTextColumn Header="المستفيد" Binding="{Binding Beneficiary}" Width="120"/>
                            <DataGridTextColumn Header="المستخدم" Binding="{Binding UserName}" Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="📝" ToolTip="تعديل" Margin="2" Padding="5" Click="EditExpenseButton_Click" Tag="{Binding}"/>
                                            <Button Content="🗑️" ToolTip="حذف" Margin="2" Padding="5" Click="DeleteExpenseButton_Click" Tag="{Binding}"/>
                                            <Button Content="🖨️" ToolTip="طباعة" Margin="2" Padding="5" Click="PrintExpenseButton_Click" Tag="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- رسالة عند عدم وجود منصرفات -->
                    <TextBlock x:Name="NoExpensesMessage" Grid.Row="1" Text="لا توجد منصرفات للعرض"
                               FontSize="18" HorizontalAlignment="Center" VerticalAlignment="Center"
                               Foreground="Gray" Visibility="Collapsed"/>

                    <!-- مؤشر التحميل -->
                    <StackPanel x:Name="LoadingIndicator" Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center" Visibility="Collapsed">
                        <TextBlock Text="جاري تحميل البيانات..." FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,10" Foreground="Gray"/>
                        <ProgressBar Width="200" Height="10" IsIndeterminate="True"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- ملخص الخزانة -->
            <Border Grid.Column="1" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,10,10,10" CornerRadius="4">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان الخزانة -->
                    <Border Grid.Row="0" Background="#2196F3" Margin="0" Padding="0,10" CornerRadius="4,4,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="حالة الخزانة" FontSize="18" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="💰" FontSize="18" Margin="10,0,0,0" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- معلومات الخزانة -->
                    <Grid Grid.Row="1" Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الخزانة:" FontWeight="SemiBold"/>
                        <TextBlock x:Name="CashRegisterNameTextBlock" Grid.Row="0" Grid.Column="1" Text="الخزانة الرئيسية"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الرصيد الحالي:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="CurrentBalanceTextBlock" Grid.Row="1" Grid.Column="1" Text="0.00" Foreground="#4CAF50" FontWeight="Bold" Margin="0,10,0,0"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي المنصرفات:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="TotalExpensesTextBlock" Grid.Row="2" Grid.Column="1" Text="0.00" Foreground="#F44336" Margin="0,10,0,0"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="آخر تحديث:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="LastUpdateTextBlock" Grid.Row="3" Grid.Column="1" Text="2023/01/01 00:00:00" Margin="0,10,0,0"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="المستخدم المسؤول:" FontWeight="SemiBold" Margin="0,10,0,0"/>
                        <TextBlock x:Name="ResponsibleUserTextBlock" Grid.Row="4" Grid.Column="1" Text="المدير" Margin="0,10,0,0"/>
                    </Grid>

                    <!-- أزرار الخزانة -->
                    <StackPanel Grid.Row="2" Margin="10">
                        <Button x:Name="ViewCashRegisterButton" Content="عرض تفاصيل الخزانة" Margin="0,0,0,10" Padding="10" Background="#2196F3" Foreground="White" Click="ViewCashRegisterButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📊" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="عرض تفاصيل الخزانة" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="AddDepositButton" Content="إضافة إيداع" Margin="0,0,0,10" Padding="10" Background="#4CAF50" Foreground="White" Click="AddDepositButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="⬆️" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="إضافة إيداع" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="AddWithdrawalButton" Content="إضافة سحب" Margin="0,0,0,10" Padding="10" Background="#FF9800" Foreground="White" Click="AddWithdrawalButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="⬇️" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="إضافة سحب" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="PrintReportButton" Content="طباعة تقرير" Padding="10" Background="#607D8B" Foreground="White" Click="PrintReportButton_Click">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🖨️" FontSize="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="طباعة تقرير" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
