<UserControl x:Class="GoPOS.UI.Views.InventoryCheckView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان وشريط الأدوات -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="الجرد الدوري" FontSize="24" FontWeight="Bold"/>

            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <ComboBox x:Name="WarehouseComboBox" Width="150" Height="30" Margin="0,0,10,0" SelectionChanged="WarehouseComboBox_SelectionChanged">
                    <ComboBoxItem Content="جميع المخازن" IsSelected="True"/>
                    <ComboBoxItem Content="المخزن الرئيسي"/>
                    <ComboBoxItem Content="مخزن الفرع 1"/>
                    <ComboBoxItem Content="مخزن الفرع 2"/>
                </ComboBox>
                <ComboBox x:Name="StatusFilterComboBox" Width="150" Height="30" Margin="0,0,10,0" SelectionChanged="StatusFilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                    <ComboBoxItem Content="مسودة"/>
                    <ComboBoxItem Content="قيد التنفيذ"/>
                    <ComboBoxItem Content="مكتمل"/>
                    <ComboBoxItem Content="معتمد"/>
                    <ComboBoxItem Content="ملغي"/>
                </ComboBox>
                <Button x:Name="RefreshButton" Content="تحديث" Width="80" Height="30" Click="RefreshButton_Click"/>
            </StackPanel>
        </Grid>

        <!-- محتوى الجرد -->
        <TabControl Grid.Row="1">
            <!-- قائمة عمليات الجرد -->
            <TabItem Header="عمليات الجرد">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط أدوات الجرد -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,10">
                        <Button x:Name="NewCheckButton" Content="جرد جديد" Width="120" Height="30" Margin="0,0,10,0" Click="NewCheckButton_Click"/>
                        <Button x:Name="EditCheckButton" Content="تعديل" Width="80" Height="30" Margin="0,0,10,0" Click="EditCheckButton_Click"/>
                        <Button x:Name="DeleteCheckButton" Content="حذف" Width="80" Height="30" Margin="0,0,10,0" Background="#F44336" Foreground="White" Click="DeleteCheckButton_Click"/>
                        <Button x:Name="PrintCheckButton" Content="طباعة" Width="80" Height="30" Margin="0,0,10,0" Click="PrintCheckButton_Click"/>
                        <Button x:Name="ExportCheckButton" Content="تصدير" Width="80" Height="30" Click="ExportCheckButton_Click"/>
                    </StackPanel>

                    <!-- جدول عمليات الجرد -->
                    <DataGrid x:Name="InventoryChecksDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectionChanged="InventoryChecksDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الجرد" Binding="{Binding CheckNumber}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding CheckDate, StringFormat={}{0:yyyy/MM/dd}}" Width="100"/>
                            <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="150"/>
                            <DataGridTextColumn Header="المستخدم" Binding="{Binding UserName}" Width="120"/>
                            <DataGridTextColumn Header="عدد المنتجات" Binding="{Binding ItemCount}" Width="100"/>
                            <DataGridTextColumn Header="المنتجات المجرودة" Binding="{Binding CheckedItemCount}" Width="120"/>
                            <DataGridTextColumn Header="الفروقات" Binding="{Binding DifferenceCount}" Width="80"/>
                            <DataGridTemplateColumn Header="الحالة" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding StatusColor}" CornerRadius="3" Padding="5,2">
                                            <TextBlock Text="{Binding StatusText}" Foreground="White" HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Header="إجراءات" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="عرض" Margin="5,0" Click="ViewCheckButton_Click" Tag="{Binding}"/>
                                            <Button Content="تنفيذ الجرد" Margin="5,0" Click="PerformCheckButton_Click" Tag="{Binding}" Visibility="{Binding CanPerform}"/>
                                            <Button Content="اعتماد" Margin="5,0" Click="ApproveCheckButton_Click" Tag="{Binding}" Visibility="{Binding CanApprove}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- المنتجات منتهية الصلاحية -->
            <TabItem Header="المنتجات منتهية الصلاحية">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط أدوات المنتجات منتهية الصلاحية -->
                    <Grid Grid.Row="0" Margin="0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="عرض المنتجات التي تنتهي خلال:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox x:Name="ExpiryPeriodComboBox" Grid.Column="1" Width="120" Height="30" Margin="0,0,10,0" SelectionChanged="ExpiryPeriodComboBox_SelectionChanged">
                            <ComboBoxItem Content="7 أيام" IsSelected="True"/>
                            <ComboBoxItem Content="15 يوم"/>
                            <ComboBoxItem Content="30 يوم"/>
                            <ComboBoxItem Content="60 يوم"/>
                            <ComboBoxItem Content="90 يوم"/>
                            <ComboBoxItem Content="المنتهية فقط"/>
                            <ComboBoxItem Content="الكل"/>
                        </ComboBox>
                        
                        <Button x:Name="RemoveExpiredButton" Grid.Column="4" Content="إزالة المنتهية" Width="120" Height="30" Background="#F44336" Foreground="White" Click="RemoveExpiredButton_Click"/>
                    </Grid>

                    <!-- جدول المنتجات منتهية الصلاحية -->
                    <DataGrid x:Name="ExpiryProductsDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Extended">
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Width="30">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox IsChecked="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                            <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="تاريخ الانتهاء" Binding="{Binding ExpiryDate, StringFormat={}{0:yyyy/MM/dd}}" Width="120"/>
                            <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="120"/>
                            <DataGridTemplateColumn Header="الحالة" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding StatusColor}" CornerRadius="3" Padding="5,2">
                                            <TextBlock Text="{Binding StatusText}" Foreground="White" HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="الأيام المتبقية" Binding="{Binding DaysRemaining}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
