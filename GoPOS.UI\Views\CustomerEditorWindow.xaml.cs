using System;
using System.Threading.Tasks;
using System.Windows;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;
using GoPOS.UI.ViewModels;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for CustomerEditorWindow.xaml
    /// </summary>
    public partial class CustomerEditorWindow : Window
    {
        private CustomerViewModel _customer;
        private bool _isEditMode;
        private CustomerRepository _customerRepository;

        public CustomerEditorWindow(CustomerRepository customerRepository)
        {
            InitializeComponent();
            _isEditMode = false;
            _customer = new CustomerViewModel();
            _customerRepository = customerRepository;
        }

        public CustomerEditorWindow(CustomerViewModel customer, CustomerRepository customerRepository)
        {
            InitializeComponent();
            _isEditMode = true;
            _customer = customer;
            _customerRepository = customerRepository;
            TitleTextBlock.Text = "تعديل بيانات العميل";
            LoadCustomerData();
        }

        private void LoadCustomerData()
        {
            NameTextBox.Text = _customer.Name;
            PhoneTextBox.Text = _customer.Phone ?? string.Empty;
            EmailTextBox.Text = _customer.Email ?? string.Empty;
            AddressTextBox.Text = _customer.Address ?? string.Empty;
            NotesTextBox.Text = _customer.Notes ?? string.Empty;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
            {
                return;
            }

            try
            {
                // تعطيل زر الحفظ لمنع النقر المتكرر
                SaveButton.IsEnabled = false;

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // جمع البيانات من النموذج
                _customer.Name = NameTextBox.Text.Trim();
                _customer.Phone = PhoneTextBox.Text.Trim();
                _customer.Email = EmailTextBox.Text.Trim();
                _customer.Address = AddressTextBox.Text.Trim();
                _customer.Notes = NotesTextBox.Text.Trim();
                _customer.IsActive = true;

                // تحويل نموذج العرض إلى نموذج البيانات
                Customer customer = new Customer
                {
                    Name = _customer.Name,
                    Phone = _customer.Phone,
                    Email = _customer.Email,
                    Address = _customer.Address,
                    Notes = _customer.Notes,
                    IsActive = _customer.IsActive
                };

                // حفظ البيانات في قاعدة البيانات
                if (_isEditMode)
                {
                    // تحديث العميل الموجود
                    customer.Id = _customer.Id;
                    await _customerRepository.UpdateAsync(customer);
                    MessageBox.Show("تم تحديث بيانات العميل بنجاح", "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // إضافة عميل جديد
                    int newId = await _customerRepository.AddAsync(customer);
                    _customer.Id = newId;
                    MessageBox.Show("تمت إضافة العميل بنجاح", "تمت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ بيانات العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين زر الحفظ
                SaveButton.IsEnabled = true;

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم العميل", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال رقم هاتف العميل", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneTextBox.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال بريد إلكتروني صحيح", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                EmailTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }



        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
