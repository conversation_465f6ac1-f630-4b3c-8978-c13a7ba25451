using System;
using System.Windows;
using System.Windows.Controls;
using GoPOS.Core.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for AddExpenseWindow.xaml
    /// </summary>
    public partial class AddExpenseWindow : Window
    {
        private Expense _existingExpense;
        private bool _isEditMode;

        /// <summary>
        /// المنصرف الجديد أو المعدل
        /// </summary>
        public Expense NewExpense { get; private set; }

        /// <summary>
        /// إنشاء نافذة إضافة منصرف جديد
        /// </summary>
        public AddExpenseWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            ExpenseDatePicker.SelectedDate = DateTime.Now;
            ExpenseTypeComboBox.SelectedIndex = 0;
        }

        /// <summary>
        /// إنشاء نافذة تعديل منصرف موجود
        /// </summary>
        /// <param name="expense">المنصرف المراد تعديله</param>
        public AddExpenseWindow(Expense expense)
        {
            InitializeComponent();
            _existingExpense = expense;
            _isEditMode = true;
            
            // تغيير عنوان النافذة
            Title = "تعديل منصرف";
            WindowTitleTextBlock.Text = "تعديل منصرف";
            
            // ملء البيانات
            AmountTextBox.Text = expense.Amount.ToString("N2");
            DescriptionTextBox.Text = expense.Description;
            ExpenseTypeComboBox.SelectedIndex = (int)expense.Type - 1;
            BeneficiaryTextBox.Text = expense.Beneficiary;
            DocumentNumberTextBox.Text = expense.DocumentNumber;
            ExpenseDatePicker.SelectedDate = expense.Date;
            NotesTextBox.Text = expense.Notes;
            ConfirmExpenseCheckBox.IsChecked = expense.IsConfirmed;
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التحقق من صحة المبلغ المدخل
            if (!string.IsNullOrWhiteSpace(AmountTextBox.Text))
            {
                if (!decimal.TryParse(AmountTextBox.Text, out _))
                {
                    // إذا كان المبلغ غير صحيح، قم بإظهار رسالة خطأ
                    ErrorMessageTextBlock.Text = "الرجاء إدخال مبلغ صحيح";
                    ErrorMessageTextBlock.Visibility = Visibility.Visible;
                }
                else
                {
                    // إخفاء رسالة الخطأ
                    ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInputs())
                {
                    return;
                }

                // إنشاء كائن المنصرف
                NewExpense = new Expense
                {
                    Id = _isEditMode ? _existingExpense.Id : GetNextExpenseId(),
                    Date = ExpenseDatePicker.SelectedDate.Value,
                    Amount = decimal.Parse(AmountTextBox.Text),
                    Description = DescriptionTextBox.Text.Trim(),
                    Type = (ExpenseType)(ExpenseTypeComboBox.SelectedIndex + 1),
                    UserId = 1, // سيتم استبداله بمعرف المستخدم الحالي
                    UserName = "المدير", // سيتم استبداله باسم المستخدم الحالي
                    IsConfirmed = ConfirmExpenseCheckBox.IsChecked.Value,
                    Notes = NotesTextBox.Text.Trim(),
                    DocumentNumber = DocumentNumberTextBox.Text.Trim(),
                    Beneficiary = BeneficiaryTextBox.Text.Trim()
                };

                // إغلاق النافذة مع إرجاع قيمة صحيحة
                DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المنصرف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة بدون حفظ
            DialogResult = false;
        }

        private bool ValidateInputs()
        {
            // التحقق من المبلغ
            if (string.IsNullOrWhiteSpace(AmountTextBox.Text) || !decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                ErrorMessageTextBlock.Text = "الرجاء إدخال مبلغ صحيح أكبر من صفر";
                ErrorMessageTextBlock.Visibility = Visibility.Visible;
                AmountTextBox.Focus();
                return false;
            }

            // التحقق من الوصف
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                ErrorMessageTextBlock.Text = "الرجاء إدخال وصف للمنصرف";
                ErrorMessageTextBlock.Visibility = Visibility.Visible;
                DescriptionTextBox.Focus();
                return false;
            }

            // التحقق من نوع المنصرف
            if (ExpenseTypeComboBox.SelectedIndex < 0)
            {
                ErrorMessageTextBlock.Text = "الرجاء اختيار نوع المنصرف";
                ErrorMessageTextBlock.Visibility = Visibility.Visible;
                ExpenseTypeComboBox.Focus();
                return false;
            }

            // التحقق من التاريخ
            if (!ExpenseDatePicker.SelectedDate.HasValue)
            {
                ErrorMessageTextBlock.Text = "الرجاء اختيار تاريخ المنصرف";
                ErrorMessageTextBlock.Visibility = Visibility.Visible;
                ExpenseDatePicker.Focus();
                return false;
            }

            // إخفاء رسالة الخطأ
            ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
            return true;
        }

        private int GetNextExpenseId()
        {
            // في التطبيق الحقيقي، سيتم الحصول على المعرف التالي من قاعدة البيانات
            // هنا نستخدم قيمة عشوائية للتجربة
            Random random = new Random();
            return random.Next(100, 1000);
        }
    }
}
