using System;
using System.Collections.Generic;

namespace GoPOS.Data.Models
{
    /// <summary>
    /// طريقة الدفع
    /// </summary>
    public enum PaymentMethod
    {
        /// <summary>
        /// نقدي
        /// </summary>
        Cash = 0,
        
        /// <summary>
        /// بطاقة ائتمان
        /// </summary>
        CreditCard = 1,
        
        /// <summary>
        /// تحويل بنكي
        /// </summary>
        BankTransfer = 2,
        
        /// <summary>
        /// شيك
        /// </summary>
        Check = 3,
        
        /// <summary>
        /// أقساط
        /// </summary>
        Installment = 4,
        
        /// <summary>
        /// أخرى
        /// </summary>
        Other = 5
    }

    /// <summary>
    /// حالة الفاتورة
    /// </summary>
    public enum InvoiceStatus
    {
        /// <summary>
        /// مسودة
        /// </summary>
        Draft = 0,
        
        /// <summary>
        /// معلقة
        /// </summary>
        Pending = 1,
        
        /// <summary>
        /// مكتملة
        /// </summary>
        Completed = 2,
        
        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 3,
        
        /// <summary>
        /// مرتجعة
        /// </summary>
        Returned = 4
    }

    /// <summary>
    /// نموذج الفاتورة
    /// </summary>
    public class Invoice
    {
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        public string InvoiceNumber { get; set; }
        
        /// <summary>
        /// تاريخ الفاتورة
        /// </summary>
        public DateTime Date { get; set; }
        
        /// <summary>
        /// معرف العميل
        /// </summary>
        public int? CustomerId { get; set; }
        
        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName { get; set; }
        
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// المجموع الفرعي
        /// </summary>
        public decimal Subtotal { get; set; }
        
        /// <summary>
        /// مبلغ الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; }
        
        /// <summary>
        /// مبلغ الخصم
        /// </summary>
        public decimal DiscountAmount { get; set; }
        
        /// <summary>
        /// المجموع
        /// </summary>
        public decimal Total { get; set; }
        
        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; }
        
        /// <summary>
        /// حالة الفاتورة
        /// </summary>
        public InvoiceStatus Status { get; set; }
        
        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// عناصر الفاتورة
        /// </summary>
        public List<InvoiceItem> Items { get; set; }
        
        /// <summary>
        /// مدفوعات الفاتورة
        /// </summary>
        public List<InvoicePayment> Payments { get; set; }
    }

    /// <summary>
    /// نموذج عنصر الفاتورة
    /// </summary>
    public class InvoiceItem
    {
        /// <summary>
        /// معرف العنصر
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int InvoiceId { get; set; }
        
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int ProductId { get; set; }
        
        /// <summary>
        /// اسم المنتج
        /// </summary>
        public string ProductName { get; set; }
        
        /// <summary>
        /// باركود المنتج
        /// </summary>
        public string Barcode { get; set; }
        
        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity { get; set; }
        
        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }
        
        /// <summary>
        /// مبلغ الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; }
        
        /// <summary>
        /// مبلغ الخصم
        /// </summary>
        public decimal DiscountAmount { get; set; }
        
        /// <summary>
        /// المجموع
        /// </summary>
        public decimal Total { get; set; }
    }

    /// <summary>
    /// نموذج مدفوعات الفاتورة
    /// </summary>
    public class InvoicePayment
    {
        /// <summary>
        /// معرف المدفوعات
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int InvoiceId { get; set; }
        
        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; }
        
        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// رقم المرجع
        /// </summary>
        public string ReferenceNumber { get; set; }
        
        /// <summary>
        /// تاريخ الدفع
        /// </summary>
        public DateTime PaymentDate { get; set; }
    }
}
