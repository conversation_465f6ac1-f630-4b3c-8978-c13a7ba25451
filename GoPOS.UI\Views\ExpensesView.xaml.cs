using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for ExpensesView.xaml
    /// </summary>
    public partial class ExpensesView : UserControl
    {
        private ObservableCollection<Expense> _expenses;
        private decimal _totalExpenses;
        private ExpenseRepository _expenseRepository;
        private DatabaseContext _dbContext;

        public ExpensesView()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _expenseRepository = new ExpenseRepository(_dbContext);

            InitializeDataAsync();
        }

        private async void InitializeDataAsync()
        {
            try
            {
                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;
                NoExpensesMessage.Visibility = Visibility.Collapsed;

                // تهيئة قائمة المنصرفات
                _expenses = new ObservableCollection<Expense>();
                ExpensesDataGrid.ItemsSource = _expenses;

                // تحميل بيانات المنصرفات من قاعدة البيانات
                await Task.Run(async () =>
                {
                    try
                    {
                        // الحصول على المنصرفات من قاعدة البيانات
                        var expensesList = await _expenseRepository.GetAllAsync();

                        // تحديث واجهة المستخدم
                        Dispatcher.Invoke(() =>
                        {
                            _expenses.Clear();
                            foreach (var expense in expensesList)
                            {
                                _expenses.Add(expense);
                            }

                            // حساب إجمالي المنصرفات
                            _totalExpenses = _expenses.Sum(e => e.Amount);

                            // تحديث معلومات الخزانة
                            UpdateCashRegisterInfo();

                            // تحديث حالة العرض
                            UpdateDisplayStatus();

                            // إخفاء مؤشر التحميل
                            LoadingIndicator.Visibility = Visibility.Collapsed;
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            LoadingIndicator.Visibility = Visibility.Collapsed;
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
        }

        private List<Expense> GetSampleExpenses()
        {
            // بيانات المنصرفات
            return new List<Expense>
            {
                new Expense
                {
                    Id = 1,
                    Date = DateTime.Now.AddDays(-5),
                    Amount = 500.00m,
                    Description = "فاتورة الكهرباء",
                    Type = ExpenseType.Utilities,
                    UserId = 1,
                    UserName = "المدير",
                    IsConfirmed = true,
                    Beneficiary = "شركة الكهرباء",
                    DocumentNumber = "UTL-001"
                },
                new Expense
                {
                    Id = 2,
                    Date = DateTime.Now.AddDays(-3),
                    Amount = 1200.00m,
                    Description = "إيجار المحل",
                    Type = ExpenseType.Rent,
                    UserId = 1,
                    UserName = "المدير",
                    IsConfirmed = true,
                    Beneficiary = "المالك",
                    DocumentNumber = "RNT-001"
                },
                new Expense
                {
                    Id = 3,
                    Date = DateTime.Now.AddDays(-1),
                    Amount = 300.00m,
                    Description = "مستلزمات مكتبية",
                    Type = ExpenseType.Operational,
                    UserId = 2,
                    UserName = "أحمد",
                    IsConfirmed = true,
                    Beneficiary = "مكتبة الأمل",
                    DocumentNumber = "OPR-001"
                },
                new Expense
                {
                    Id = 4,
                    Date = DateTime.Now,
                    Amount = 2500.00m,
                    Description = "راتب موظف",
                    Type = ExpenseType.Salary,
                    UserId = 1,
                    UserName = "المدير",
                    IsConfirmed = true,
                    Beneficiary = "محمد علي",
                    DocumentNumber = "SAL-001"
                },
                new Expense
                {
                    Id = 5,
                    Date = DateTime.Now,
                    Amount = 800.00m,
                    Description = "شراء بضاعة",
                    Type = ExpenseType.Purchases,
                    UserId = 2,
                    UserName = "أحمد",
                    IsConfirmed = false,
                    Beneficiary = "شركة التوريدات",
                    DocumentNumber = "PUR-001"
                }
            };
        }

        private void UpdateCashRegisterInfo()
        {
            // تحديث معلومات الخزانة في الواجهة
            CashRegisterNameTextBlock.Text = "الخزانة الرئيسية";
            CurrentBalanceTextBlock.Text = "---";
            TotalExpensesTextBlock.Text = $"{_totalExpenses:N2}";
            LastUpdateTextBlock.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            ResponsibleUserTextBlock.Text = "المدير";
        }

        private void UpdateDisplayStatus()
        {
            // إظهار/إخفاء رسالة عدم وجود منصرفات
            if (_expenses.Count == 0)
            {
                NoExpensesMessage.Visibility = Visibility.Visible;
            }
            else
            {
                NoExpensesMessage.Visibility = Visibility.Collapsed;
            }
        }

        private async void AddExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إضافة منصرف جديد
                AddExpenseWindow addExpenseWindow = new AddExpenseWindow();
                addExpenseWindow.Owner = Window.GetWindow(this);

                // عند إغلاق النافذة، تحقق مما إذا تم إضافة منصرف جديد
                if (addExpenseWindow.ShowDialog() == true)
                {
                    // الحصول على المنصرف الجديد
                    Expense newExpense = addExpenseWindow.NewExpense;

                    try
                    {
                        // إضافة المنصرف إلى قاعدة البيانات
                        int newExpenseId = await _expenseRepository.AddAsync(newExpense);

                        // تحديث معرف المنصرف
                        newExpense.Id = newExpenseId;

                        // إضافة المنصرف إلى القائمة
                        _expenses.Add(newExpense);

                        // تحديث إجمالي المنصرفات
                        _totalExpenses += newExpense.Amount;

                        // تحديث معلومات الخزانة
                        UpdateCashRegisterInfo();

                        // تحديث حالة العرض
                        UpdateDisplayStatus();

                        // عرض رسالة نجاح
                        MessageBox.Show("تم إضافة المنصرف بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"حدث خطأ أثناء حفظ المنصرف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المنصرف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // إعادة تحميل البيانات
            InitializeDataAsync();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchExpenses();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchExpenses();
            }
        }

        private async void SearchExpenses()
        {
            try
            {
                string searchText = SearchTextBox.Text.Trim().ToLower();
                if (string.IsNullOrEmpty(searchText))
                {
                    // إعادة تحميل جميع المنصرفات
                    InitializeDataAsync();
                    return;
                }

                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // البحث عن المنصرفات
                await Task.Run(async () =>
                {
                    try
                    {
                        // البحث في المنصرفات باستخدام قاعدة البيانات
                        var searchResults = await _expenseRepository.SearchAsync(searchText);

                        // تحديث واجهة المستخدم
                        Dispatcher.Invoke(() =>
                        {
                            _expenses.Clear();
                            foreach (var expense in searchResults)
                            {
                                _expenses.Add(expense);
                            }

                            // حساب إجمالي المنصرفات
                            _totalExpenses = _expenses.Sum(e => e.Amount);

                            // تحديث معلومات الخزانة
                            UpdateCashRegisterInfo();

                            // تحديث حالة العرض
                            UpdateDisplayStatus();

                            // إخفاء مؤشر التحميل
                            LoadingIndicator.Visibility = Visibility.Collapsed;
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show($"حدث خطأ أثناء البحث عن المنصرفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            LoadingIndicator.Visibility = Visibility.Collapsed;
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث عن المنصرفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
        }

        private void FilterTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void FilterDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void ApplyFilters()
        {
            try
            {
                // الحصول على قيم التصفية
                int selectedTypeIndex = FilterTypeComboBox.SelectedIndex;
                DateTime? selectedDate = FilterDatePicker.SelectedDate;

                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // تطبيق التصفية
                await Task.Run(async () =>
                {
                    try
                    {
                        IEnumerable<Expense> filteredExpenses;

                        // تطبيق تصفية النوع
                        if (selectedTypeIndex > 0 && selectedDate.HasValue)
                        {
                            // تصفية حسب النوع والتاريخ
                            ExpenseType selectedType = (ExpenseType)selectedTypeIndex;
                            DateTime filterDate = selectedDate.Value.Date;

                            // الحصول على المنصرفات المصفاة من قاعدة البيانات
                            var allExpenses = await _expenseRepository.GetAllAsync();
                            filteredExpenses = allExpenses.Where(e => e.Type == selectedType && e.Date.Date == filterDate);
                        }
                        else if (selectedTypeIndex > 0)
                        {
                            // تصفية حسب النوع فقط
                            ExpenseType selectedType = (ExpenseType)selectedTypeIndex;
                            filteredExpenses = await _expenseRepository.GetByTypeAsync(selectedType);
                        }
                        else if (selectedDate.HasValue)
                        {
                            // تصفية حسب التاريخ فقط
                            DateTime filterDate = selectedDate.Value.Date;
                            filteredExpenses = await _expenseRepository.GetByDateAsync(filterDate);
                        }
                        else
                        {
                            // الحصول على جميع المنصرفات
                            filteredExpenses = await _expenseRepository.GetAllAsync();
                        }

                        // تحديث واجهة المستخدم
                        Dispatcher.Invoke(() =>
                        {
                            _expenses.Clear();
                            foreach (var expense in filteredExpenses)
                            {
                                _expenses.Add(expense);
                            }

                            // حساب إجمالي المنصرفات
                            _totalExpenses = _expenses.Sum(e => e.Amount);

                            // تحديث معلومات الخزانة
                            UpdateCashRegisterInfo();

                            // تحديث حالة العرض
                            UpdateDisplayStatus();

                            // إخفاء مؤشر التحميل
                            LoadingIndicator.Visibility = Visibility.Collapsed;
                        });
                    }
                    catch (Exception ex)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show($"حدث خطأ أثناء تطبيق التصفية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            LoadingIndicator.Visibility = Visibility.Collapsed;
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تطبيق التصفية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
        }

        private void ExpensesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق هنا إذا كنت بحاجة إلى التفاعل مع تحديد المنصرفات
        }

        private async void EditExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على المنصرف المحدد
                Button button = sender as Button;
                if (button != null && button.Tag is Expense selectedExpense)
                {
                    // حفظ المبلغ القديم للمنصرف
                    decimal oldAmount = selectedExpense.Amount;

                    // فتح نافذة تعديل المنصرف
                    AddExpenseWindow editExpenseWindow = new AddExpenseWindow(selectedExpense);
                    editExpenseWindow.Owner = Window.GetWindow(this);

                    // عند إغلاق النافذة، تحقق مما إذا تم تعديل المنصرف
                    if (editExpenseWindow.ShowDialog() == true)
                    {
                        // الحصول على المنصرف المعدل
                        Expense updatedExpense = editExpenseWindow.NewExpense;

                        try
                        {
                            // تحديث المنصرف في قاعدة البيانات
                            await _expenseRepository.UpdateAsync(updatedExpense);

                            // تحديث المنصرف في القائمة
                            int index = _expenses.IndexOf(selectedExpense);
                            if (index >= 0)
                            {
                                _expenses[index] = updatedExpense;
                            }

                            // تحديث إجمالي المنصرفات
                            _totalExpenses = _totalExpenses - oldAmount + updatedExpense.Amount;

                            // تحديث معلومات الخزانة
                            UpdateCashRegisterInfo();

                            // عرض رسالة نجاح
                            MessageBox.Show("تم تعديل المنصرف بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"حدث خطأ أثناء حفظ التعديلات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل المنصرف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على المنصرف المحدد
                Button button = sender as Button;
                if (button != null && button.Tag is Expense selectedExpense)
                {
                    // تأكيد الحذف
                    MessageBoxResult result = MessageBox.Show(
                        $"هل أنت متأكد من حذف المنصرف '{selectedExpense.Description}' بمبلغ {selectedExpense.Amount:N2}؟",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            // حذف المنصرف من قاعدة البيانات
                            await _expenseRepository.DeleteAsync(selectedExpense.Id);

                            // حذف المنصرف من القائمة
                            _expenses.Remove(selectedExpense);

                            // تحديث إجمالي المنصرفات
                            _totalExpenses -= selectedExpense.Amount;

                            // تحديث معلومات الخزانة
                            UpdateCashRegisterInfo();

                            // تحديث حالة العرض
                            UpdateDisplayStatus();

                            // عرض رسالة نجاح
                            MessageBox.Show("تم حذف المنصرف بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"حدث خطأ أثناء حذف المنصرف من قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف المنصرف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على المنصرف المحدد
                Button button = sender as Button;
                if (button != null && button.Tag is Expense selectedExpense)
                {
                    // محاكاة طباعة المنصرف
                    MessageBox.Show($"جاري طباعة سند الصرف رقم {selectedExpense.Id}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة المنصرف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewCashRegisterButton_Click(object sender, RoutedEventArgs e)
        {
            // محاكاة عرض تفاصيل الخزانة
            MessageBox.Show("سيتم فتح شاشة تفاصيل الخزانة", "تفاصيل الخزانة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddDepositButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // محاكاة إضافة إيداع
                string amountStr = ShowInputDialog("أدخل مبلغ الإيداع:", "إضافة إيداع", "0.00");

                if (!string.IsNullOrWhiteSpace(amountStr) && decimal.TryParse(amountStr, out decimal amount) && amount > 0)
                {
                    // تحديث رصيد الخزانة
                    _cashRegister.CurrentBalance += amount;
                    _cashRegister.LastUpdated = DateTime.Now;

                    // تحديث معلومات الخزانة
                    UpdateCashRegisterInfo();

                    // عرض رسالة نجاح
                    MessageBox.Show($"تم إضافة إيداع بمبلغ {amount:N2} بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة الإيداع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddWithdrawalButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // محاكاة إضافة سحب
                string amountStr = ShowInputDialog("أدخل مبلغ السحب:", "إضافة سحب", "0.00");

                if (!string.IsNullOrWhiteSpace(amountStr) && decimal.TryParse(amountStr, out decimal amount) && amount > 0)
                {
                    if (amount > _cashRegister.CurrentBalance)
                    {
                        MessageBox.Show("مبلغ السحب أكبر من الرصيد الحالي للخزانة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // تحديث رصيد الخزانة
                    _cashRegister.CurrentBalance -= amount;
                    _cashRegister.LastUpdated = DateTime.Now;

                    // تحديث معلومات الخزانة
                    UpdateCashRegisterInfo();

                    // عرض رسالة نجاح
                    MessageBox.Show($"تم إضافة سحب بمبلغ {amount:N2} بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة السحب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            // محاكاة طباعة تقرير
            MessageBox.Show("سيتم طباعة تقرير المنصرفات", "طباعة تقرير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private string ShowInputDialog(string prompt, string title, string defaultValue = "")
        {
            // إنشاء نافذة حوار بسيطة
            Window inputDialog = new Window
            {
                Title = title,
                Width = 300,
                Height = 150,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                ResizeMode = ResizeMode.NoResize,
                WindowStyle = WindowStyle.ToolWindow
            };

            // إنشاء تخطيط النافذة
            Grid grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // إضافة نص التوجيه
            TextBlock promptText = new TextBlock
            {
                Text = prompt,
                Margin = new Thickness(10),
                TextWrapping = TextWrapping.Wrap
            };
            Grid.SetRow(promptText, 0);

            // إضافة مربع النص
            TextBox inputTextBox = new TextBox
            {
                Text = defaultValue,
                Margin = new Thickness(10),
                Padding = new Thickness(5),
                Height = 30
            };
            Grid.SetRow(inputTextBox, 1);

            // إضافة أزرار الإجراءات
            StackPanel buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(10)
            };

            Button okButton = new Button
            {
                Content = "موافق",
                Width = 80,
                Height = 30,
                Margin = new Thickness(5, 0, 0, 0),
                IsDefault = true
            };

            Button cancelButton = new Button
            {
                Content = "إلغاء",
                Width = 80,
                Height = 30,
                IsCancel = true
            };

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(okButton);
            Grid.SetRow(buttonPanel, 2);

            // إضافة العناصر إلى الشبكة
            grid.Children.Add(promptText);
            grid.Children.Add(inputTextBox);
            grid.Children.Add(buttonPanel);

            // تعيين محتوى النافذة
            inputDialog.Content = grid;

            // تعيين التركيز على مربع النص
            inputDialog.Loaded += (s, e) => inputTextBox.Focus();

            // تعيين نتيجة النافذة
            string result = null;
            okButton.Click += (s, e) =>
            {
                result = inputTextBox.Text;
                inputDialog.DialogResult = true;
            };

            // عرض النافذة وانتظار النتيجة
            bool? dialogResult = inputDialog.ShowDialog();

            // إرجاع النتيجة
            return dialogResult == true ? result : null;
        }
    }
}
