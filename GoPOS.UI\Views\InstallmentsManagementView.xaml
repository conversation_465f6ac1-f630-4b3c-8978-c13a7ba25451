<UserControl x:Class="GoPOS.UI.Views.InstallmentsManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Vertical">
                <TextBlock Text="إدارة الأقساط" FontSize="24" FontWeight="Bold"/>
                <TextBlock x:Name="TotalDueTextBlock" Text="إجمالي المستحقات: 0.00 ر.س" Foreground="#2196F3" FontSize="14" Margin="0,5,0,0"/>
            </StackPanel>

            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,0,5,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="العميل:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                        <ComboBox x:Name="CustomerFilterComboBox"
                                  Grid.Column="1"
                                  Width="200"
                                  Height="40"
                                  Padding="5"
                                  BorderThickness="0"
                                  DisplayMemberPath="Name"
                                  SelectionChanged="CustomerFilterComboBox_SelectionChanged"/>
                    </Grid>
                </Border>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4" Margin="0,0,5,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="الحالة:" VerticalAlignment="Center" Margin="10,0,5,0"/>
                        <ComboBox x:Name="StatusFilterComboBox"
                                  Grid.Column="1"
                                  Width="120"
                                  Height="40"
                                  Padding="5"
                                  BorderThickness="0"
                                  SelectionChanged="StatusFilterComboBox_SelectionChanged">
                            <ComboBoxItem Content="الكل" IsSelected="True"/>
                            <ComboBoxItem Content="مستحقة"/>
                            <ComboBoxItem Content="مدفوعة"/>
                            <ComboBoxItem Content="متأخرة"/>
                        </ComboBox>
                    </Grid>
                </Border>
                <Button x:Name="RefreshButton"
                        Content="تحديث"
                        Width="80"
                        Height="40"
                        Click="RefreshButton_Click"
                        Background="#2196F3"
                        Foreground="White">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                        <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>

        <!-- جدول خطط الأقساط -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="خطط الأقساط" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
            <DataGrid x:Name="InstallmentPlansDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectionChanged="InstallmentPlansDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الطلب" Binding="{Binding OrderId}" Width="80"/>
                    <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                    <DataGridTextColumn Header="إجمالي المبلغ" Binding="{Binding TotalAmount, StringFormat={}{0:N2}}" Width="100"/>
                    <DataGridTextColumn Header="الدفعة المقدمة" Binding="{Binding DownPayment, StringFormat={}{0:N2}}" Width="100"/>
                    <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingAmount, StringFormat={}{0:N2}}" Width="100"/>
                    <DataGridTextColumn Header="عدد الأقساط" Binding="{Binding NumberOfInstallments}" Width="80"/>
                    <DataGridTextColumn Header="تاريخ البدء" Binding="{Binding StartDate, StringFormat={}{0:yyyy/MM/dd}}" Width="100"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
                    <DataGridTemplateColumn Header="إجراءات" Width="*">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="طباعة" Margin="5,0" Click="PrintPlanButton_Click" Tag="{Binding}"/>
                                    <Button Content="إلغاء الخطة" Margin="5,0" Click="CancelPlanButton_Click" Tag="{Binding}" Visibility="{Binding CanCancel}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <!-- عنوان جدول الأقساط -->
        <Grid Grid.Row="2" Margin="0,20,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="الأقساط" FontSize="16" FontWeight="Bold"/>
            <Button x:Name="PayInstallmentButton" Grid.Column="1" Content="تسديد القسط المحدد" Width="150" Height="30" IsEnabled="False" Click="PayInstallmentButton_Click"/>
        </Grid>

        <!-- جدول الأقساط -->
        <DataGrid x:Name="InstallmentsDataGrid"
                  Grid.Row="3"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  SelectionMode="Single"
                  SelectionChanged="InstallmentsDataGrid_SelectionChanged"
                  AlternatingRowBackground="#F5F5F5"
                  BorderBrush="#DDDDDD"
                  BorderThickness="1"
                  RowHeight="35"
                  HeadersVisibility="Column"
                  GridLinesVisibility="Horizontal"
                  HorizontalGridLinesBrush="#EEEEEE">
            <DataGrid.Resources>
                <Style TargetType="DataGridColumnHeader">
                    <Setter Property="Background" Value="#2196F3"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="FontWeight" Value="SemiBold"/>
                    <Setter Property="Height" Value="40"/>
                    <Setter Property="Padding" Value="10,0"/>
                </Style>
            </DataGrid.Resources>
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم القسط" Binding="{Binding InstallmentNumber}" Width="80">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="5"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat={}{0:N2}}" Width="100">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="5"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="Foreground" Value="#2196F3"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
                <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat={}{0:yyyy/MM/dd}}" Width="120">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="5"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
                <DataGridTextColumn Header="تاريخ الدفع" Binding="{Binding PaymentDate, StringFormat={}{0:yyyy/MM/dd}}" Width="120">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="5"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
                <DataGridTemplateColumn Header="الحالة" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border CornerRadius="4" Padding="5,2" Margin="5">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Status}" Value="0">
                                                <Setter Property="Background" Value="#FFC107"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="1">
                                                <Setter Property="Background" Value="#4CAF50"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="2">
                                                <Setter Property="Background" Value="#F44336"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="{Binding StatusText}"
                                           Foreground="White"
                                           FontWeight="SemiBold"
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="5"/>
                            <Setter Property="TextWrapping" Value="Wrap"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
