using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GoPOS.Core.Models;
using GoPOS.UI.Services;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InvoicesView.xaml
    /// </summary>
    public partial class InvoicesView : UserControl
    {
        private ObservableCollection<InvoiceViewModel> _invoices;
        private InvoiceViewModel _selectedInvoice;
        private decimal _totalSales;
        private decimal _todaySales;
        private decimal _unpaidAmount;

        public InvoicesView()
        {
            InitializeComponent();
            InitializeData();
        }

        private void InitializeData()
        {
            try
            {
                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;
                NoInvoicesMessage.Visibility = Visibility.Collapsed;

                // تهيئة قائمة الفواتير
                _invoices = new ObservableCollection<InvoiceViewModel>();
                InvoicesDataGrid.ItemsSource = _invoices;

                // تحميل بيانات الفواتير بشكل غير متزامن
                System.Threading.Tasks.Task.Run(() =>
                {
                    // محاكاة تأخير تحميل البيانات
                    System.Threading.Thread.Sleep(1000);

                    // تحميل بيانات الفواتير (سيتم استبدالها بقراءة من قاعدة البيانات)
                    List<InvoiceViewModel> invoicesList = GetSampleInvoices();

                    // تحديث واجهة المستخدم
                    Dispatcher.Invoke(() =>
                    {
                        foreach (var invoice in invoicesList)
                        {
                            _invoices.Add(invoice);
                        }

                        // حساب إجماليات الفواتير
                        CalculateTotals();

                        // تحديث معلومات الملخص
                        UpdateSummaryInfo();

                        // تحديث حالة العرض
                        UpdateDisplayStatus();

                        // إخفاء مؤشر التحميل
                        LoadingIndicator.Visibility = Visibility.Collapsed;
                    });
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
        }

        private List<InvoiceViewModel> GetSampleInvoices()
        {
            // بيانات تجريبية للفواتير
            List<InvoiceViewModel> invoices = new List<InvoiceViewModel>();

            // فاتورة 1 - مكتملة ومدفوعة نقداً
            invoices.Add(new InvoiceViewModel
            {
                Id = 1,
                InvoiceNumber = "INV-001",
                Date = DateTime.Now.AddDays(-1),
                CustomerId = 1,
                CustomerName = "عميل نقدي",
                UserId = 1,
                UserName = "المدير",
                Subtotal = 150.00m,
                DiscountAmount = 0,
                DiscountPercentage = 0,
                IsDiscountPercentage = false,
                TaxAmount = 22.50m,
                TaxPercentage = 15,
                Total = 172.50m,
                Status = InvoiceStatus.Completed,
                StatusText = "مكتملة",
                PaymentMethod = InvoicePaymentMethod.Cash,
                PaymentMethodText = "نقداً",
                IsPaid = true,
                PaidAmount = 172.50m,
                RemainingAmount = 0,
                IsInstallment = false,
                InstallmentPlanId = null,
                Notes = "فاتورة نقدية",
                CanEdit = Visibility.Collapsed,
                CanDelete = Visibility.Collapsed
            });

            // فاتورة 2 - مكتملة ومدفوعة ببطاقة ائتمان
            invoices.Add(new InvoiceViewModel
            {
                Id = 2,
                InvoiceNumber = "INV-002",
                Date = DateTime.Now.AddDays(-2),
                CustomerId = 2,
                CustomerName = "أحمد محمد",
                UserId = 1,
                UserName = "المدير",
                Subtotal = 300.00m,
                DiscountAmount = 30.00m,
                DiscountPercentage = 10,
                IsDiscountPercentage = true,
                TaxAmount = 40.50m,
                TaxPercentage = 15,
                Total = 310.50m,
                Status = InvoiceStatus.Completed,
                StatusText = "مكتملة",
                PaymentMethod = InvoicePaymentMethod.CreditCard,
                PaymentMethodText = "بطاقة ائتمان",
                IsPaid = true,
                PaidAmount = 310.50m,
                RemainingAmount = 0,
                IsInstallment = false,
                InstallmentPlanId = null,
                Notes = "فاتورة بطاقة ائتمان",
                CanEdit = Visibility.Collapsed,
                CanDelete = Visibility.Collapsed
            });

            // فاتورة 3 - مكتملة ومدفوعة بالأقساط
            invoices.Add(new InvoiceViewModel
            {
                Id = 3,
                InvoiceNumber = "INV-003",
                Date = DateTime.Now.AddDays(-5),
                CustomerId = 3,
                CustomerName = "سارة أحمد",
                UserId = 2,
                UserName = "أحمد",
                Subtotal = 1200.00m,
                DiscountAmount = 0,
                DiscountPercentage = 0,
                IsDiscountPercentage = false,
                TaxAmount = 180.00m,
                TaxPercentage = 15,
                Total = 1380.00m,
                Status = InvoiceStatus.Completed,
                StatusText = "مكتملة",
                PaymentMethod = InvoicePaymentMethod.Installment,
                PaymentMethodText = "أقساط",
                IsPaid = false,
                PaidAmount = 460.00m,
                RemainingAmount = 920.00m,
                IsInstallment = true,
                InstallmentPlanId = 1,
                Notes = "فاتورة أقساط - 3 أقساط",
                CanEdit = Visibility.Collapsed,
                CanDelete = Visibility.Collapsed
            });

            // فاتورة 4 - مسودة
            invoices.Add(new InvoiceViewModel
            {
                Id = 4,
                InvoiceNumber = "INV-004",
                Date = DateTime.Now,
                CustomerId = 2,
                CustomerName = "أحمد محمد",
                UserId = 1,
                UserName = "المدير",
                Subtotal = 500.00m,
                DiscountAmount = 50.00m,
                DiscountPercentage = 10,
                IsDiscountPercentage = true,
                TaxAmount = 67.50m,
                TaxPercentage = 15,
                Total = 517.50m,
                Status = InvoiceStatus.Draft,
                StatusText = "مسودة",
                PaymentMethod = InvoicePaymentMethod.Cash,
                PaymentMethodText = "نقداً",
                IsPaid = false,
                PaidAmount = 0,
                RemainingAmount = 517.50m,
                IsInstallment = false,
                InstallmentPlanId = null,
                Notes = "فاتورة مسودة",
                CanEdit = Visibility.Visible,
                CanDelete = Visibility.Visible
            });

            // فاتورة 5 - ملغاة
            invoices.Add(new InvoiceViewModel
            {
                Id = 5,
                InvoiceNumber = "INV-005",
                Date = DateTime.Now.AddDays(-3),
                CustomerId = 1,
                CustomerName = "عميل نقدي",
                UserId = 2,
                UserName = "أحمد",
                Subtotal = 75.00m,
                DiscountAmount = 0,
                DiscountPercentage = 0,
                IsDiscountPercentage = false,
                TaxAmount = 11.25m,
                TaxPercentage = 15,
                Total = 86.25m,
                Status = InvoiceStatus.Cancelled,
                StatusText = "ملغاة",
                PaymentMethod = InvoicePaymentMethod.Cash,
                PaymentMethodText = "نقداً",
                IsPaid = false,
                PaidAmount = 0,
                RemainingAmount = 0,
                IsInstallment = false,
                InstallmentPlanId = null,
                Notes = "فاتورة ملغاة",
                CanEdit = Visibility.Collapsed,
                CanDelete = Visibility.Collapsed
            });

            // فاتورة 6 - مرتجعة
            invoices.Add(new InvoiceViewModel
            {
                Id = 6,
                InvoiceNumber = "RET-INV-001",
                Date = DateTime.Now.AddDays(-1),
                CustomerId = 1,
                CustomerName = "عميل نقدي",
                UserId = 1,
                UserName = "المدير",
                Subtotal = -50.00m,
                DiscountAmount = 0,
                DiscountPercentage = 0,
                IsDiscountPercentage = false,
                TaxAmount = -7.50m,
                TaxPercentage = 15,
                Total = -57.50m,
                Status = InvoiceStatus.Returned,
                StatusText = "مرتجعة",
                PaymentMethod = InvoicePaymentMethod.Cash,
                PaymentMethodText = "نقداً",
                IsPaid = true,
                PaidAmount = -57.50m,
                RemainingAmount = 0,
                IsInstallment = false,
                InstallmentPlanId = null,
                Notes = "إرجاع للفاتورة INV-001\nسبب الإرجاع: منتج معيب",
                CanEdit = Visibility.Collapsed,
                CanDelete = Visibility.Collapsed
            });

            return invoices;
        }

        private void CalculateTotals()
        {
            // حساب إجمالي المبيعات
            _totalSales = _invoices
                .Where(i => i.Status == InvoiceStatus.Completed)
                .Sum(i => i.Total);

            // حساب مبيعات اليوم
            _todaySales = _invoices
                .Where(i => i.Status == InvoiceStatus.Completed && i.Date.Date == DateTime.Now.Date)
                .Sum(i => i.Total);

            // حساب المبالغ غير المدفوعة
            _unpaidAmount = _invoices
                .Where(i => i.Status == InvoiceStatus.Completed && !i.IsPaid)
                .Sum(i => i.RemainingAmount);
        }

        private void UpdateSummaryInfo()
        {
            // تحديث معلومات الملخص
            TotalInvoicesCountTextBlock.Text = _invoices.Count(i => i.Status == InvoiceStatus.Completed).ToString();
            TotalSalesTextBlock.Text = _totalSales.ToString("N2");

            TodayInvoicesCountTextBlock.Text = _invoices.Count(i => i.Status == InvoiceStatus.Completed && i.Date.Date == DateTime.Now.Date).ToString();
            TodaySalesTextBlock.Text = _todaySales.ToString("N2");

            UnpaidInvoicesCountTextBlock.Text = _invoices.Count(i => i.Status == InvoiceStatus.Completed && !i.IsPaid).ToString();
            UnpaidAmountTextBlock.Text = _unpaidAmount.ToString("N2");
        }

        private void UpdateDisplayStatus()
        {
            // إظهار/إخفاء رسالة عدم وجود فواتير
            if (_invoices.Count == 0)
            {
                NoInvoicesMessage.Visibility = Visibility.Visible;
            }
            else
            {
                NoInvoicesMessage.Visibility = Visibility.Collapsed;
            }
        }

        private void CreateInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح شاشة نقطة البيع لإنشاء فاتورة جديدة
                MessageBox.Show("سيتم فتح شاشة نقطة البيع لإنشاء فاتورة جديدة", "إنشاء فاتورة", MessageBoxButton.OK, MessageBoxImage.Information);

                // هنا يمكن إضافة كود لفتح شاشة نقطة البيع
                // MainWindow mainWindow = Window.GetWindow(this) as MainWindow;
                // mainWindow?.LoadView(new POSTerminalView());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء فاتورة جديدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // إعادة تحميل البيانات
            InitializeData();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchInvoices();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchInvoices();
            }
        }

        private void SearchInvoices()
        {
            try
            {
                string searchText = SearchTextBox.Text.Trim().ToLower();
                if (string.IsNullOrEmpty(searchText))
                {
                    // إعادة تحميل جميع الفواتير
                    InitializeData();
                    return;
                }

                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // البحث عن الفواتير
                System.Threading.Tasks.Task.Run(() =>
                {
                    // محاكاة تأخير البحث
                    System.Threading.Thread.Sleep(500);

                    // البحث في الفواتير
                    List<InvoiceViewModel> searchResults = GetSampleInvoices().Where(i =>
                        i.InvoiceNumber.ToLower().Contains(searchText) ||
                        i.CustomerName.ToLower().Contains(searchText) ||
                        i.Total.ToString().Contains(searchText)
                    ).ToList();

                    // تحديث واجهة المستخدم
                    Dispatcher.Invoke(() =>
                    {
                        _invoices.Clear();
                        foreach (var invoice in searchResults)
                        {
                            _invoices.Add(invoice);
                        }

                        // حساب إجماليات الفواتير
                        CalculateTotals();

                        // تحديث معلومات الملخص
                        UpdateSummaryInfo();

                        // تحديث حالة العرض
                        UpdateDisplayStatus();

                        // إخفاء مؤشر التحميل
                        LoadingIndicator.Visibility = Visibility.Collapsed;
                    });
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث عن الفواتير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
        }

        private void FilterStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void FilterPaymentComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void FilterDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                // الحصول على قيم التصفية
                int selectedStatusIndex = FilterStatusComboBox.SelectedIndex;
                int selectedPaymentIndex = FilterPaymentComboBox.SelectedIndex;
                DateTime? selectedDate = FilterDatePicker.SelectedDate;

                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // تطبيق التصفية
                System.Threading.Tasks.Task.Run(() =>
                {
                    // محاكاة تأخير التصفية
                    System.Threading.Thread.Sleep(500);

                    // الحصول على جميع الفواتير
                    List<InvoiceViewModel> allInvoices = GetSampleInvoices();

                    // تطبيق تصفية الحالة
                    if (selectedStatusIndex > 0)
                    {
                        InvoiceStatus selectedStatus = (InvoiceStatus)selectedStatusIndex;
                        allInvoices = allInvoices.Where(i => i.Status == selectedStatus).ToList();
                    }

                    // تطبيق تصفية طريقة الدفع
                    if (selectedPaymentIndex > 0)
                    {
                        InvoicePaymentMethod selectedPayment = (InvoicePaymentMethod)selectedPaymentIndex;
                        allInvoices = allInvoices.Where(i => i.PaymentMethod == selectedPayment).ToList();
                    }

                    // تطبيق تصفية التاريخ
                    if (selectedDate.HasValue)
                    {
                        DateTime filterDate = selectedDate.Value.Date;
                        allInvoices = allInvoices.Where(i => i.Date.Date == filterDate).ToList();
                    }

                    // تحديث واجهة المستخدم
                    Dispatcher.Invoke(() =>
                    {
                        _invoices.Clear();
                        foreach (var invoice in allInvoices)
                        {
                            _invoices.Add(invoice);
                        }

                        // حساب إجماليات الفواتير
                        CalculateTotals();

                        // تحديث معلومات الملخص
                        UpdateSummaryInfo();

                        // تحديث حالة العرض
                        UpdateDisplayStatus();

                        // إخفاء مؤشر التحميل
                        LoadingIndicator.Visibility = Visibility.Collapsed;
                    });
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تطبيق التصفية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedInvoice = InvoicesDataGrid.SelectedItem as InvoiceViewModel;
        }

        private void ViewInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الفاتورة المحددة
                Button button = sender as Button;
                if (button != null && button.Tag is InvoiceViewModel selectedInvoice)
                {
                    // عرض تفاصيل الفاتورة
                    InvoiceDetailsWindow detailsWindow = new InvoiceDetailsWindow(selectedInvoice);
                    detailsWindow.Owner = Window.GetWindow(this);

                    // عرض النافذة وانتظار النتيجة
                    bool? result = detailsWindow.ShowDialog();

                    // إذا تم تعديل الفاتورة، تحديث البيانات
                    if (result == true)
                    {
                        // إعادة تحميل البيانات
                        InitializeData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الفاتورة المحددة
                Button button = sender as Button;
                if (button != null && button.Tag is InvoiceViewModel selectedInvoice)
                {
                    // التحقق من إمكانية تعديل الفاتورة
                    if (selectedInvoice.CanEdit != Visibility.Visible)
                    {
                        MessageBox.Show("لا يمكن تعديل هذه الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // تعديل الفاتورة
                    MessageBox.Show($"تعديل الفاتورة رقم {selectedInvoice.InvoiceNumber}", "تعديل الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);

                    // هنا يمكن إضافة كود لفتح شاشة نقطة البيع لتعديل الفاتورة
                    // MainWindow mainWindow = Window.GetWindow(this) as MainWindow;
                    // mainWindow?.LoadView(new POSTerminalView(selectedInvoice));
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الفاتورة المحددة
                Button button = sender as Button;
                if (button != null && button.Tag is InvoiceViewModel selectedInvoice)
                {
                    // التحقق من إمكانية حذف الفاتورة
                    if (selectedInvoice.CanDelete != Visibility.Visible)
                    {
                        MessageBox.Show("لا يمكن حذف هذه الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // تأكيد الحذف
                    MessageBoxResult result = MessageBox.Show(
                        $"هل أنت متأكد من حذف الفاتورة رقم {selectedInvoice.InvoiceNumber}؟",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // حذف الفاتورة من القائمة
                        _invoices.Remove(selectedInvoice);

                        // حساب إجماليات الفواتير
                        CalculateTotals();

                        // تحديث معلومات الملخص
                        UpdateSummaryInfo();

                        // تحديث حالة العرض
                        UpdateDisplayStatus();

                        // عرض رسالة نجاح
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الفاتورة المحددة
                Button button = sender as Button;
                if (button != null && button.Tag is InvoiceViewModel selectedInvoice)
                {
                    // طباعة الفاتورة باستخدام خدمة الطباعة
                    PrintService.PrintInvoice(selectedInvoice);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DailySalesReportButton_Click(object sender, RoutedEventArgs e)
        {
            // محاكاة عرض تقرير مبيعات اليوم
            MessageBox.Show("سيتم عرض تقرير مبيعات اليوم", "تقرير مبيعات اليوم", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void MonthlySalesReportButton_Click(object sender, RoutedEventArgs e)
        {
            // محاكاة عرض تقرير مبيعات الشهر
            MessageBox.Show("سيتم عرض تقرير مبيعات الشهر", "تقرير مبيعات الشهر", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UnpaidInvoicesReportButton_Click(object sender, RoutedEventArgs e)
        {
            // محاكاة عرض تقرير الفواتير غير المدفوعة
            MessageBox.Show("سيتم عرض تقرير الفواتير غير المدفوعة", "تقرير الفواتير غير المدفوعة", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintAllInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود فواتير
                if (_invoices == null || _invoices.Count == 0)
                {
                    MessageBox.Show("لا توجد فواتير للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تأكيد الطباعة
                MessageBoxResult result = MessageBox.Show(
                    $"هل تريد طباعة جميع الفواتير المعروضة ({_invoices.Count} فاتورة)؟",
                    "تأكيد الطباعة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إظهار مؤشر الانتظار
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // طباعة كل فاتورة
                    foreach (var invoice in _invoices)
                    {
                        PrintService.PrintInvoice(invoice);
                    }

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;

                    // عرض رسالة نجاح
                    MessageBox.Show("تمت طباعة جميع الفواتير بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفواتير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }
    }

    /// <summary>
    /// نموذج عرض الفاتورة
    /// </summary>
    public class InvoiceViewModel
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime Date { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
        public decimal Subtotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public bool IsDiscountPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal Total { get; set; }
        public InvoiceStatus Status { get; set; }
        public string StatusText { get; set; }
        public InvoicePaymentMethod PaymentMethod { get; set; }
        public string PaymentMethodText { get; set; }
        public bool IsPaid { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public bool IsInstallment { get; set; }
        public int? InstallmentPlanId { get; set; }
        public string Notes { get; set; }
        public Visibility CanEdit { get; set; }
        public Visibility CanDelete { get; set; }
    }
}
