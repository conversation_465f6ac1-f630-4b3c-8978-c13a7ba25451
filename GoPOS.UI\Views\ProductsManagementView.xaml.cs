using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;
using GoPOS.UI.ViewModels;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for ProductsManagementView.xaml
    /// </summary>
    public partial class ProductsManagementView : UserControl
    {
        private ObservableCollection<ProductItemViewModel> _products;
        private ObservableCollection<CategoryViewModel> _categories;
        private ProductItemViewModel _selectedProduct;
        private CategoryViewModel _selectedCategory;
        private ProductRepository _productRepository;
        private CategoryRepository _categoryRepository;
        private DatabaseContext _dbContext;

        public ProductsManagementView()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _productRepository = new ProductRepository(_dbContext);
            _categoryRepository = new CategoryRepository(_dbContext);

            LoadDataAsync();
        }

        private async void LoadDataAsync()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // تحميل الفئات من قاعدة البيانات
                _categories = new ObservableCollection<CategoryViewModel>();
                _categories.Add(new CategoryViewModel { Id = 0, Name = "جميع الفئات" });

                var categories = await _categoryRepository.GetAllAsync();
                foreach (var category in categories)
                {
                    _categories.Add(new CategoryViewModel { Id = category.Id, Name = category.Name });
                }

                CategoriesListView.ItemsSource = _categories;
                CategoriesListView.SelectedIndex = 0;

                // تحميل المنتجات
                await LoadProductsAsync(0);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async Task LoadProductsAsync(int categoryId)
        {
            try
            {
                _products = new ObservableCollection<ProductItemViewModel>();

                // تحميل المنتجات من قاعدة البيانات
                IEnumerable<Product> products;

                if (categoryId == 0)
                {
                    // تحميل جميع المنتجات
                    products = await _productRepository.GetAllAsync();
                }
                else
                {
                    // تحميل المنتجات حسب الفئة
                    products = await _productRepository.GetByCategoryAsync(categoryId);
                }

                // تحويل المنتجات إلى نماذج عرض
                foreach (var product in products)
                {
                    _products.Add(new ProductItemViewModel
                    {
                        Id = product.Id,
                        Name = product.Name,
                        Code = product.Barcode ?? string.Empty, // استخدام الباركود كرمز للمنتج
                        Barcode = product.Barcode ?? string.Empty,
                        Price = product.Price,
                        CostPrice = product.CostPrice,
                        Quantity = (int)product.Quantity,
                        MinimumQuantity = (int)product.MinQuantity,
                        CategoryId = product.CategoryId ?? 0,
                        CategoryName = product.CategoryName ?? string.Empty,
                        Description = product.Description ?? string.Empty,
                        IsAvailable = product.IsActive
                    });
                }

                ProductsDataGrid.ItemsSource = _products;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CategoriesListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedCategory = CategoriesListView.SelectedItem as CategoryViewModel;
            if (_selectedCategory != null)
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                try
                {
                    await LoadProductsAsync(_selectedCategory.Id);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;
                }
            }
        }

        private void ProductsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedProduct = ProductsDataGrid.SelectedItem as ProductItemViewModel;
            UpdateButtonsState();
        }

        private void UpdateButtonsState()
        {
            bool hasSelection = _selectedProduct != null;
            EditProductButton.IsEnabled = hasSelection;
            DeleteProductButton.IsEnabled = hasSelection;
        }

        private void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر لمنع النقر المتكرر
                AddProductButton.IsEnabled = false;

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // فتح نافذة إضافة منتج جديد
                ProductEditorWindow editorWindow = new ProductEditorWindow();
                editorWindow.Owner = Window.GetWindow(this);

                // إعادة المؤشر إلى الوضع الطبيعي قبل عرض النافذة
                Cursor = System.Windows.Input.Cursors.Arrow;

                bool? result = editorWindow.ShowDialog();

                // إعادة تحميل المنتجات بعد الإضافة
                if (result == true)
                {
                    // إظهار مؤشر الانتظار مرة أخرى
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // إعادة تحميل المنتجات
                    LoadProducts(_selectedCategory?.Id ?? 0);

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة منتج جديد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
            finally
            {
                // إعادة تمكين الزر
                AddProductButton.IsEnabled = true;
            }
        }

        private void EditProductButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedProduct == null)
                {
                    MessageBox.Show("الرجاء تحديد منتج للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تعطيل الزر لمنع النقر المتكرر
                EditProductButton.IsEnabled = false;

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // فتح نافذة تعديل المنتج
                ProductEditorWindow editorWindow = new ProductEditorWindow(_selectedProduct);
                editorWindow.Owner = Window.GetWindow(this);

                // إعادة المؤشر إلى الوضع الطبيعي قبل عرض النافذة
                Cursor = System.Windows.Input.Cursors.Arrow;

                bool? result = editorWindow.ShowDialog();

                // إعادة تحميل المنتجات بعد التعديل
                if (result == true)
                {
                    // إظهار مؤشر الانتظار مرة أخرى
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // إعادة تحميل المنتجات
                    LoadProducts(_selectedCategory?.Id ?? 0);

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
            finally
            {
                // إعادة تمكين الزر
                EditProductButton.IsEnabled = true;
            }
        }

        private async void DeleteProductButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedProduct == null)
                {
                    MessageBox.Show("الرجاء تحديد منتج للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تعطيل الزر لمنع النقر المتكرر
                DeleteProductButton.IsEnabled = false;

                MessageBoxResult result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المنتج '{_selectedProduct.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إظهار مؤشر الانتظار
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // حذف المنتج من قاعدة البيانات
                    await _productRepository.DeleteAsync(_selectedProduct.Id);

                    // حذف المنتج من القائمة
                    _products.Remove(_selectedProduct);

                    // تحديث العرض
                    ProductsDataGrid.ItemsSource = null;
                    ProductsDataGrid.ItemsSource = _products;

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;

                    MessageBox.Show("تم حذف المنتج بنجاح", "تم الحذف", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إعادة تعيين المنتج المحدد
                    _selectedProduct = null;
                    UpdateButtonsState();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
            finally
            {
                // إعادة تمكين الزر
                DeleteProductButton.IsEnabled = true;
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchProducts();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchProducts();
            }
        }

        private async void SearchProducts()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                string searchText = SearchTextBox.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    await LoadProductsAsync(_selectedCategory?.Id ?? 0);
                    return;
                }

                // البحث في قاعدة البيانات
                var searchResults = await _productRepository.SearchAsync(searchText);

                // تصفية النتائج حسب الفئة المحددة إذا لم تكن "جميع الفئات"
                if (_selectedCategory != null && _selectedCategory.Id != 0)
                {
                    searchResults = searchResults.Where(p => p.CategoryId == _selectedCategory.Id);
                }

                // تحويل النتائج إلى نماذج عرض
                _products = new ObservableCollection<ProductItemViewModel>();
                foreach (var product in searchResults)
                {
                    _products.Add(new ProductItemViewModel
                    {
                        Id = product.Id,
                        Name = product.Name,
                        Code = product.Barcode ?? string.Empty,
                        Barcode = product.Barcode ?? string.Empty,
                        Price = product.Price,
                        CostPrice = product.CostPrice,
                        Quantity = (int)product.Quantity,
                        MinimumQuantity = (int)product.MinQuantity,
                        CategoryId = product.CategoryId ?? 0,
                        CategoryName = product.CategoryName ?? string.Empty,
                        Description = product.Description ?? string.Empty,
                        IsAvailable = product.IsActive
                    });
                }

                ProductsDataGrid.ItemsSource = _products;

                // إظهار رسالة إذا لم يتم العثور على نتائج
                if (_products.Count == 0)
                {
                    MessageBox.Show("لم يتم العثور على منتجات مطابقة لمعايير البحث", "نتائج البحث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث عن المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }
    }

    // فئات عرض البيانات
    public class ProductItemViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty; // كود المنتج
        public decimal CostPrice { get; set; } // سعر التكلفة
        public decimal Price { get; set; } // سعر البيع
        public decimal? DiscountPrice { get; set; } // سعر الخصم
        public int Quantity { get; set; }
        public string Barcode { get; set; } = string.Empty;
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int MinimumQuantity { get; set; } = 0;
        public bool IsAvailable { get; set; } = true;
    }
}
