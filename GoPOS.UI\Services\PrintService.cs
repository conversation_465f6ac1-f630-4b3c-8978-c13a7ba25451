using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Xps.Packaging;
using GoPOS.UI.Views;

namespace GoPOS.UI.Services
{
    /// <summary>
    /// خدمة الطباعة
    /// </summary>
    public class PrintService
    {
        /// <summary>
        /// طباعة فاتورة
        /// </summary>
        /// <param name="invoice">الفاتورة المراد طباعتها</param>
        public static void PrintInvoice(InvoiceViewModel invoice)
        {
            try
            {
                // إنشاء مستند الطباعة
                FlowDocument document = CreateInvoiceDocument(invoice);

                // طباعة المستند
                PrintDocument(document, $"فاتورة رقم {invoice.InvoiceNumber}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء مستند الفاتورة
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <returns>مستند الفاتورة</returns>
        private static FlowDocument CreateInvoiceDocument(InvoiceViewModel invoice)
        {
            // إنشاء مستند جديد
            FlowDocument document = new FlowDocument();
            document.FontFamily = new FontFamily("Arial");
            document.FontSize = 12;
            document.PagePadding = new Thickness(50);
            document.TextAlignment = TextAlignment.Right;
            document.FlowDirection = FlowDirection.RightToLeft;

            // إضافة رأس الفاتورة
            Section headerSection = new Section();
            headerSection.Blocks.Add(CreateParagraph("نظام نقاط البيع GoPOS", 20, FontWeights.Bold, Colors.DarkBlue));
            headerSection.Blocks.Add(CreateParagraph("فاتورة مبيعات", 16, FontWeights.Bold, Colors.Black));
            headerSection.Blocks.Add(CreateParagraph($"رقم الفاتورة: {invoice.InvoiceNumber}", 14));
            headerSection.Blocks.Add(CreateParagraph($"التاريخ: {invoice.Date:yyyy/MM/dd}", 14));
            headerSection.Blocks.Add(CreateParagraph($"العميل: {invoice.CustomerName}", 14));
            headerSection.Blocks.Add(CreateParagraph($"الحالة: {invoice.StatusText}", 14));
            headerSection.Blocks.Add(CreateParagraph($"طريقة الدفع: {invoice.PaymentMethodText}", 14));
            document.Blocks.Add(headerSection);

            // إضافة خط فاصل
            document.Blocks.Add(CreateSeparator());

            // إضافة عناصر الفاتورة
            Section itemsSection = new Section();
            itemsSection.Blocks.Add(CreateParagraph("عناصر الفاتورة", 16, FontWeights.Bold, Colors.DarkBlue));

            // إنشاء جدول العناصر
            Table itemsTable = new Table();
            itemsTable.CellSpacing = 0;
            itemsTable.BorderBrush = Brushes.Black;
            itemsTable.BorderThickness = new Thickness(1);

            // تعريف أعمدة الجدول
            for (int i = 0; i < 6; i++)
            {
                itemsTable.Columns.Add(new TableColumn());
            }

            // تعيين عرض الأعمدة
            itemsTable.Columns[0].Width = new GridLength(50); // #
            itemsTable.Columns[1].Width = new GridLength(200); // المنتج
            itemsTable.Columns[2].Width = new GridLength(80); // سعر الوحدة
            itemsTable.Columns[3].Width = new GridLength(60); // الكمية
            itemsTable.Columns[4].Width = new GridLength(80); // الضريبة
            itemsTable.Columns[5].Width = new GridLength(100); // المجموع

            // إضافة رأس الجدول
            TableRowGroup headerRowGroup = new TableRowGroup();
            TableRow headerRow = new TableRow();
            headerRow.Background = new SolidColorBrush(Colors.LightGray);
            headerRow.FontWeight = FontWeights.Bold;

            headerRow.Cells.Add(CreateTableCell("#"));
            headerRow.Cells.Add(CreateTableCell("المنتج"));
            headerRow.Cells.Add(CreateTableCell("سعر الوحدة"));
            headerRow.Cells.Add(CreateTableCell("الكمية"));
            headerRow.Cells.Add(CreateTableCell("الضريبة"));
            headerRow.Cells.Add(CreateTableCell("المجموع"));

            headerRowGroup.Rows.Add(headerRow);
            itemsTable.RowGroups.Add(headerRowGroup);

            // إضافة صفوف العناصر
            TableRowGroup itemsRowGroup = new TableRowGroup();

            // محاكاة عناصر الفاتورة
            List<InvoiceItemViewModel> items = GetSampleInvoiceItems(invoice);

            foreach (var item in items)
            {
                TableRow itemRow = new TableRow();
                itemRow.Cells.Add(CreateTableCell(item.Index.ToString()));
                itemRow.Cells.Add(CreateTableCell(item.ProductName));
                itemRow.Cells.Add(CreateTableCell(item.UnitPrice.ToString("N2")));
                itemRow.Cells.Add(CreateTableCell(item.Quantity.ToString()));
                itemRow.Cells.Add(CreateTableCell(item.TaxAmount.ToString("N2")));
                itemRow.Cells.Add(CreateTableCell(item.Total.ToString("N2")));
                itemsRowGroup.Rows.Add(itemRow);
            }

            itemsTable.RowGroups.Add(itemsRowGroup);
            itemsSection.Blocks.Add(itemsTable);
            document.Blocks.Add(itemsSection);

            // إضافة خط فاصل
            document.Blocks.Add(CreateSeparator());

            // إضافة ملخص الفاتورة
            Section summarySection = new Section();
            summarySection.Blocks.Add(CreateParagraph("ملخص الفاتورة", 16, FontWeights.Bold, Colors.DarkBlue));
            summarySection.Blocks.Add(CreateParagraph($"المجموع الفرعي: {invoice.Subtotal:N2}", 14, FontWeights.Normal, Colors.Black, TextAlignment.Left));

            if (invoice.DiscountAmount > 0)
            {
                string discountText = $"الخصم: {invoice.DiscountAmount:N2}";
                if (invoice.IsDiscountPercentage && invoice.DiscountPercentage > 0)
                {
                    discountText += $" ({invoice.DiscountPercentage}%)";
                }
                summarySection.Blocks.Add(CreateParagraph(discountText, 14, FontWeights.Normal, Colors.Black, TextAlignment.Left));
            }

            summarySection.Blocks.Add(CreateParagraph($"الضريبة: {invoice.TaxAmount:N2}", 14, FontWeights.Normal, Colors.Black, TextAlignment.Left));
            summarySection.Blocks.Add(CreateParagraph($"المجموع النهائي: {invoice.Total:N2}", 16, FontWeights.Bold, Colors.DarkBlue, TextAlignment.Left));

            if (!invoice.IsPaid)
            {
                summarySection.Blocks.Add(CreateParagraph($"المبلغ المدفوع: {invoice.PaidAmount:N2}", 14, FontWeights.Normal, Colors.Black, TextAlignment.Left));
                summarySection.Blocks.Add(CreateParagraph($"المبلغ المتبقي: {invoice.RemainingAmount:N2}", 14, FontWeights.Bold, Colors.Red, TextAlignment.Left));
            }

            document.Blocks.Add(summarySection);

            // إضافة خط فاصل
            document.Blocks.Add(CreateSeparator());

            // إضافة تذييل الفاتورة
            Section footerSection = new Section();
            footerSection.Blocks.Add(CreateParagraph("شكراً لتعاملكم معنا", 14, FontWeights.Bold, Colors.DarkBlue, TextAlignment.Center));
            footerSection.Blocks.Add(CreateParagraph("نظام نقاط البيع GoPOS", 12, FontWeights.Normal, Colors.Gray, TextAlignment.Center));
            document.Blocks.Add(footerSection);

            return document;
        }

        /// <summary>
        /// الحصول على عناصر الفاتورة (محاكاة)
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <returns>قائمة عناصر الفاتورة</returns>
        private static List<InvoiceItemViewModel> GetSampleInvoiceItems(InvoiceViewModel invoice)
        {
            // محاكاة عناصر الفاتورة
            List<InvoiceItemViewModel> items = new List<InvoiceItemViewModel>();

            // إضافة عناصر محاكاة بناءً على إجمالي الفاتورة
            if (invoice.Total > 0)
            {
                // العنصر الأول
                items.Add(new InvoiceItemViewModel
                {
                    Index = 1,
                    ProductId = 1,
                    ProductName = "منتج 1",
                    Barcode = "1234567890",
                    UnitPrice = 50.00m,
                    Quantity = 2,
                    DiscountAmount = 0,
                    TaxAmount = 15.00m,
                    Total = 115.00m
                });

                // العنصر الثاني (إذا كان المجموع أكبر من 150)
                if (invoice.Total > 150)
                {
                    items.Add(new InvoiceItemViewModel
                    {
                        Index = 2,
                        ProductId = 2,
                        ProductName = "منتج 2",
                        Barcode = "0987654321",
                        UnitPrice = 25.00m,
                        Quantity = 1,
                        DiscountAmount = 0,
                        TaxAmount = 3.75m,
                        Total = 28.75m
                    });
                }

                // العنصر الثالث (إذا كان المجموع أكبر من 300)
                if (invoice.Total > 300)
                {
                    items.Add(new InvoiceItemViewModel
                    {
                        Index = 3,
                        ProductId = 3,
                        ProductName = "منتج 3",
                        Barcode = "5678901234",
                        UnitPrice = 100.00m,
                        Quantity = 1,
                        DiscountAmount = 10.00m,
                        TaxAmount = 13.50m,
                        Total = 103.50m
                    });
                }
            }

            return items;
        }

        /// <summary>
        /// إنشاء فقرة نصية
        /// </summary>
        private static Paragraph CreateParagraph(string text, double fontSize = 12, FontWeight fontWeight = default, Color? color = null, TextAlignment? alignment = null)
        {
            Paragraph paragraph = new Paragraph();
            paragraph.FontSize = fontSize;
            paragraph.FontWeight = fontWeight == default ? FontWeights.Normal : fontWeight;

            if (color.HasValue)
            {
                paragraph.Foreground = new SolidColorBrush(color.Value);
            }

            if (alignment.HasValue)
            {
                paragraph.TextAlignment = alignment.Value;
            }

            paragraph.Inlines.Add(new Run(text));
            return paragraph;
        }

        /// <summary>
        /// إنشاء خلية جدول
        /// </summary>
        private static TableCell CreateTableCell(string text)
        {
            TableCell cell = new TableCell();
            cell.BorderBrush = Brushes.Black;
            cell.BorderThickness = new Thickness(1);
            cell.Padding = new Thickness(5);

            Paragraph paragraph = new Paragraph();
            paragraph.Inlines.Add(new Run(text));
            cell.Blocks.Add(paragraph);

            return cell;
        }

        /// <summary>
        /// إنشاء خط فاصل
        /// </summary>
        private static Paragraph CreateSeparator()
        {
            Paragraph separator = new Paragraph();
            separator.Margin = new Thickness(0, 10, 0, 10);

            Line line = new Line();
            line.X1 = 0;
            line.X2 = 1000;
            line.Y1 = 0;
            line.Y2 = 0;
            line.Stroke = Brushes.Gray;
            line.StrokeThickness = 1;

            separator.Inlines.Add(new InlineUIContainer(line));
            return separator;
        }

        /// <summary>
        /// طباعة مستند
        /// </summary>
        private static void PrintDocument(FlowDocument document, string documentName)
        {
            try
            {
                // إنشاء مربع حوار الطباعة
                PrintDialog printDialog = new PrintDialog();

                if (printDialog.ShowDialog() == true)
                {
                    // تعيين حجم الصفحة
                    document.PageHeight = printDialog.PrintableAreaHeight;
                    document.PageWidth = printDialog.PrintableAreaWidth;
                    document.ColumnWidth = printDialog.PrintableAreaWidth;

                    // إنشاء مستند XPS مؤقت
                    string tempFileName = System.IO.Path.GetTempFileName();
                    XpsDocument xpsDocument = new XpsDocument(tempFileName, FileAccess.ReadWrite);

                    // إنشاء كاتب XPS
                    var xpsWriter = System.Windows.Xps.Packaging.XpsDocument.CreateXpsDocumentWriter(xpsDocument);

                    // طباعة المستند
                    xpsWriter.Write(((IDocumentPaginatorSource)document).DocumentPaginator);

                    // إغلاق مستند XPS
                    xpsDocument.Close();

                    // حذف الملف المؤقت
                    try
                    {
                        System.IO.File.Delete(tempFileName);
                    }
                    catch
                    {
                        // تجاهل أي أخطاء أثناء حذف الملف المؤقت
                    }

                    // عرض رسالة نجاح
                    MessageBox.Show("تمت الطباعة بنجاح", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
