using System;
using System.IO;

namespace GoPOS.UI.Models
{
    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        /// <summary>
        /// اسم الملف
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// المسار الكامل للملف
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// تاريخ إنشاء النسخة الاحتياطية
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// حجم الملف بالبايت
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// نوع النسخة الاحتياطية
        /// </summary>
        public string BackupType { get; set; }

        /// <summary>
        /// حجم الملف بتنسيق مقروء
        /// </summary>
        public string FormattedSize
        {
            get
            {
                const int KB = 1024;
                const int MB = KB * 1024;
                const int GB = MB * 1024;

                if (FileSize >= GB)
                    return $"{FileSize / (double)GB:0.##} GB";
                if (FileSize >= MB)
                    return $"{FileSize / (double)MB:0.##} MB";
                if (FileSize >= KB)
                    return $"{FileSize / (double)KB:0.##} KB";

                return $"{FileSize} bytes";
            }
        }

        /// <summary>
        /// إنشاء كائن معلومات النسخة الاحتياطية من ملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>كائن معلومات النسخة الاحتياطية</returns>
        public static BackupInfo FromFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return null;

            FileInfo fileInfo = new FileInfo(filePath);
            BackupInfo backupInfo = new BackupInfo
            {
                FileName = fileInfo.Name,
                FilePath = fileInfo.FullName,
                CreationDate = fileInfo.CreationTime,
                FileSize = fileInfo.Length,
                BackupType = DetermineBackupType(fileInfo)
            };

            return backupInfo;
        }

        /// <summary>
        /// تحديد نوع النسخة الاحتياطية
        /// </summary>
        /// <param name="fileInfo">معلومات الملف</param>
        /// <returns>نوع النسخة الاحتياطية</returns>
        private static string DetermineBackupType(FileInfo fileInfo)
        {
            string fileName = fileInfo.Name.ToLower();
            
            // التحقق من وجود ملف معلومات النسخة الاحتياطية
            string infoFilePath = Path.ChangeExtension(fileInfo.FullName, ".info");
            if (File.Exists(infoFilePath))
            {
                try
                {
                    string infoContent = File.ReadAllText(infoFilePath);
                    string backupType = ExtractValueFromContent(infoContent, "BackupType:");
                    if (!string.IsNullOrEmpty(backupType))
                    {
                        switch (backupType.Trim())
                        {
                            case "Emergency":
                                return "نسخة طوارئ";
                            case "FullSystem":
                                return "نسخة كاملة للنظام";
                            case "Auto":
                                return "نسخة تلقائية";
                        }
                    }
                }
                catch
                {
                    // تجاهل أي خطأ أثناء قراءة ملف المعلومات
                }
            }
            
            // تحديد النوع من اسم الملف إذا لم يتم العثور على ملف معلومات
            if (fileName.Contains("emergency"))
            {
                return "نسخة طوارئ";
            }
            else if (fileName.Contains("full") || fileName.Contains("system"))
            {
                return "نسخة كاملة للنظام";
            }
            else if (fileName.Contains("auto"))
            {
                return "نسخة تلقائية";
            }
            
            return "نسخة عادية";
        }

        /// <summary>
        /// استخراج قيمة من محتوى ملف المعلومات
        /// </summary>
        /// <param name="content">محتوى الملف</param>
        /// <param name="key">المفتاح</param>
        /// <returns>القيمة</returns>
        private static string ExtractValueFromContent(string content, string key)
        {
            try
            {
                int keyIndex = content.IndexOf(key);
                if (keyIndex >= 0)
                {
                    int startIndex = keyIndex + key.Length;
                    int endIndex = content.IndexOf('\n', startIndex);
                    if (endIndex < 0)
                    {
                        endIndex = content.Length;
                    }
                    
                    return content.Substring(startIndex, endIndex - startIndex).Trim();
                }
            }
            catch
            {
                // تجاهل أي خطأ أثناء استخراج القيمة
            }
            
            return string.Empty;
        }
    }
}
