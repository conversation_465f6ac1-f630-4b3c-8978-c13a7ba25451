using System;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for ReportsView.xaml
    /// </summary>
    public partial class ReportsView : UserControl
    {
        private string _currentReportType = "SalesReport";

        // جعل ReportsListBox متاحًا للوصول من الخارج
        public ListBox ReportsListBox { get; private set; }

        public ReportsView()
        {
            InitializeComponent();

            // الحصول على مرجع إلى ReportsListBox
            ReportsListBox = this.FindName("ReportsListBox") as ListBox;

            // تعيين التاريخ الافتراضي
            FromDatePicker.SelectedDate = DateTime.Today;
            ToDatePicker.SelectedDate = DateTime.Today;

            // تحديد التقرير الافتراضي
            ReportsListBox.SelectedIndex = 0;

            // تحديث حالة حقول التاريخ
            UpdateDateFieldsState();

            // تحميل التقرير الافتراضي
            LoadReport();
        }

        private void ReportsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ReportsListBox.SelectedItem != null)
            {
                ListBoxItem selectedItem = (ListBoxItem)ReportsListBox.SelectedItem;
                _currentReportType = selectedItem.Tag.ToString();

                // تحديث عنوان التقرير
                UpdateReportTitle();

                // تحميل التقرير
                LoadReport();
            }
        }

        private void PeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateDateFieldsState();
            UpdateDateRange();
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            // التأكد من أن تاريخ "إلى" لا يسبق تاريخ "من"
            if (FromDatePicker.SelectedDate.HasValue && ToDatePicker.SelectedDate.HasValue)
            {
                if (ToDatePicker.SelectedDate.Value < FromDatePicker.SelectedDate.Value)
                {
                    ToDatePicker.SelectedDate = FromDatePicker.SelectedDate;
                }
            }
        }

        private void ApplyFilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // تحميل التقرير بناءً على المعايير المحددة
                LoadReport();

                MessageBox.Show("تم تطبيق الفلتر بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تطبيق الفلتر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show($"سيتم طباعة {ReportTitleTextBlock.Text}", "طباعة التقرير", MessageBoxButton.OK, MessageBoxImage.Information);

                // هنا يمكن إضافة كود لطباعة التقرير
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مربع حوار حفظ الملف
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "ملف Excel (*.xlsx)|*.xlsx|ملف PDF (*.pdf)|*.pdf|ملف CSV (*.csv)|*.csv",
                    Title = "تصدير التقرير",
                    FileName = $"{ReportTitleTextBlock.Text}_{DateTime.Now:yyyy-MM-dd}"
                };

                // عرض مربع الحوار وانتظار اختيار المستخدم
                if (saveFileDialog.ShowDialog() == true)
                {
                    string filePath = saveFileDialog.FileName;
                    string fileExtension = System.IO.Path.GetExtension(filePath).ToLower();

                    // تصدير التقرير بناءً على نوع الملف المختار
                    switch (fileExtension)
                    {
                        case ".xlsx":
                            ExportToExcel(filePath);
                            break;
                        case ".pdf":
                            ExportToPdf(filePath);
                            break;
                        case ".csv":
                            ExportToCsv(filePath);
                            break;
                    }

                    MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}", "تم التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateDateFieldsState()
        {
            // تحديث حالة حقول التاريخ بناءً على الفترة المحددة
            bool isCustomPeriod = PeriodComboBox.SelectedIndex == 4; // "فترة محددة"

            FromDatePicker.IsEnabled = isCustomPeriod;
            ToDatePicker.IsEnabled = isCustomPeriod;
        }

        private void UpdateDateRange()
        {
            // تحديث نطاق التاريخ بناءً على الفترة المحددة
            DateTime today = DateTime.Today;

            switch (PeriodComboBox.SelectedIndex)
            {
                case 0: // اليوم
                    FromDatePicker.SelectedDate = today;
                    ToDatePicker.SelectedDate = today;
                    break;
                case 1: // الأسبوع
                    FromDatePicker.SelectedDate = today.AddDays(-(int)today.DayOfWeek);
                    ToDatePicker.SelectedDate = today;
                    break;
                case 2: // الشهر
                    FromDatePicker.SelectedDate = new DateTime(today.Year, today.Month, 1);
                    ToDatePicker.SelectedDate = today;
                    break;
                case 3: // السنة
                    FromDatePicker.SelectedDate = new DateTime(today.Year, 1, 1);
                    ToDatePicker.SelectedDate = today;
                    break;
                // حالة "فترة محددة" لا تحتاج إلى تحديث تلقائي
            }
        }

        private void UpdateReportTitle()
        {
            // تحديث عنوان التقرير بناءً على نوع التقرير المحدد
            switch (_currentReportType)
            {
                case "SalesReport":
                    ReportTitleTextBlock.Text = "تقرير المبيعات";
                    break;
                case "InventoryReport":
                    ReportTitleTextBlock.Text = "تقرير المخزون";
                    break;
                case "CustomersReport":
                    ReportTitleTextBlock.Text = "تقرير العملاء";
                    break;
                case "InstallmentsReport":
                    ReportTitleTextBlock.Text = "تقرير الأقساط";
                    break;
                case "ExpensesReport":
                    ReportTitleTextBlock.Text = "تقرير المنصرفات";
                    break;
                case "ProfitReport":
                    ReportTitleTextBlock.Text = "تقرير الأرباح";
                    break;
                case "TaxReport":
                    ReportTitleTextBlock.Text = "تقرير الضرائب";
                    break;
            }
        }

        private void LoadReport()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // الحصول على نطاق التاريخ
                DateTime fromDate = FromDatePicker.SelectedDate ?? DateTime.Today;
                DateTime toDate = ToDatePicker.SelectedDate ?? DateTime.Today;

                // تحميل التقرير المناسب بناءً على النوع المحدد
                switch (_currentReportType)
                {
                    case "SalesReport":
                        LoadSalesReport(fromDate, toDate);
                        break;
                    case "InventoryReport":
                        LoadInventoryReport(fromDate, toDate);
                        break;
                    case "CustomersReport":
                        LoadCustomersReport(fromDate, toDate);
                        break;
                    case "InstallmentsReport":
                        LoadInstallmentsReport(fromDate, toDate);
                        break;
                    case "ExpensesReport":
                        LoadExpensesReport(fromDate, toDate);
                        break;
                    case "ProfitReport":
                        LoadProfitReport(fromDate, toDate);
                        break;
                    case "TaxReport":
                        LoadTaxReport(fromDate, toDate);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void LoadSalesReport(DateTime fromDate, DateTime toDate)
        {
            // إنشاء تقرير المبيعات
            SalesReportControl salesReport = new SalesReportControl(fromDate, toDate);
            ReportContentControl.Content = salesReport;
        }

        private void LoadInventoryReport(DateTime fromDate, DateTime toDate)
        {
            // محاكاة تقرير المخزون (سيتم استبداله بتقرير حقيقي)
            TextBlock placeholder = new TextBlock
            {
                Text = $"تقرير المخزون من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            ReportContentControl.Content = placeholder;
        }

        private void LoadCustomersReport(DateTime fromDate, DateTime toDate)
        {
            // محاكاة تقرير العملاء (سيتم استبداله بتقرير حقيقي)
            TextBlock placeholder = new TextBlock
            {
                Text = $"تقرير العملاء من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            ReportContentControl.Content = placeholder;
        }

        private void LoadInstallmentsReport(DateTime fromDate, DateTime toDate)
        {
            // محاكاة تقرير الأقساط (سيتم استبداله بتقرير حقيقي)
            TextBlock placeholder = new TextBlock
            {
                Text = $"تقرير الأقساط من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            ReportContentControl.Content = placeholder;
        }

        private void LoadExpensesReport(DateTime fromDate, DateTime toDate)
        {
            // محاكاة تقرير المنصرفات (سيتم استبداله بتقرير حقيقي)
            TextBlock placeholder = new TextBlock
            {
                Text = $"تقرير المنصرفات من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            ReportContentControl.Content = placeholder;
        }

        private void LoadProfitReport(DateTime fromDate, DateTime toDate)
        {
            // محاكاة تقرير الأرباح (سيتم استبداله بتقرير حقيقي)
            TextBlock placeholder = new TextBlock
            {
                Text = $"تقرير الأرباح من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            ReportContentControl.Content = placeholder;
        }

        private void LoadTaxReport(DateTime fromDate, DateTime toDate)
        {
            // محاكاة تقرير الضرائب (سيتم استبداله بتقرير حقيقي)
            TextBlock placeholder = new TextBlock
            {
                Text = $"تقرير الضرائب من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            ReportContentControl.Content = placeholder;
        }

        private void ExportToExcel(string filePath)
        {
            // محاكاة تصدير إلى Excel (سيتم استبداله بتصدير حقيقي)
            System.IO.File.WriteAllText(filePath, "محتوى تجريبي لملف Excel");
        }

        private void ExportToPdf(string filePath)
        {
            // محاكاة تصدير إلى PDF (سيتم استبداله بتصدير حقيقي)
            System.IO.File.WriteAllText(filePath, "محتوى تجريبي لملف PDF");
        }

        private void ExportToCsv(string filePath)
        {
            // محاكاة تصدير إلى CSV (سيتم استبداله بتصدير حقيقي)
            System.IO.File.WriteAllText(filePath, "محتوى تجريبي لملف CSV");
        }
    }
}
