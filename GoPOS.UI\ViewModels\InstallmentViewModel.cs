using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GoPOS.UI.ViewModels
{
    /// <summary>
    /// نموذج عرض القسط
    /// </summary>
    public class InstallmentViewModel : INotifyPropertyChanged
    {
        private int _id;
        private int _planId;
        private DateTime _dueDate;
        private decimal _amount;
        private int _status;
        private string _statusText = string.Empty;
        private DateTime? _paymentDate;
        private string _notes = string.Empty;
        private int? _customerId;
        private string _customerName = string.Empty;
        private int _installmentNumber;

        /// <summary>
        /// معرف القسط
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// معرف خطة الأقساط
        /// </summary>
        public int PlanId
        {
            get => _planId;
            set
            {
                if (_planId != value)
                {
                    _planId = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// تاريخ الاستحقاق
        /// </summary>
        public DateTime DueDate
        {
            get => _dueDate;
            set
            {
                if (_dueDate != value)
                {
                    _dueDate = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount
        {
            get => _amount;
            set
            {
                if (_amount != value)
                {
                    _amount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حالة القسط
        /// </summary>
        public int Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// نص حالة القسط
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set
            {
                if (_statusText != value)
                {
                    _statusText = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// تاريخ الدفع
        /// </summary>
        public DateTime? PaymentDate
        {
            get => _paymentDate;
            set
            {
                if (_paymentDate != value)
                {
                    _paymentDate = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int? CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName
        {
            get => _customerName;
            set
            {
                if (_customerName != value)
                {
                    _customerName = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// رقم القسط
        /// </summary>
        public int InstallmentNumber
        {
            get => _installmentNumber;
            set
            {
                if (_installmentNumber != value)
                {
                    _installmentNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حدث تغيير الخاصية
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// إشعار بتغيير الخاصية
        /// </summary>
        /// <param name="propertyName">اسم الخاصية</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
