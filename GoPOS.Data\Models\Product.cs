namespace GoPOS.Data.Models
{
    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product
    {
        /// <summary>
        /// معرف المنتج
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// اسم المنتج
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// وصف المنتج
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// باركود المنتج
        /// </summary>
        public string Barcode { get; set; }
        
        /// <summary>
        /// معرف الفئة
        /// </summary>
        public int? CategoryId { get; set; }
        
        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string CategoryName { get; set; }
        
        /// <summary>
        /// سعر البيع
        /// </summary>
        public decimal Price { get; set; }
        
        /// <summary>
        /// سعر التكلفة
        /// </summary>
        public decimal CostPrice { get; set; }
        
        /// <summary>
        /// الكمية المتوفرة
        /// </summary>
        public decimal Quantity { get; set; }
        
        /// <summary>
        /// الحد الأدنى للكمية
        /// </summary>
        public decimal MinQuantity { get; set; }
        
        /// <summary>
        /// هل المنتج نشط
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// مسار الصورة
        /// </summary>
        public string ImagePath { get; set; }
    }
}
