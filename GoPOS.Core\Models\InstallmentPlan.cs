using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج خطة الأقساط
    /// </summary>
    public class InstallmentPlan
    {
        public int Id { get; set; }

        [Required]
        public int OrderId { get; set; }
        public Order Order { get; set; }

        [Required]
        public int CustomerId { get; set; }
        public Customer Customer { get; set; }

        [Required]
        public decimal TotalAmount { get; set; }

        [Required]
        public decimal DownPayment { get; set; }

        [Required]
        public decimal RemainingAmount { get; set; }

        [Required]
        public int NumberOfInstallments { get; set; }

        [Required]
        public InstallmentFrequency Frequency { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public InstallmentPlanStatus Status { get; set; }

        [MaxLength(500)]
        public string Terms { get; set; }

        [MaxLength(200)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public ICollection<Installment> Installments { get; set; }
    }

    public enum InstallmentFrequency
    {
        Weekly = 0,
        BiWeekly = 1,
        Monthly = 2,
        Quarterly = 3
    }

    public enum InstallmentPlanStatus
    {
        Active = 0,
        Completed = 1,
        Defaulted = 2,
        Cancelled = 3
    }
}
