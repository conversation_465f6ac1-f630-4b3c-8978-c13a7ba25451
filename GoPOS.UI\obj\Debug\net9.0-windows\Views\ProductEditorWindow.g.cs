﻿#pragma checksum "..\..\..\..\Views\ProductEditorWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0BA51E56AE3F8250DA2D7D6667F3CE3A54C83172"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// ProductEditorWindow
    /// </summary>
    public partial class ProductEditorWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BarcodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateBarcodeButton;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CostPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinimumQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsAvailableCheckBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CalculatePriceButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image ProductImage;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectImageButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfitMarginTextBlock;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\ProductEditorWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/producteditorwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProductEditorWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.BarcodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.GenerateBarcodeButton = ((System.Windows.Controls.Button)(target));
            
            #line 44 "..\..\..\..\Views\ProductEditorWindow.xaml"
            this.GenerateBarcodeButton.Click += new System.Windows.RoutedEventHandler(this.GenerateBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CostPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.PriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.DiscountPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.MinimumQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.IsAvailableCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.CalculatePriceButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\Views\ProductEditorWindow.xaml"
            this.CalculatePriceButton.Click += new System.Windows.RoutedEventHandler(this.CalculatePriceButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ProductImage = ((System.Windows.Controls.Image)(target));
            return;
            case 16:
            this.SelectImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\Views\ProductEditorWindow.xaml"
            this.SelectImageButton.Click += new System.Windows.RoutedEventHandler(this.SelectImageButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ProfitMarginTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\Views\ProductEditorWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\..\Views\ProductEditorWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

