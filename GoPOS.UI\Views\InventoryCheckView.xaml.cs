using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InventoryCheckView.xaml
    /// </summary>
    public partial class InventoryCheckView : UserControl
    {
        private ObservableCollection<InventoryCheckViewModel> _inventoryChecks;
        private ObservableCollection<ExpiryProductViewModel> _expiryProducts;
        private InventoryCheckViewModel _selectedCheck;

        public InventoryCheckView()
        {
            InitializeComponent();
            LoadInventoryChecks();
            LoadExpiryProducts();
        }

        private void LoadInventoryChecks(int warehouseId = 0, int statusId = 0)
        {
            // تحميل عمليات الجرد (سيتم استبدالها بقراءة من قاعدة البيانات)
            _inventoryChecks = new ObservableCollection<InventoryCheckViewModel>();
            Random random = new Random();

            // بيانات تجريبية
            for (int i = 1; i <= 10; i++)
            {
                int statusIndex = random.Next(0, 5);
                string[] statusTexts = { "مسودة", "قيد التنفيذ", "مكتمل", "معتمد", "ملغي" };
                string statusText = statusTexts[statusIndex];

                // تخطي الحالات التي لا تتطابق مع معايير التصفية
                if (statusId > 0 && statusIndex != statusId - 1)
                    continue;

                SolidColorBrush statusColor;
                switch (statusIndex)
                {
                    case 0: // مسودة
                        statusColor = new SolidColorBrush(Colors.Gray);
                        break;
                    case 1: // قيد التنفيذ
                        statusColor = new SolidColorBrush(Colors.Blue);
                        break;
                    case 2: // مكتمل
                        statusColor = new SolidColorBrush(Colors.Green);
                        break;
                    case 3: // معتمد
                        statusColor = new SolidColorBrush(Colors.Purple);
                        break;
                    case 4: // ملغي
                        statusColor = new SolidColorBrush(Colors.Red);
                        break;
                    default:
                        statusColor = new SolidColorBrush(Colors.Black);
                        break;
                }

                int warehouseIndex = random.Next(0, 3);
                string warehouseName = warehouseIndex == 0 ? "المخزن الرئيسي" : (warehouseIndex == 1 ? "مخزن الفرع 1" : "مخزن الفرع 2");
                int warehouseIdValue = warehouseIndex + 1;

                // تخطي المخازن التي لا تتطابق مع معايير التصفية
                if (warehouseId > 0 && warehouseIdValue != warehouseId)
                    continue;

                int itemCount = random.Next(10, 100);
                int checkedItemCount = statusIndex >= 2 ? itemCount : random.Next(0, itemCount);
                int differenceCount = statusIndex >= 2 ? random.Next(0, 10) : 0;

                // تحديد ما إذا كان يمكن تنفيذ الجرد أو اعتماده
                Visibility canPerform = statusIndex == 0 ? Visibility.Visible : Visibility.Collapsed;
                Visibility canApprove = statusIndex == 2 ? Visibility.Visible : Visibility.Collapsed;

                _inventoryChecks.Add(new InventoryCheckViewModel
                {
                    Id = i,
                    CheckNumber = $"INV-{DateTime.Now.Year}-{i:D4}",
                    CheckDate = DateTime.Now.AddDays(-random.Next(0, 30)),
                    WarehouseId = warehouseIdValue,
                    WarehouseName = warehouseName,
                    UserName = "مدير النظام",
                    ItemCount = itemCount,
                    CheckedItemCount = checkedItemCount,
                    DifferenceCount = differenceCount,
                    StatusId = statusIndex,
                    StatusText = statusText,
                    StatusColor = statusColor,
                    CanPerform = canPerform,
                    CanApprove = canApprove
                });
            }

            InventoryChecksDataGrid.ItemsSource = _inventoryChecks;
            UpdateButtonsState();
        }

        private void LoadExpiryProducts(int days = 7)
        {
            // تحميل المنتجات منتهية الصلاحية أو التي ستنتهي قريباً (سيتم استبدالها بقراءة من قاعدة البيانات)
            _expiryProducts = new ObservableCollection<ExpiryProductViewModel>();
            Random random = new Random();

            // بيانات تجريبية
            for (int i = 1; i <= 20; i++)
            {
                int daysToExpiry = random.Next(-30, 120); // من -30 (منتهي منذ 30 يوم) إلى 120 (سينتهي بعد 120 يوم)

                // تخطي المنتجات التي لا تتطابق مع معايير التصفية
                if (days < 1000) // 1000 يعني عرض الكل
                {
                    if (days == 0) // المنتهية فقط
                    {
                        if (daysToExpiry > 0)
                            continue;
                    }
                    else if (daysToExpiry > days || daysToExpiry < 0)
                    {
                        continue;
                    }
                }

                DateTime expiryDate = DateTime.Now.AddDays(daysToExpiry);

                string statusText;
                SolidColorBrush statusColor;

                if (daysToExpiry < 0)
                {
                    statusText = "منتهي";
                    statusColor = new SolidColorBrush(Colors.Red);
                }
                else if (daysToExpiry <= 7)
                {
                    statusText = "ينتهي قريباً";
                    statusColor = new SolidColorBrush(Colors.Orange);
                }
                else if (daysToExpiry <= 30)
                {
                    statusText = "ينتهي خلال شهر";
                    statusColor = new SolidColorBrush(Colors.Yellow);
                    statusColor.Opacity = 0.8;
                }
                else
                {
                    statusText = "سليم";
                    statusColor = new SolidColorBrush(Colors.Green);
                }

                int categoryIndex = random.Next(0, 3);
                string productName = GetRandomProductName(categoryIndex);

                int warehouseIndex = random.Next(0, 3);
                string warehouseName = warehouseIndex == 0 ? "المخزن الرئيسي" : (warehouseIndex == 1 ? "مخزن الفرع 1" : "مخزن الفرع 2");

                _expiryProducts.Add(new ExpiryProductViewModel
                {
                    Id = i,
                    Barcode = $"100{i:D4}",
                    Name = productName,
                    Quantity = random.Next(1, 50),
                    ExpiryDate = expiryDate,
                    WarehouseName = warehouseName,
                    StatusText = statusText,
                    StatusColor = statusColor,
                    DaysRemaining = daysToExpiry,
                    IsSelected = false
                });
            }

            // ترتيب المنتجات حسب تاريخ الانتهاء (الأقرب للانتهاء أولاً)
            var sortedProducts = new ObservableCollection<ExpiryProductViewModel>(
                _expiryProducts.OrderBy(p => p.ExpiryDate)
            );

            ExpiryProductsDataGrid.ItemsSource = sortedProducts;
        }

        private string GetRandomProductName(int categoryIndex)
        {
            string[] drinks = { "قهوة", "شاي", "عصير برتقال", "عصير تفاح", "ماء معدني", "مشروب غازي", "عصير مانجو" };
            string[] fastFood = { "برجر", "بيتزا", "ساندويتش", "دجاج مقلي", "بطاطس", "ناجتس", "شاورما" };
            string[] desserts = { "كيك", "آيس كريم", "كوكيز", "دونات", "تشيز كيك", "بودينج", "كنافة" };

            Random random = new Random();

            if (categoryIndex == 0)
                return drinks[random.Next(drinks.Length)];
            else if (categoryIndex == 1)
                return fastFood[random.Next(fastFood.Length)];
            else
                return desserts[random.Next(desserts.Length)];
        }

        private void UpdateButtonsState()
        {
            bool isCheckSelected = _selectedCheck != null;
            EditCheckButton.IsEnabled = isCheckSelected && (_selectedCheck.StatusId == 0); // يمكن تعديل المسودات فقط
            DeleteCheckButton.IsEnabled = isCheckSelected && (_selectedCheck.StatusId == 0 || _selectedCheck.StatusId == 1); // يمكن حذف المسودات وقيد التنفيذ فقط
            PrintCheckButton.IsEnabled = isCheckSelected;
        }

        private void WarehouseComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            int warehouseId = WarehouseComboBox.SelectedIndex;
            int statusId = StatusFilterComboBox.SelectedIndex;
            LoadInventoryChecks(warehouseId, statusId);
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            int warehouseId = WarehouseComboBox.SelectedIndex;
            int statusId = StatusFilterComboBox.SelectedIndex;
            LoadInventoryChecks(warehouseId, statusId);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            int warehouseId = WarehouseComboBox.SelectedIndex;
            int statusId = StatusFilterComboBox.SelectedIndex;
            LoadInventoryChecks(warehouseId, statusId);
            LoadExpiryProducts(GetSelectedExpiryDays());
        }

        private void InventoryChecksDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedCheck = InventoryChecksDataGrid.SelectedItem as InventoryCheckViewModel;
            UpdateButtonsState();
        }

        private void NewCheckButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إنشاء جرد جديد
            InventoryCheckDetailsWindow detailsWindow = new InventoryCheckDetailsWindow();
            detailsWindow.Owner = Window.GetWindow(this);

            if (detailsWindow.ShowDialog() == true)
            {
                // تحديث قائمة عمليات الجرد
                int warehouseId = WarehouseComboBox.SelectedIndex;
                int statusId = StatusFilterComboBox.SelectedIndex;
                LoadInventoryChecks(warehouseId, statusId);
            }
        }

        private void EditCheckButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCheck == null) return;

            // فتح نافذة تعديل الجرد
            InventoryCheckDetailsWindow detailsWindow = new InventoryCheckDetailsWindow(_selectedCheck);
            detailsWindow.Owner = Window.GetWindow(this);

            if (detailsWindow.ShowDialog() == true)
            {
                // تحديث قائمة عمليات الجرد
                int warehouseId = WarehouseComboBox.SelectedIndex;
                int statusId = StatusFilterComboBox.SelectedIndex;
                LoadInventoryChecks(warehouseId, statusId);
            }
        }

        private void DeleteCheckButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCheck == null) return;

            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من حذف عملية الجرد المحددة؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // حذف عملية الجرد (سيتم استبداله بالحذف من قاعدة البيانات)
                _inventoryChecks.Remove(_selectedCheck);
                _selectedCheck = null;
                UpdateButtonsState();
            }
        }

        private void PrintCheckButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCheck == null)
            {
                MessageBox.Show("الرجاء تحديد عملية جرد للطباعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // محاكاة طباعة تقرير الجرد
                // هنا يمكن إضافة الكود الفعلي للطباعة

                MessageBox.Show("تم إرسال تقرير الجرد إلى الطابعة", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة تقرير الجرد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                // يمكن إضافة تسجيل الخطأ في ملف السجل هنا
            }
        }

        private void ExportCheckButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح مربع حوار حفظ الملف
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "ملفات Excel (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv",
                    Title = "تصدير تقرير الجرد"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // محاكاة تصدير تقرير الجرد
                    // هنا يمكن إضافة الكود الفعلي لتصدير البيانات إلى ملف Excel أو CSV

                    MessageBox.Show("تم تصدير تقرير الجرد بنجاح", "تم التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير تقرير الجرد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                // يمكن إضافة تسجيل الخطأ في ملف السجل هنا
            }
        }

        private void ViewCheckButton_Click(object sender, RoutedEventArgs e)
        {
            Button button = sender as Button;
            if (button != null)
            {
                InventoryCheckViewModel check = button.Tag as InventoryCheckViewModel;

                // فتح نافذة عرض تفاصيل الجرد
                InventoryCheckDetailsWindow detailsWindow = new InventoryCheckDetailsWindow(check, true);
                detailsWindow.Owner = Window.GetWindow(this);
                detailsWindow.ShowDialog();
            }
        }

        private void PerformCheckButton_Click(object sender, RoutedEventArgs e)
        {
            Button button = sender as Button;
            if (button != null)
            {
                InventoryCheckViewModel check = button.Tag as InventoryCheckViewModel;

                // فتح نافذة تنفيذ الجرد
                InventoryCheckExecutionWindow executionWindow = new InventoryCheckExecutionWindow(check);
                executionWindow.Owner = Window.GetWindow(this);

                if (executionWindow.ShowDialog() == true)
                {
                    // تحديث قائمة عمليات الجرد
                    int warehouseId = WarehouseComboBox.SelectedIndex;
                    int statusId = StatusFilterComboBox.SelectedIndex;
                    LoadInventoryChecks(warehouseId, statusId);
                }
            }
        }

        private void ApproveCheckButton_Click(object sender, RoutedEventArgs e)
        {
            Button button = sender as Button;
            if (button != null)
            {
                InventoryCheckViewModel check = button.Tag as InventoryCheckViewModel;

                MessageBoxResult result = MessageBox.Show("هل أنت متأكد من اعتماد عملية الجرد؟ سيتم تعديل المخزون بناءً على نتائج الجرد.", "تأكيد الاعتماد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // اعتماد عملية الجرد (سيتم استبداله بالتحديث في قاعدة البيانات)
                    check.StatusId = 3; // معتمد
                    check.StatusText = "معتمد";
                    check.StatusColor = new SolidColorBrush(Colors.Purple);
                    check.CanApprove = Visibility.Collapsed;

                    // تحديث قائمة عمليات الجرد
                    InventoryChecksDataGrid.Items.Refresh();

                    MessageBox.Show("تم اعتماد عملية الجرد بنجاح وتعديل المخزون", "تم الاعتماد", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void ExpiryPeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            int days = GetSelectedExpiryDays();
            LoadExpiryProducts(days);
        }

        private int GetSelectedExpiryDays()
        {
            switch (ExpiryPeriodComboBox.SelectedIndex)
            {
                case 0: return 7;  // 7 أيام
                case 1: return 15; // 15 يوم
                case 2: return 30; // 30 يوم
                case 3: return 60; // 60 يوم
                case 4: return 90; // 90 يوم
                case 5: return 0;  // المنتهية فقط
                case 6: return 1000; // الكل
                default: return 7;
            }
        }

        private void RemoveExpiredButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من وجود منتجات محددة
            bool anySelected = false;
            List<ExpiryProductViewModel> selectedProducts = new List<ExpiryProductViewModel>();

            foreach (ExpiryProductViewModel product in ExpiryProductsDataGrid.Items)
            {
                if (product.IsSelected)
                {
                    anySelected = true;
                    selectedProducts.Add(product);
                }
            }

            if (!anySelected)
            {
                MessageBox.Show("الرجاء تحديد منتج واحد على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إزالة المنتجات المنتهية المحددة من المخزون؟", "تأكيد الإزالة", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // إزالة المنتجات المنتهية (سيتم استبداله بالتحديث في قاعدة البيانات)
                foreach (var product in selectedProducts)
                {
                    (_expiryProducts as ObservableCollection<ExpiryProductViewModel>).Remove(product);
                }

                // تحديث قائمة المنتجات
                ExpiryProductsDataGrid.Items.Refresh();

                MessageBox.Show("تم إزالة المنتجات المنتهية بنجاح", "تمت الإزالة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    // فئات عرض البيانات
    public class InventoryCheckViewModel
    {
        public int Id { get; set; }
        public required string CheckNumber { get; set; }
        public DateTime CheckDate { get; set; }
        public int WarehouseId { get; set; }
        public required string WarehouseName { get; set; }
        public required string UserName { get; set; }
        public int ItemCount { get; set; }
        public int CheckedItemCount { get; set; }
        public int DifferenceCount { get; set; }
        public int StatusId { get; set; }
        public required string StatusText { get; set; }
        public required SolidColorBrush StatusColor { get; set; }
        public Visibility CanPerform { get; set; } = Visibility.Collapsed;
        public Visibility CanApprove { get; set; } = Visibility.Collapsed;
        public string Notes { get; set; } = string.Empty;
    }

    public class ExpiryProductViewModel
    {
        public int Id { get; set; }
        public required string Barcode { get; set; }
        public required string Name { get; set; }
        public int Quantity { get; set; }
        public DateTime ExpiryDate { get; set; }
        public required string WarehouseName { get; set; }
        public required string StatusText { get; set; }
        public required SolidColorBrush StatusColor { get; set; }
        public int DaysRemaining { get; set; }
        public bool IsSelected { get; set; }
    }
}
