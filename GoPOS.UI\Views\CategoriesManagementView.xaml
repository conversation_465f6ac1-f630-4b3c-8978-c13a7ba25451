<UserControl x:Class="GoPOS.UI.Views.CategoriesManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="8" Margin="0,0,0,20">
            <Grid Margin="15,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="إدارة فئات المنتجات" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="إضافة وتعديل وحذف فئات المنتجات" FontSize="14" Foreground="White" Margin="0,5,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="AddCategoryButton" 
                            Width="120" 
                            Height="40" 
                            Margin="0,0,10,0" 
                            Click="AddCategoryButton_Click"
                            Background="White"
                            Foreground="#2196F3"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="➕" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إضافة فئة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="RefreshButton" 
                            Width="100" 
                            Height="40" 
                            Click="RefreshButton_Click"
                            Background="White"
                            Foreground="#2196F3"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- جدول الفئات -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة الفئات -->
            <DataGrid x:Name="CategoriesDataGrid" 
                      Grid.Column="0" 
                      AutoGenerateColumns="False" 
                      IsReadOnly="True" 
                      SelectionMode="Single" 
                      SelectionChanged="CategoriesDataGrid_SelectionChanged"
                      AlternatingRowBackground="#F5F5F5" 
                      BorderBrush="#DDDDDD" 
                      BorderThickness="1" 
                      RowHeight="35" 
                      HeadersVisibility="Column" 
                      GridLinesVisibility="Horizontal" 
                      HorizontalGridLinesBrush="#EEEEEE">
                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#2196F3"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Padding" Value="10,0"/>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="كود الفئة" Binding="{Binding Code}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTemplateColumn Header="اسم الفئة" Width="*">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="5">
                                    <Border Width="16" Height="16" CornerRadius="8" Margin="0,0,5,0">
                                        <Border.Background>
                                            <SolidColorBrush Color="{Binding ColorBrush}"/>
                                        </Border.Background>
                                    </Border>
                                    <TextBlock Text="{Binding Name}" FontWeight="SemiBold" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="عدد المنتجات" Binding="{Binding ProductCount}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTemplateColumn Header="الحالة" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="4" Padding="5,2" Margin="5">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsActive}" Value="False">
                                                    <Setter Property="Background" Value="#F44336"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Foreground="White" FontWeight="SemiBold" HorizontalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                        <Setter Property="Text" Value="نشط"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="False">
                                                        <Setter Property="Text" Value="غير نشط"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <Button x:Name="EditCategoryButton" 
                        Content="تعديل" 
                        Width="100" 
                        Height="35" 
                        Margin="0,0,0,10" 
                        Click="EditCategoryButton_Click"
                        Background="#FF9800"
                        Foreground="White"
                        IsEnabled="False">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                </Button>
                
                <Button x:Name="DeleteCategoryButton" 
                        Content="حذف" 
                        Width="100" 
                        Height="35" 
                        Margin="0,0,0,10" 
                        Click="DeleteCategoryButton_Click"
                        Background="#F44336"
                        Foreground="White"
                        IsEnabled="False">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                </Button>
                
                <Button x:Name="ViewProductsButton" 
                        Content="عرض المنتجات" 
                        Width="100" 
                        Height="35" 
                        Margin="0,0,0,10" 
                        Click="ViewProductsButton_Click"
                        Background="#673AB7"
                        Foreground="White"
                        IsEnabled="False">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                </Button>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
