using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Media;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for CategoryEditorWindow.xaml
    /// </summary>
    public partial class CategoryEditorWindow : Window
    {
        private CategoryViewModel _category;
        private bool _isEditMode;

        public CategoryEditorWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            _category = new CategoryViewModel
            {
                Id = 0,
                Name = "",
                Code = "",
                Color = "#2196F3",
                Description = "",
                IsActive = true
            };

            // إضافة معالج حدث لتغيير لون المعاينة عند تغيير قيمة اللون
            ColorTextBox.TextChanged += ColorTextBox_TextChanged;
        }

        public CategoryEditorWindow(CategoryViewModel category)
        {
            InitializeComponent();
            _isEditMode = true;
            _category = category;
            TitleTextBlock.Text = "تعديل الفئة";

            // إضافة معالج حدث لتغيير لون المعاينة عند تغيير قيمة اللون
            ColorTextBox.TextChanged += ColorTextBox_TextChanged;

            // تحميل بيانات الفئة
            LoadCategoryData();
        }

        private void LoadCategoryData()
        {
            try
            {
                // تعبئة حقول النموذج بالبيانات الحالية
                NameTextBox.Text = _category.Name;
                CodeTextBox.Text = _category.Code;
                ColorTextBox.Text = _category.Color;
                DescriptionTextBox.Text = _category.Description ?? string.Empty;
                IsActiveCheckBox.IsChecked = _category.IsActive;

                // تحديث لون المعاينة
                UpdateColorPreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ColorTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateColorPreview();
        }

        private void UpdateColorPreview()
        {
            try
            {
                string colorText = ColorTextBox.Text.Trim();
                if (IsValidHexColor(colorText))
                {
                    // تحويل النص إلى لون
                    Color color = (Color)ColorConverter.ConvertFromString(colorText);
                    ColorPreview.Background = new SolidColorBrush(color);
                }
            }
            catch
            {
                // في حالة حدوث خطأ، استخدام اللون الافتراضي
                ColorPreview.Background = new SolidColorBrush(Colors.Gray);
            }
        }

        private bool IsValidHexColor(string colorText)
        {
            // التحقق من صحة تنسيق اللون
            return Regex.IsMatch(colorText, @"^#(?:[0-9a-fA-F]{3}){1,2}$");
        }

        private void GenerateCodeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // توليد كود عشوائي للفئة
                Random random = new Random();
                string code = "CAT";
                
                // إضافة 4 أرقام عشوائية
                for (int i = 0; i < 4; i++)
                {
                    code += random.Next(0, 10).ToString();
                }
                
                // تحديث حقل الكود
                CodeTextBox.Text = code;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء توليد الكود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // تعطيل زر الحفظ لمنع النقر المتكرر
            SaveButton.IsEnabled = false;

            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                {
                    SaveButton.IsEnabled = true;
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // جمع البيانات من النموذج
                _category.Name = NameTextBox.Text.Trim();
                _category.Code = CodeTextBox.Text.Trim();
                _category.Color = ColorTextBox.Text.Trim();
                _category.Description = DescriptionTextBox.Text.Trim();
                _category.IsActive = IsActiveCheckBox.IsChecked ?? true;

                // حفظ البيانات (سيتم استبداله بالحفظ في قاعدة البيانات)
                if (_isEditMode)
                {
                    // تحديث الفئة الموجودة
                    // هنا يمكن إضافة كود لتحديث الفئة في قاعدة البيانات

                    MessageBox.Show("تم تحديث الفئة بنجاح", "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // إضافة فئة جديدة
                    _category.Id = GenerateNewId(); // توليد معرف جديد (سيتم استبداله بمعرف من قاعدة البيانات)

                    // هنا يمكن إضافة كود لإضافة الفئة إلى قاعدة البيانات

                    MessageBox.Show("تمت إضافة الفئة بنجاح", "تمت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                SaveButton.IsEnabled = true;
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private bool ValidateInput()
        {
            try
            {
                // التحقق من اسم الفئة
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم الفئة", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    NameTextBox.Focus();
                    return false;
                }

                // التحقق من كود الفئة
                if (string.IsNullOrWhiteSpace(CodeTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال كود الفئة", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    CodeTextBox.Focus();
                    return false;
                }

                // التحقق من لون الفئة
                if (string.IsNullOrWhiteSpace(ColorTextBox.Text) || !IsValidHexColor(ColorTextBox.Text.Trim()))
                {
                    MessageBox.Show("الرجاء إدخال لون صحيح بتنسيق #RRGGBB", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ColorTextBox.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التحقق من البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private int GenerateNewId()
        {
            // توليد معرف عشوائي (سيتم استبداله بمعرف من قاعدة البيانات)
            Random random = new Random();
            return random.Next(100, 1000);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // توسيع فئة CategoryViewModel لتشمل الخصائص الإضافية
    public class CategoryViewModel
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Color { get; set; } = "#2196F3";
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public int ProductCount { get; set; } = 0;
    }
}
