using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.IO;
using System.Diagnostics;
using System.Collections.Generic;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// فئة سياق قاعدة البيانات المسؤولة عن إدارة الاتصال بقاعدة البيانات
    /// </summary>
    public class DatabaseContext : IDisposable
    {
        private readonly string _connectionString;
        private SqlConnection _connection;
        private bool _disposed = false;

        /// <summary>
        /// إنشاء سياق قاعدة البيانات جديد
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال بقاعدة البيانات</param>
        public DatabaseContext(string connectionString = null)
        {
            _connectionString = connectionString ?? GetDefaultConnectionString();
            _connection = new SqlConnection(_connectionString);
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال الافتراضية
        /// </summary>
        private string GetDefaultConnectionString()
        {
            string serverName = "localhost";
            string databaseName = "GoPOS";
            string username = "sa";
            string password = "";
            bool useWindowsAuth = true;

            // قراءة إعدادات قاعدة البيانات من ملف الإعدادات إذا كان موجوداً
            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GoPOS");
            string settingsPath = Path.Combine(appDataPath, "settings.json");
            if (File.Exists(settingsPath))
            {
                try
                {
                    string settingsJson = File.ReadAllText(settingsPath);
                    // يمكن استخدام مكتبة JSON لقراءة الإعدادات
                    // هنا نستخدم طريقة بسيطة للتوضيح
                    if (settingsJson.Contains("\"ServerName\":"))
                    {
                        int start = settingsJson.IndexOf("\"ServerName\":") + 14;
                        int end = settingsJson.IndexOf("\"", start);
                        serverName = settingsJson.Substring(start, end - start);
                    }

                    if (settingsJson.Contains("\"DatabaseName\":"))
                    {
                        int start = settingsJson.IndexOf("\"DatabaseName\":") + 16;
                        int end = settingsJson.IndexOf("\"", start);
                        databaseName = settingsJson.Substring(start, end - start);
                    }

                    if (settingsJson.Contains("\"UseWindowsAuth\":"))
                    {
                        int start = settingsJson.IndexOf("\"UseWindowsAuth\":") + 17;
                        useWindowsAuth = settingsJson.Substring(start, 5).Contains("true");
                    }

                    if (!useWindowsAuth)
                    {
                        if (settingsJson.Contains("\"Username\":"))
                        {
                            int start = settingsJson.IndexOf("\"Username\":") + 12;
                            int end = settingsJson.IndexOf("\"", start);
                            username = settingsJson.Substring(start, end - start);
                        }

                        if (settingsJson.Contains("\"Password\":"))
                        {
                            int start = settingsJson.IndexOf("\"Password\":") + 12;
                            int end = settingsJson.IndexOf("\"", start);
                            password = settingsJson.Substring(start, end - start);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"حدث خطأ أثناء قراءة ملف الإعدادات: {ex.Message}");
                }
            }

            // إنشاء سلسلة الاتصال
            if (useWindowsAuth)
            {
                return $"Server={serverName};Database={databaseName};Integrated Security=True;";
            }
            else
            {
                return $"Server={serverName};Database={databaseName};User Id={username};Password={password};";
            }
        }

        /// <summary>
        /// فتح الاتصال بقاعدة البيانات
        /// </summary>
        public async Task OpenConnectionAsync()
        {
            if (_connection.State != ConnectionState.Open)
            {
                await _connection.OpenAsync();
            }
        }

        /// <summary>
        /// إغلاق الاتصال بقاعدة البيانات
        /// </summary>
        public void CloseConnection()
        {
            if (_connection.State != ConnectionState.Closed)
            {
                _connection.Close();
            }
        }

        /// <summary>
        /// الحصول على الاتصال
        /// </summary>
        public SqlConnection GetConnection()
        {
            return _connection;
        }

        /// <summary>
        /// إنشاء قاعدة البيانات إذا لم تكن موجودة
        /// </summary>
        public async Task CreateDatabaseIfNotExistsAsync()
        {
     // الحصول على اسم قاعدة البيانات من سلسلة الاتصال
            string databaseName = GetDatabaseNameFromConnectionString();

            // إنشاء سلسلة اتصال بدون اسم قاعدة البيانات
            string masterConnectionString = _connectionString.Replace($"Database={databaseName};", "");

            using (SqlConnection masterConnection = new SqlConnection(masterConnectionString))
            {
                await masterConnection.OpenAsync();

                // التحقق من وجود قاعدة البيانات
                string checkQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'";
                using (SqlCommand checkCommand = new SqlCommand(checkQuery, masterConnection))
                {
                    int databaseCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());

                    if (databaseCount == 0)
                    {
                        // إنشاء قاعدة البيانات
                        string createQuery = $"CREATE DATABASE [{databaseName}]";
                        using (SqlCommand createCommand = new SqlCommand(createQuery, masterConnection))
                        {
                            await createCommand.ExecuteNonQueryAsync();
                        }

                        // إنشاء الجداول
                        await CreateTablesAsync();
                    }
                }
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                await OpenConnectionAsync();
                return true;
            }
            catch
            {
                return false;
            }
            finally
            {
                CloseConnection();
            }
        }

        /// <summary>
        /// الحصول على اسم قاعدة البيانات من سلسلة الاتصال
        /// </summary>
        private string GetDatabaseNameFromConnectionString()
        {
            string databaseName = "GoPOS";

            // البحث عن اسم قاعدة البيانات في سلسلة الاتصال
            string[] parts = _connectionString.Split(';');
            foreach (string part in parts)
            {
                if (part.Trim().StartsWith("Database=", StringComparison.OrdinalIgnoreCase))
                {
                    databaseName = part.Trim().Substring("Database=".Length);
                    break;
                }

                if (part.Trim().StartsWith("Initial Catalog=", StringComparison.OrdinalIgnoreCase))
                {
                    databaseName = part.Trim().Substring("Initial Catalog=".Length);
                    break;
                }
            }

            return databaseName;
        }

        /// <summary>
        /// تنفيذ استعلام قراءة وإرجاع DataTable
        /// </summary>
        /// <param name="query">استعلام SQL</param>
        /// <param name="parameters">معلمات الاستعلام</param>
        /// <returns>DataTable تحتوي على نتائج الاستعلام</returns>
        public async Task<DataTable> ExecuteQueryAsync(string query, SqlParameter[] parameters = null)
        {
            DataTable dataTable = new DataTable();

            try
            {
                await OpenConnectionAsync();

                using (SqlCommand command = new SqlCommand(query, _connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(dataTable);
                    }
                }
            }
            finally
            {
                CloseConnection();
            }

            return dataTable;
        }

        /// <summary>
        /// تنفيذ استعلام غير قارئ (INSERT, UPDATE, DELETE)
        /// </summary>
        /// <param name="query">استعلام SQL</param>
        /// <param name="parameters">معلمات الاستعلام</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> ExecuteNonQueryAsync(string query, SqlParameter[] parameters = null)
        {
            int rowsAffected = 0;

            try
            {
                await OpenConnectionAsync();

                using (SqlCommand command = new SqlCommand(query, _connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    rowsAffected = await command.ExecuteNonQueryAsync();
                }
            }
            finally
            {
                CloseConnection();
            }

            return rowsAffected;
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">استعلام SQL</param>
        /// <param name="parameters">معلمات الاستعلام</param>
        /// <returns>القيمة الناتجة من الاستعلام</returns>
        public async Task<object> ExecuteScalarAsync(string query, SqlParameter[] parameters = null)
        {
            object result = null;

            try
            {
                await OpenConnectionAsync();

                using (SqlCommand command = new SqlCommand(query, _connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    result = await command.ExecuteScalarAsync();
                }
            }
            finally
            {
                CloseConnection();
            }

            return result;
        }

        /// <summary>
        /// التخلص من الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// التخلص من الموارد
        /// </summary>
        /// <param name="disposing">هل يتم التخلص من الموارد المدارة</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // التخلص من الموارد المدارة
                    if (_connection != null)
                    {
                        CloseConnection();
                        _connection.Dispose();
                        _connection = null;
                    }
                }

                // تعيين العلامة
                _disposed = true;
            }
        }

        /// <summary>
        /// المنظف
        /// </summary>
        ~DatabaseContext()
        {
            Dispose(false);
        }

        /// <summary>
        /// إنشاء الجداول
        /// </summary>
        private async Task CreateTablesAsync()
        {
            // قائمة بأوامر إنشاء الجداول
            List<string> createTableQueries = new List<string>
            {
                // جدول المستخدمين
                @"
                CREATE TABLE Users (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Username NVARCHAR(50) NOT NULL,
                    PasswordHash NVARCHAR(255) NOT NULL,
                    FullName NVARCHAR(100) NOT NULL,
                    Email NVARCHAR(100) NULL,
                    Role INT NOT NULL DEFAULT 0,
                    IsActive BIT NOT NULL DEFAULT 1,
                    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
                    LastLogin DATETIME NULL
                )",

                // جدول صلاحيات المستخدمين
                @"
                CREATE TABLE UserPermissions (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    UserId INT NOT NULL,
                    CanManageSales BIT NOT NULL DEFAULT 1,
                    CanManageInvoices BIT NOT NULL DEFAULT 1,
                    CanReturnInvoices BIT NOT NULL DEFAULT 1,
                    CanManageProducts BIT NOT NULL DEFAULT 1,
                    CanManageCategories BIT NOT NULL DEFAULT 1,
                    CanManageInventory BIT NOT NULL DEFAULT 1,
                    CanManageUsers BIT NOT NULL DEFAULT 1,
                    CanViewReports BIT NOT NULL DEFAULT 1,
                    CanManageSettings BIT NOT NULL DEFAULT 1,
                    CanPerformMaintenance BIT NOT NULL DEFAULT 0,
                    CanManageBackups BIT NOT NULL DEFAULT 0,
                    CanAccessDatabase BIT NOT NULL DEFAULT 0,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )",

                // جدول الفئات
                @"
                CREATE TABLE Categories (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Description NVARCHAR(255) NULL,
                    Color NVARCHAR(20) NULL,
                    ParentId INT NULL,
                    IsActive BIT NOT NULL DEFAULT 1,
                    FOREIGN KEY (ParentId) REFERENCES Categories(Id)
                )",

                // جدول المنتجات
                @"
                CREATE TABLE Products (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Description NVARCHAR(255) NULL,
                    Barcode NVARCHAR(50) NULL,
                    CategoryId INT NULL,
                    Price DECIMAL(18, 2) NOT NULL,
                    CostPrice DECIMAL(18, 2) NOT NULL,
                    Quantity DECIMAL(18, 2) NOT NULL DEFAULT 0,
                    MinQuantity DECIMAL(18, 2) NOT NULL DEFAULT 0,
                    IsActive BIT NOT NULL DEFAULT 1,
                    ImagePath NVARCHAR(255) NULL,
                    FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                )",

                // جدول العملاء
                @"
                CREATE TABLE Customers (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Phone NVARCHAR(20) NULL,
                    Email NVARCHAR(100) NULL,
                    Address NVARCHAR(255) NULL,
                    Notes NVARCHAR(MAX) NULL,
                    IsActive BIT NOT NULL DEFAULT 1
                )",

                // جدول الفواتير
                @"
                CREATE TABLE Invoices (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    InvoiceNumber NVARCHAR(50) NOT NULL,
                    Date DATETIME NOT NULL DEFAULT GETDATE(),
                    CustomerId INT NULL,
                    UserId INT NOT NULL,
                    Subtotal DECIMAL(18, 2) NOT NULL,
                    TaxAmount DECIMAL(18, 2) NOT NULL,
                    DiscountAmount DECIMAL(18, 2) NOT NULL,
                    Total DECIMAL(18, 2) NOT NULL,
                    PaymentMethod INT NOT NULL DEFAULT 0,
                    Status INT NOT NULL DEFAULT 0,
                    Notes NVARCHAR(MAX) NULL,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )",

                // جدول عناصر الفاتورة
                @"
                CREATE TABLE InvoiceItems (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    InvoiceId INT NOT NULL,
                    ProductId INT NOT NULL,
                    Quantity DECIMAL(18, 2) NOT NULL,
                    UnitPrice DECIMAL(18, 2) NOT NULL,
                    TaxAmount DECIMAL(18, 2) NOT NULL,
                    DiscountAmount DECIMAL(18, 2) NOT NULL,
                    Total DECIMAL(18, 2) NOT NULL,
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )",

                // جدول مدفوعات الفاتورة
                @"
                CREATE TABLE InvoicePayments (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    InvoiceId INT NOT NULL,
                    PaymentMethod INT NOT NULL DEFAULT 0,
                    Amount DECIMAL(18, 2) NOT NULL,
                    ReferenceNumber NVARCHAR(50) NULL,
                    PaymentDate DATETIME NOT NULL DEFAULT GETDATE(),
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id)
                )",

                // جدول المنصرفات
                @"
                CREATE TABLE Expenses (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Date DATETIME NOT NULL DEFAULT GETDATE(),
                    Amount DECIMAL(18, 2) NOT NULL,
                    Description NVARCHAR(255) NOT NULL,
                    Type INT NOT NULL DEFAULT 0,
                    UserId INT NOT NULL,
                    IsConfirmed BIT NOT NULL DEFAULT 0,
                    Beneficiary NVARCHAR(100) NULL,
                    DocumentNumber NVARCHAR(50) NULL,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )",

                // جدول خطط الأقساط
                @"
                CREATE TABLE InstallmentPlans (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    CustomerId INT NOT NULL,
                    InvoiceId INT NULL,
                    TotalAmount DECIMAL(18, 2) NOT NULL,
                    DownPayment DECIMAL(18, 2) NOT NULL,
                    RemainingAmount DECIMAL(18, 2) NOT NULL,
                    NumberOfInstallments INT NOT NULL,
                    InstallmentAmount DECIMAL(18, 2) NOT NULL,
                    StartDate DATETIME NOT NULL,
                    EndDate DATETIME NOT NULL,
                    Status INT NOT NULL DEFAULT 0,
                    Notes NVARCHAR(MAX) NULL,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id)
                )",

                // جدول الأقساط
                @"
                CREATE TABLE Installments (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    PlanId INT NOT NULL,
                    DueDate DATETIME NOT NULL,
                    Amount DECIMAL(18, 2) NOT NULL,
                    Status INT NOT NULL DEFAULT 0,
                    PaymentDate DATETIME NULL,
                    Notes NVARCHAR(MAX) NULL,
                    FOREIGN KEY (PlanId) REFERENCES InstallmentPlans(Id)
                )"
            };

            // إنشاء الجداول
            foreach (string query in createTableQueries)
            {
                try
                {
                    await ExecuteNonQueryAsync(query);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"حدث خطأ أثناء إنشاء الجداول: {ex.Message}");
                }
            }

            // إنشاء مستخدم افتراضي
            try
            {
                string adminPasswordHash = "jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg="; // admin
                string createAdminQuery = @"
                    INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive)
                    VALUES ('admin', @PasswordHash, 'مدير النظام', 0, 1);

                    DECLARE @AdminId INT = SCOPE_IDENTITY();

                    INSERT INTO UserPermissions (UserId, CanManageSales, CanManageInvoices, CanReturnInvoices,
                        CanManageProducts, CanManageCategories, CanManageInventory, CanManageUsers,
                        CanViewReports, CanManageSettings, CanPerformMaintenance, CanManageBackups, CanAccessDatabase)
                    VALUES (@AdminId, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);";

                SqlParameter[] parameters = new SqlParameter[]
                {
                    new SqlParameter("@PasswordHash", adminPasswordHash)
                };

                await ExecuteNonQueryAsync(createAdminQuery, parameters);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"حدث خطأ أثناء إنشاء المستخدم الافتراضي: {ex.Message}");
            }
        }
    }
}
