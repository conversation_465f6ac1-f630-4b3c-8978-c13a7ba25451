<UserControl x:Class="GoPOS.UI.Views.CustomersManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <Button x:Name="AddCustomerButton" Content="إضافة عميل" Width="120" Height="40" Margin="0,0,10,0" Click="AddCustomerButton_Click"/>
                <Button x:Name="EditCustomerButton" Content="تعديل" Width="80" Height="40" Margin="0,0,10,0" Click="EditCustomerButton_Click"/>
                <Button x:Name="DeleteCustomerButton" Content="حذف" Width="80" Height="40" Background="#F44336" Foreground="White" Click="DeleteCustomerButton_Click"/>
            </StackPanel>

            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <TextBox x:Name="SearchTextBox" Width="200" Height="40" Padding="5" Margin="0,0,5,0" KeyDown="SearchTextBox_KeyDown"/>
                <Button x:Name="SearchButton" Content="بحث" Width="80" Height="40" Click="SearchButton_Click"/>
            </StackPanel>
        </Grid>

        <!-- جدول العملاء -->
        <DataGrid x:Name="CustomersDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectionChanged="CustomersDataGrid_SelectionChanged">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60"/>
                <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120"/>
                <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="180"/>
                <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="200"/>
                <DataGridTextColumn Header="تاريخ التسجيل" Binding="{Binding CreatedAt, StringFormat={}{0:yyyy/MM/dd}}" Width="120"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
