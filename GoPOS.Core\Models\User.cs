using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج المستخدم
    /// </summary>
    public class User
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Username { get; set; }

        [Required]
        [MaxLength(100)]
        public string PasswordHash { get; set; }

        [Required]
        [MaxLength(100)]
        public string FullName { get; set; }

        [MaxLength(100)]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        public UserRole Role { get; set; }

        public bool IsActive { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? LastLogin { get; set; }

        // العلاقات
        public ICollection<Order> Orders { get; set; }
        public ICollection<InventoryTransaction> InventoryTransactions { get; set; }
        public ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        public ICollection<InventoryCheck> InventoryChecks { get; set; }
        public ICollection<InventoryCheck> ApprovedInventoryChecks { get; set; }
    }

    public enum UserRole
    {
        Administrator = 0,
        Manager = 1,
        Cashier = 2
    }
}
