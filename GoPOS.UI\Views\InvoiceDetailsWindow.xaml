<Window x:Class="GoPOS.UI.Views.InvoiceDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تفاصيل الفاتورة"
        Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinWidth="800" MinHeight="600">

    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#2196F3"/>
        </Style>

        <Style x:Key="FieldLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5,0,5"/>
        </Style>

        <Style x:Key="FieldValueStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,5,0,5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
            <Style.Resources>
                <Style TargetType="Border">
                    <Setter Property="CornerRadius" Value="4"/>
                </Style>
            </Style.Resources>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس الفاتورة -->
        <Border Grid.Row="0" Background="#2196F3" Padding="15" CornerRadius="4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="InvoiceNumberText" Text="فاتورة رقم: INV-001" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="📝" FontSize="20" Margin="10,0,0,0" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="InvoiceStatusText" Text="الحالة: مكتملة" FontSize="16" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- محتوى الفاتورة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- معلومات الفاتورة -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- معلومات الفاتورة الأساسية -->
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="معلومات الفاتورة" Style="{StaticResource SectionHeaderStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الفاتورة:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="InvoiceNumberValueText" Grid.Row="0" Grid.Column="1" Text="INV-001" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="التاريخ:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="InvoiceDateValueText" Grid.Row="1" Grid.Column="1" Text="2023/05/15" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="الحالة:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="InvoiceStatusValueText" Grid.Row="2" Grid.Column="1" Text="مكتملة" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="طريقة الدفع:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="PaymentMethodValueText" Grid.Row="3" Grid.Column="1" Text="نقداً" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="المستخدم:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="UserNameValueText" Grid.Row="4" Grid.Column="1" Text="المدير" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="5" Grid.Column="0" Text="ملاحظات:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="NotesValueText" Grid.Row="5" Grid.Column="1" Text="فاتورة نقدية" Style="{StaticResource FieldValueStyle}" TextWrapping="Wrap"/>
                        </Grid>
                    </StackPanel>

                    <!-- معلومات العميل -->
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="معلومات العميل" Style="{StaticResource SectionHeaderStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم العميل:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="CustomerNameValueText" Grid.Row="0" Grid.Column="1" Text="عميل نقدي" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم العميل:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="CustomerIdValueText" Grid.Row="1" Grid.Column="1" Text="1" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="حالة الدفع:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="PaymentStatusValueText" Grid.Row="2" Grid.Column="1" Text="مدفوعة بالكامل" Style="{StaticResource FieldValueStyle}"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="نوع الفاتورة:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="InvoiceTypeValueText" Grid.Row="3" Grid.Column="1" Text="فاتورة عادية" Style="{StaticResource FieldValueStyle}"/>
                        </Grid>
                    </StackPanel>
                </Grid>

                <!-- معلومات الدفع -->
                <StackPanel Grid.Row="1" Margin="0,10,0,0">
                    <TextBlock Text="معلومات الدفع" Style="{StaticResource SectionHeaderStyle}"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#F5F5F5" Margin="0,0,5,0" Padding="10" CornerRadius="4">
                            <StackPanel>
                                <TextBlock Text="المجموع الفرعي" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="SubtotalValueText" Text="150.00" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Background="#F5F5F5" Margin="5,0" Padding="10" CornerRadius="4">
                            <StackPanel>
                                <TextBlock Text="الضريبة" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TaxValueText" Text="22.50" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Background="#4CAF50" Margin="5,0,0,0" Padding="10" CornerRadius="4">
                            <StackPanel>
                                <TextBlock Text="المجموع النهائي" FontWeight="SemiBold" HorizontalAlignment="Center" Foreground="White"/>
                                <TextBlock x:Name="TotalValueText" Text="172.50" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0" Foreground="White"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#F5F5F5" Margin="0,0,5,0" Padding="10" CornerRadius="4">
                            <StackPanel>
                                <TextBlock Text="المبلغ المدفوع" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="PaidAmountValueText" Text="172.50" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Background="#F5F5F5" Margin="5,0" Padding="10" CornerRadius="4">
                            <StackPanel>
                                <TextBlock Text="المبلغ المتبقي" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="RemainingAmountValueText" Text="0.00" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Background="#F5F5F5" Margin="5,0,0,0" Padding="10" CornerRadius="4">
                            <StackPanel>
                                <TextBlock Text="الخصم" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="DiscountValueText" Text="0.00" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>

                <!-- عناصر الفاتورة -->
                <StackPanel Grid.Row="2" Margin="0,10,0,0">
                    <TextBlock Text="عناصر الفاتورة" Style="{StaticResource SectionHeaderStyle}"/>

                    <DataGrid x:Name="InvoiceItemsDataGrid" AutoGenerateColumns="False" IsReadOnly="True"
                              BorderThickness="1" GridLinesVisibility="All" AlternatingRowBackground="#F5F5F5"
                              CanUserSortColumns="True" HeadersVisibility="All" Margin="0,5,0,0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="#" Binding="{Binding Index}" Width="50"/>
                            <DataGridTextColumn Header="المنتج" Binding="{Binding ProductName}" Width="*"/>
                            <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                            <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, StringFormat={}{0:N2}}" Width="100"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="الخصم" Binding="{Binding DiscountAmount, StringFormat={}{0:N2}}" Width="80"/>
                            <DataGridTextColumn Header="الضريبة" Binding="{Binding TaxAmount, StringFormat={}{0:N2}}" Width="80"/>
                            <DataGridTextColumn Header="المجموع" Binding="{Binding Total, StringFormat={}{0:N2}}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>

                <!-- معلومات الأقساط (إذا كانت فاتورة أقساط) -->
                <StackPanel x:Name="InstallmentInfoPanel" Grid.Row="3" Margin="0,10,0,0" Visibility="Collapsed">
                    <TextBlock Text="معلومات الأقساط" Style="{StaticResource SectionHeaderStyle}"/>

                    <DataGrid x:Name="InstallmentsDataGrid" AutoGenerateColumns="False" IsReadOnly="True"
                              BorderThickness="1" GridLinesVisibility="All" AlternatingRowBackground="#F5F5F5"
                              CanUserSortColumns="True" HeadersVisibility="All" Margin="0,5,0,0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="#" Binding="{Binding Index}" Width="50"/>
                            <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat={}{0:yyyy/MM/dd}}" Width="120"/>
                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat={}{0:N2}}" Width="100"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
                            <DataGridTextColumn Header="تاريخ الدفع" Binding="{Binding PaymentDate, StringFormat={}{0:yyyy/MM/dd}}" Width="120"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button x:Name="PrintButton" Content="طباعة" Style="{StaticResource ActionButtonStyle}" Background="#2196F3" Foreground="White" Click="PrintButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖨️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="طباعة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>

            <Button x:Name="ReturnInvoiceButton" Content="إرجاع الفاتورة" Style="{StaticResource ActionButtonStyle}" Background="#F44336" Foreground="White" Click="ReturnInvoiceButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="↩️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إرجاع الفاتورة" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>

            <Button x:Name="EditButton" Content="تعديل" Style="{StaticResource ActionButtonStyle}" Background="#FF9800" Foreground="White" Click="EditButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✏️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تعديل" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>

            <Button x:Name="CloseButton" Content="إغلاق" Style="{StaticResource ActionButtonStyle}" Background="#607D8B" Foreground="White" Click="CloseButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إغلاق" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </StackPanel>
    </Grid>
</Window>
