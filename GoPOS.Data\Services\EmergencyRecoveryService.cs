using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.Data.Services
{
    /// <summary>
    /// خدمة استعادة الطوارئ
    /// </summary>
    public class EmergencyRecoveryService
    {
        private readonly DatabaseContext _dbContext;
        private readonly MaintenanceRepository _maintenanceRepository;
        private readonly string _logFilePath;
        private readonly int _developerId;
        private readonly string _recoveryDirectory;
        private readonly string _cloudBackupDirectory;

        /// <summary>
        /// إنشاء خدمة استعادة الطوارئ
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        /// <param name="developerId">معرف المستخدم المطور</param>
        public EmergencyRecoveryService(DatabaseContext dbContext, int developerId)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _maintenanceRepository = new MaintenanceRepository(dbContext);
            _developerId = developerId;

            // إنشاء مجلد السجلات إذا لم يكن موجودًا
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // تحديد مسار ملف السجل
            _logFilePath = Path.Combine(logDirectory, "EmergencyRecovery.log");

            // تحديد مجلد استعادة الطوارئ
            _recoveryDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmergencyRecovery");
            if (!Directory.Exists(_recoveryDirectory))
            {
                Directory.CreateDirectory(_recoveryDirectory);
            }

            // تحديد مجلد النسخ الاحتياطي السحابي (محاكاة)
            _cloudBackupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CloudBackups");
            if (!Directory.Exists(_cloudBackupDirectory))
            {
                Directory.CreateDirectory(_cloudBackupDirectory);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية للطوارئ
        /// </summary>
        /// <returns>مسار ملف النسخة الاحتياطية</returns>
        public async Task<string> CreateEmergencyBackupAsync()
        {
            try
            {
                LogMessage("بدء إنشاء نسخة احتياطية للطوارئ...");

                // إنشاء نسخة احتياطية باستخدام MaintenanceRepository
                string backupPath = await _maintenanceRepository.CreateBackupAsync();

                // إنشاء نسخة من النسخة الاحتياطية في مجلد استعادة الطوارئ
                string emergencyBackupPath = Path.Combine(_recoveryDirectory, Path.GetFileName(backupPath));
                File.Copy(backupPath, emergencyBackupPath, true);

                // إنشاء ملف معلومات النسخة الاحتياطية
                string infoFilePath = Path.ChangeExtension(emergencyBackupPath, ".info");
                string infoContent = $"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                                    $"DatabaseName: {GetDatabaseNameFromConnectionString(_dbContext.ConnectionString)}\n" +
                                    $"BackupType: Emergency\n" +
                                    $"CreatedBy: Developer (ID: {_developerId})";
                File.WriteAllText(infoFilePath, infoContent);

                // نسخ النسخة الاحتياطية إلى المجلد السحابي (محاكاة)
                string cloudBackupPath = Path.Combine(_cloudBackupDirectory, Path.GetFileName(backupPath));
                File.Copy(backupPath, cloudBackupPath, true);

                // تسجيل العملية
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "إنشاء نسخة احتياطية للطوارئ",
                    $"تم إنشاء نسخة احتياطية للطوارئ في المسار: {emergencyBackupPath}");

                LogMessage($"تم إنشاء نسخة احتياطية للطوارئ بنجاح في المسار: {emergencyBackupPath}");
                return emergencyBackupPath;
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء إنشاء نسخة احتياطية للطوارئ: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// استعادة النظام من نسخة احتياطية للطوارئ
        /// </summary>
        /// <param name="backupPath">مسار ملف النسخة الاحتياطية (اختياري)</param>
        /// <returns>نتيجة عملية الاستعادة</returns>
        public async Task<RecoveryResult> RecoverSystemAsync(string backupPath = null)
        {
            RecoveryResult result = new RecoveryResult
            {
                StartTime = DateTime.Now,
                Success = false
            };

            try
            {
                LogMessage("بدء استعادة النظام من نسخة احتياطية للطوارئ...");

                // إذا لم يتم تحديد مسار النسخة الاحتياطية، استخدم أحدث نسخة احتياطية
                if (string.IsNullOrEmpty(backupPath))
                {
                    backupPath = GetLatestBackupFile();
                    if (string.IsNullOrEmpty(backupPath))
                    {
                        throw new FileNotFoundException("لم يتم العثور على نسخة احتياطية للطوارئ");
                    }
                }
                else if (!File.Exists(backupPath))
                {
                    throw new FileNotFoundException($"ملف النسخة الاحتياطية غير موجود: {backupPath}");
                }

                // إنشاء نسخة احتياطية قبل الاستعادة
                string preRecoveryBackupPath = await _maintenanceRepository.CreateBackupAsync();
                result.PreRecoveryBackupPath = preRecoveryBackupPath;

                // استعادة النسخة الاحتياطية
                await _maintenanceRepository.RestoreBackupAsync(backupPath);

                // تسجيل العملية
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "استعادة النظام من نسخة احتياطية للطوارئ",
                    $"تم استعادة النظام من النسخة الاحتياطية: {backupPath}");

                result.EndTime = DateTime.Now;
                result.Duration = (result.EndTime - result.StartTime).TotalSeconds;
                result.Success = true;
                result.RecoveredBackupPath = backupPath;

                LogMessage($"تم استعادة النظام بنجاح من النسخة الاحتياطية: {backupPath}");
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Duration = (result.EndTime - result.StartTime).TotalSeconds;
                result.ErrorMessage = ex.Message;

                LogMessage($"حدث خطأ أثناء استعادة النظام: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية كاملة للنظام (قاعدة البيانات والملفات)
        /// </summary>
        /// <returns>مسار ملف النسخة الاحتياطية</returns>
        public async Task<string> CreateFullSystemBackupAsync()
        {
            try
            {
                LogMessage("بدء إنشاء نسخة احتياطية كاملة للنظام...");

                // إنشاء نسخة احتياطية لقاعدة البيانات
                string dbBackupPath = await _maintenanceRepository.CreateBackupAsync();

                // إنشاء مجلد مؤقت للنسخة الاحتياطية
                string tempBackupDir = Path.Combine(Path.GetTempPath(), $"GoPOS_Backup_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(tempBackupDir);

                // نسخ ملف قاعدة البيانات إلى المجلد المؤقت
                string dbBackupFileName = Path.GetFileName(dbBackupPath);
                File.Copy(dbBackupPath, Path.Combine(tempBackupDir, dbBackupFileName), true);

                // نسخ ملفات التطبيق المهمة إلى المجلد المؤقت
                string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string configDirectory = Path.Combine(appDirectory, "Config");
                if (Directory.Exists(configDirectory))
                {
                    CopyDirectory(configDirectory, Path.Combine(tempBackupDir, "Config"));
                }

                // نسخ ملفات الموارد إلى المجلد المؤقت
                string resourcesDirectory = Path.Combine(appDirectory, "Resources");
                if (Directory.Exists(resourcesDirectory))
                {
                    CopyDirectory(resourcesDirectory, Path.Combine(tempBackupDir, "Resources"));
                }

                // إنشاء ملف معلومات النسخة الاحتياطية
                string infoContent = $"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                                    $"DatabaseName: {GetDatabaseNameFromConnectionString(_dbContext.ConnectionString)}\n" +
                                    $"BackupType: FullSystem\n" +
                                    $"CreatedBy: Developer (ID: {_developerId})";
                File.WriteAllText(Path.Combine(tempBackupDir, "backup_info.txt"), infoContent);

                // ضغط المجلد المؤقت إلى ملف ZIP
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string zipFilePath = Path.Combine(_recoveryDirectory, $"FullSystemBackup_{timestamp}.zip");
                ZipFile.CreateFromDirectory(tempBackupDir, zipFilePath);

                // حذف المجلد المؤقت
                Directory.Delete(tempBackupDir, true);

                // نسخ النسخة الاحتياطية إلى المجلد السحابي (محاكاة)
                string cloudBackupPath = Path.Combine(_cloudBackupDirectory, Path.GetFileName(zipFilePath));
                File.Copy(zipFilePath, cloudBackupPath, true);

                // تسجيل العملية
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "إنشاء نسخة احتياطية كاملة للنظام",
                    $"تم إنشاء نسخة احتياطية كاملة للنظام في المسار: {zipFilePath}");

                LogMessage($"تم إنشاء نسخة احتياطية كاملة للنظام بنجاح في المسار: {zipFilePath}");
                return zipFilePath;
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء إنشاء نسخة احتياطية كاملة للنظام: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// استعادة النظام من نسخة احتياطية كاملة
        /// </summary>
        /// <param name="zipFilePath">مسار ملف النسخة الاحتياطية المضغوط</param>
        /// <returns>نتيجة عملية الاستعادة</returns>
        public async Task<RecoveryResult> RecoverFullSystemAsync(string zipFilePath)
        {
            RecoveryResult result = new RecoveryResult
            {
                StartTime = DateTime.Now,
                Success = false
            };

            try
            {
                LogMessage("بدء استعادة النظام من نسخة احتياطية كاملة...");

                if (!File.Exists(zipFilePath))
                {
                    throw new FileNotFoundException($"ملف النسخة الاحتياطية غير موجود: {zipFilePath}");
                }

                // إنشاء نسخة احتياطية قبل الاستعادة
                string preRecoveryBackupPath = await _maintenanceRepository.CreateBackupAsync();
                result.PreRecoveryBackupPath = preRecoveryBackupPath;

                // إنشاء مجلد مؤقت لاستخراج النسخة الاحتياطية
                string tempExtractDir = Path.Combine(Path.GetTempPath(), $"GoPOS_Restore_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(tempExtractDir);

                // استخراج ملف ZIP
                ZipFile.ExtractToDirectory(zipFilePath, tempExtractDir);

                // البحث عن ملف قاعدة البيانات
                string[] dbBackupFiles = Directory.GetFiles(tempExtractDir, "*.bak");
                if (dbBackupFiles.Length == 0)
                {
                    throw new FileNotFoundException("لم يتم العثور على ملف قاعدة البيانات في النسخة الاحتياطية");
                }

                // استعادة قاعدة البيانات
                string dbBackupPath = dbBackupFiles[0];
                await _maintenanceRepository.RestoreBackupAsync(dbBackupPath);

                // استعادة ملفات التطبيق المهمة
                string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                
                // استعادة ملفات الإعدادات
                string configSourceDir = Path.Combine(tempExtractDir, "Config");
                string configTargetDir = Path.Combine(appDirectory, "Config");
                if (Directory.Exists(configSourceDir))
                {
                    if (Directory.Exists(configTargetDir))
                    {
                        Directory.Delete(configTargetDir, true);
                    }
                    CopyDirectory(configSourceDir, configTargetDir);
                }

                // استعادة ملفات الموارد
                string resourcesSourceDir = Path.Combine(tempExtractDir, "Resources");
                string resourcesTargetDir = Path.Combine(appDirectory, "Resources");
                if (Directory.Exists(resourcesSourceDir))
                {
                    if (Directory.Exists(resourcesTargetDir))
                    {
                        Directory.Delete(resourcesTargetDir, true);
                    }
                    CopyDirectory(resourcesSourceDir, resourcesTargetDir);
                }

                // حذف المجلد المؤقت
                Directory.Delete(tempExtractDir, true);

                // تسجيل العملية
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "استعادة النظام من نسخة احتياطية كاملة",
                    $"تم استعادة النظام من النسخة الاحتياطية الكاملة: {zipFilePath}");

                result.EndTime = DateTime.Now;
                result.Duration = (result.EndTime - result.StartTime).TotalSeconds;
                result.Success = true;
                result.RecoveredBackupPath = zipFilePath;

                LogMessage($"تم استعادة النظام بنجاح من النسخة الاحتياطية الكاملة: {zipFilePath}");
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Duration = (result.EndTime - result.StartTime).TotalSeconds;
                result.ErrorMessage = ex.Message;

                LogMessage($"حدث خطأ أثناء استعادة النظام من النسخة الاحتياطية الكاملة: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// الحصول على أحدث ملف نسخة احتياطية
        /// </summary>
        private string GetLatestBackupFile()
        {
            try
            {
                // البحث في مجلد استعادة الطوارئ
                string[] backupFiles = Directory.GetFiles(_recoveryDirectory, "*.bak");
                if (backupFiles.Length > 0)
                {
                    // ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
                    return backupFiles.OrderByDescending(f => File.GetLastWriteTime(f)).First();
                }

                // البحث في مجلد النسخ الاحتياطي العادي
                string backupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                if (Directory.Exists(backupDirectory))
                {
                    backupFiles = Directory.GetFiles(backupDirectory, "*.bak");
                    if (backupFiles.Length > 0)
                    {
                        return backupFiles.OrderByDescending(f => File.GetLastWriteTime(f)).First();
                    }
                }

                // البحث في المجلد السحابي
                if (Directory.Exists(_cloudBackupDirectory))
                {
                    backupFiles = Directory.GetFiles(_cloudBackupDirectory, "*.bak");
                    if (backupFiles.Length > 0)
                    {
                        return backupFiles.OrderByDescending(f => File.GetLastWriteTime(f)).First();
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء البحث عن أحدث ملف نسخة احتياطية: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// نسخ مجلد بكل محتوياته
        /// </summary>
        private void CopyDirectory(string sourceDir, string targetDir)
        {
            Directory.CreateDirectory(targetDir);

            // نسخ الملفات
            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string fileName = Path.GetFileName(file);
                string targetFile = Path.Combine(targetDir, fileName);
                File.Copy(file, targetFile, true);
            }

            // نسخ المجلدات الفرعية
            foreach (string directory in Directory.GetDirectories(sourceDir))
            {
                string dirName = Path.GetFileName(directory);
                string targetSubDir = Path.Combine(targetDir, dirName);
                CopyDirectory(directory, targetSubDir);
            }
        }

        /// <summary>
        /// استخراج اسم قاعدة البيانات من سلسلة الاتصال
        /// </summary>
        private string GetDatabaseNameFromConnectionString(string connectionString)
        {
            try
            {
                System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder(connectionString);
                return builder.InitialCatalog;
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// تسجيل رسالة في ملف السجل
        /// </summary>
        private void LogMessage(string message)
        {
            try
            {
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
                File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
            }
            catch
            {
                // تجاهل أي خطأ أثناء تسجيل الرسالة
            }
        }
    }

    /// <summary>
    /// نتيجة عملية استعادة النظام
    /// </summary>
    public class RecoveryResult
    {
        /// <summary>
        /// وقت بدء الاستعادة
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// وقت انتهاء الاستعادة
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// مدة الاستعادة بالثواني
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// هل نجحت عملية الاستعادة
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// رسالة الخطأ (إذا فشلت عملية الاستعادة)
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// مسار النسخة الاحتياطية قبل الاستعادة
        /// </summary>
        public string PreRecoveryBackupPath { get; set; }

        /// <summary>
        /// مسار النسخة الاحتياطية التي تم استعادتها
        /// </summary>
        public string RecoveredBackupPath { get; set; }
    }
}
