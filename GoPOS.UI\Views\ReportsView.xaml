<UserControl x:Class="GoPOS.UI.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات -->
        <Border Grid.Row="0" Background="#673AB7" CornerRadius="8" Margin="0,0,0,20">
            <Grid Margin="15,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="التقارير" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="عرض وطباعة تقارير النظام" FontSize="14" Foreground="White" Margin="0,5,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="PrintReportButton" 
                            Width="120" 
                            Height="40" 
                            Margin="0,0,10,0" 
                            Click="PrintReportButton_Click"
                            Background="White"
                            Foreground="#673AB7"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🖨️" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="طباعة التقرير" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="ExportReportButton" 
                            Width="120" 
                            Height="40" 
                            Click="ExportReportButton_Click"
                            Background="White"
                            Foreground="#673AB7"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير التقرير" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- محتوى التقارير -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة التقارير -->
            <Border Grid.Column="0" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="أنواع التقارير" FontSize="16" FontWeight="SemiBold" Margin="10"/>

                    <ListBox x:Name="ReportsListBox" Grid.Row="1" BorderThickness="0" SelectionChanged="ReportsListBox_SelectionChanged">
                        <ListBox.Resources>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Padding" Value="10"/>
                                <Setter Property="Margin" Value="0,2"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#E8E3F4"/>
                                        <Setter Property="BorderBrush" Value="#673AB7"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </ListBox.Resources>
                        
                        <ListBoxItem Tag="SalesReport">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📈" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقرير المبيعات" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ListBoxItem>
                        
                        <ListBoxItem Tag="InventoryReport">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📦" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقرير المخزون" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ListBoxItem>
                        
                        <ListBoxItem Tag="CustomersReport">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="👥" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقرير العملاء" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ListBoxItem>
                        
                        <ListBoxItem Tag="InstallmentsReport">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💰" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقرير الأقساط" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ListBoxItem>
                        
                        <ListBoxItem Tag="ExpensesReport">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💸" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقرير المنصرفات" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ListBoxItem>
                        
                        <ListBoxItem Tag="ProfitReport">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💹" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقرير الأرباح" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ListBoxItem>
                        
                        <ListBoxItem Tag="TaxReport">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🧾" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Text="تقرير الضرائب" VerticalAlignment="Center"/>
                            </StackPanel>
                        </ListBoxItem>
                    </ListBox>
                </Grid>
            </Border>

            <!-- محتوى التقرير -->
            <Border Grid.Column="1" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان التقرير -->
                    <TextBlock x:Name="ReportTitleTextBlock" Grid.Row="0" Text="تقرير المبيعات" FontSize="18" FontWeight="Bold" Margin="15,15,15,5"/>

                    <!-- معايير التقرير -->
                    <Border Grid.Row="1" Background="#F5F5F5" Padding="15" Margin="15,5,15,10">
                        <Grid x:Name="ReportCriteriaGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- الفترة -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الفترة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox x:Name="PeriodComboBox" Grid.Row="0" Grid.Column="1" Margin="0,0,15,0" SelectedIndex="0" SelectionChanged="PeriodComboBox_SelectionChanged">
                                <ComboBoxItem Content="اليوم"/>
                                <ComboBoxItem Content="الأسبوع"/>
                                <ComboBoxItem Content="الشهر"/>
                                <ComboBoxItem Content="السنة"/>
                                <ComboBoxItem Content="فترة محددة"/>
                            </ComboBox>

                            <!-- من تاريخ -->
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <DatePicker x:Name="FromDatePicker" Grid.Row="0" Grid.Column="3" Margin="0,0,15,0" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                            <!-- إلى تاريخ -->
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,10,10,0"/>
                            <DatePicker x:Name="ToDatePicker" Grid.Row="1" Grid.Column="3" Margin="0,10,15,0" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                            <!-- زر تطبيق الفلتر -->
                            <Button x:Name="ApplyFilterButton" Grid.Row="0" Grid.Column="4" Grid.RowSpan="2" Content="تطبيق" Width="80" Height="35" Click="ApplyFilterButton_Click" Background="#673AB7" Foreground="White">
                                <Button.Resources>
                                    <Style TargetType="Border">
                                        <Setter Property="CornerRadius" Value="4"/>
                                    </Style>
                                </Button.Resources>
                            </Button>
                        </Grid>
                    </Border>

                    <!-- محتوى التقرير -->
                    <ContentControl x:Name="ReportContentControl" Grid.Row="2" Margin="15,0,15,15"/>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
