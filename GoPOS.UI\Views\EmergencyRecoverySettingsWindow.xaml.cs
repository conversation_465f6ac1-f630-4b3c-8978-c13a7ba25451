using System;
using System.IO;
using System.Windows;
using System.Windows.Forms;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for EmergencyRecoverySettingsWindow.xaml
    /// </summary>
    public partial class EmergencyRecoverySettingsWindow : Window
    {
        private EmergencyRecoverySettings _settings;
        private string _defaultEmergencyDirectory;
        private string _defaultCloudDirectory;

        /// <summary>
        /// إنشاء نافذة إعدادات استعادة الطوارئ
        /// </summary>
        /// <param name="settings">إعدادات استعادة الطوارئ الحالية</param>
        public EmergencyRecoverySettingsWindow(EmergencyRecoverySettings settings = null)
        {
            InitializeComponent();
            
            // تحديد المجلدات الافتراضية
            _defaultEmergencyDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmergencyRecovery");
            _defaultCloudDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CloudBackups");
            
            // استخدام الإعدادات المقدمة أو إنشاء إعدادات جديدة
            _settings = settings ?? new EmergencyRecoverySettings();
            
            // تعبئة الحقول بالإعدادات الحالية
            LoadSettings();
        }

        /// <summary>
        /// تعبئة الحقول بالإعدادات الحالية
        /// </summary>
        private void LoadSettings()
        {
            // تمكين النسخ الاحتياطي للطوارئ
            EnableEmergencyBackupCheckBox.IsChecked = _settings.EnableEmergencyBackup;
            
            // الفترة الزمنية للنسخ الاحتياطي للطوارئ
            foreach (System.Windows.Controls.ComboBoxItem item in EmergencyBackupIntervalComboBox.Items)
            {
                if (item.Content.ToString() == _settings.EmergencyBackupIntervalDays.ToString())
                {
                    EmergencyBackupIntervalComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // عدد نسخ الطوارئ
            foreach (System.Windows.Controls.ComboBoxItem item in EmergencyBackupCountComboBox.Items)
            {
                if (item.Content.ToString() == _settings.EmergencyBackupCount.ToString())
                {
                    EmergencyBackupCountComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // تمكين النسخ الاحتياطي السحابي
            EnableCloudBackupCheckBox.IsChecked = _settings.EnableCloudBackup;
            
            // تمكين النسخ الاحتياطي الكامل للنظام
            EnableFullSystemBackupCheckBox.IsChecked = _settings.EnableFullSystemBackup;
            
            // الفترة الزمنية للنسخ الاحتياطي الكامل للنظام
            foreach (System.Windows.Controls.ComboBoxItem item in FullSystemBackupIntervalComboBox.Items)
            {
                if (item.Content.ToString() == _settings.FullSystemBackupIntervalDays.ToString())
                {
                    FullSystemBackupIntervalComboBox.SelectedItem = item;
                    break;
                }
            }
            
            // وقت النسخ الاحتياطي الكامل للنظام
            FullSystemBackupTimeComboBox.SelectedIndex = _settings.FullSystemBackupTimeIndex;
            
            // مجلد الطوارئ
            EmergencyDirectoryTextBox.Text = string.IsNullOrEmpty(_settings.EmergencyDirectory) ? 
                _defaultEmergencyDirectory : _settings.EmergencyDirectory;
            
            // مجلد السحابة
            CloudDirectoryTextBox.Text = string.IsNullOrEmpty(_settings.CloudDirectory) ? 
                _defaultCloudDirectory : _settings.CloudDirectory;
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void SaveSettings()
        {
            // تمكين النسخ الاحتياطي للطوارئ
            _settings.EnableEmergencyBackup = EnableEmergencyBackupCheckBox.IsChecked ?? true;
            
            // الفترة الزمنية للنسخ الاحتياطي للطوارئ
            if (EmergencyBackupIntervalComboBox.SelectedItem != null)
            {
                _settings.EmergencyBackupIntervalDays = int.Parse(
                    ((System.Windows.Controls.ComboBoxItem)EmergencyBackupIntervalComboBox.SelectedItem).Content.ToString());
            }
            
            // عدد نسخ الطوارئ
            if (EmergencyBackupCountComboBox.SelectedItem != null)
            {
                _settings.EmergencyBackupCount = int.Parse(
                    ((System.Windows.Controls.ComboBoxItem)EmergencyBackupCountComboBox.SelectedItem).Content.ToString());
            }
            
            // تمكين النسخ الاحتياطي السحابي
            _settings.EnableCloudBackup = EnableCloudBackupCheckBox.IsChecked ?? false;
            
            // تمكين النسخ الاحتياطي الكامل للنظام
            _settings.EnableFullSystemBackup = EnableFullSystemBackupCheckBox.IsChecked ?? false;
            
            // الفترة الزمنية للنسخ الاحتياطي الكامل للنظام
            if (FullSystemBackupIntervalComboBox.SelectedItem != null)
            {
                _settings.FullSystemBackupIntervalDays = int.Parse(
                    ((System.Windows.Controls.ComboBoxItem)FullSystemBackupIntervalComboBox.SelectedItem).Content.ToString());
            }
            
            // وقت النسخ الاحتياطي الكامل للنظام
            _settings.FullSystemBackupTimeIndex = FullSystemBackupTimeComboBox.SelectedIndex;
            
            // مجلد الطوارئ
            _settings.EmergencyDirectory = EmergencyDirectoryTextBox.Text;
            
            // مجلد السحابة
            _settings.CloudDirectory = CloudDirectoryTextBox.Text;
            
            // التحقق من وجود المجلدات وإنشائها إذا لم تكن موجودة
            EnsureDirectoryExists(_settings.EmergencyDirectory);
            EnsureDirectoryExists(_settings.CloudDirectory);
        }

        /// <summary>
        /// التحقق من وجود المجلد وإنشائه إذا لم يكن موجودًا
        /// </summary>
        private void EnsureDirectoryExists(string directory)
        {
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                try
                {
                    Directory.CreateDirectory(directory);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show(
                        $"حدث خطأ أثناء إنشاء المجلد: {ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// معالجة حدث النقر على زر الحفظ
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ الإعدادات
                SaveSettings();
                
                // إغلاق النافذة
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة حدث النقر على زر الإلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// معالجة حدث النقر على زر استعراض مجلد الطوارئ
        /// </summary>
        private void BrowseEmergencyDirectoryButton_Click(object sender, RoutedEventArgs e)
        {
            BrowseForDirectory(EmergencyDirectoryTextBox, "اختر مجلد النسخ الاحتياطي للطوارئ", _defaultEmergencyDirectory);
        }

        /// <summary>
        /// معالجة حدث النقر على زر استعراض مجلد السحابة
        /// </summary>
        private void BrowseCloudDirectoryButton_Click(object sender, RoutedEventArgs e)
        {
            BrowseForDirectory(CloudDirectoryTextBox, "اختر مجلد النسخ الاحتياطي السحابي", _defaultCloudDirectory);
        }

        /// <summary>
        /// استعراض المجلدات
        /// </summary>
        private void BrowseForDirectory(System.Windows.Controls.TextBox textBox, string description, string defaultPath)
        {
            try
            {
                // إنشاء مربع حوار اختيار المجلد
                FolderBrowserDialog folderDialog = new FolderBrowserDialog();
                folderDialog.Description = description;
                folderDialog.ShowNewFolderButton = true;
                
                // تعيين المجلد الافتراضي
                if (!string.IsNullOrEmpty(textBox.Text) && Directory.Exists(textBox.Text))
                {
                    folderDialog.SelectedPath = textBox.Text;
                }
                else
                {
                    folderDialog.SelectedPath = defaultPath;
                }
                
                // عرض مربع الحوار
                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    textBox.Text = folderDialog.SelectedPath;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"حدث خطأ أثناء اختيار المجلد: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على إعدادات استعادة الطوارئ
        /// </summary>
        public EmergencyRecoverySettings GetSettings()
        {
            return _settings;
        }
    }

    /// <summary>
    /// إعدادات استعادة الطوارئ
    /// </summary>
    public class EmergencyRecoverySettings
    {
        /// <summary>
        /// تمكين النسخ الاحتياطي للطوارئ
        /// </summary>
        public bool EnableEmergencyBackup { get; set; } = true;

        /// <summary>
        /// الفترة الزمنية للنسخ الاحتياطي للطوارئ (بالأيام)
        /// </summary>
        public int EmergencyBackupIntervalDays { get; set; } = 7;

        /// <summary>
        /// عدد نسخ الطوارئ
        /// </summary>
        public int EmergencyBackupCount { get; set; } = 3;

        /// <summary>
        /// تمكين النسخ الاحتياطي السحابي
        /// </summary>
        public bool EnableCloudBackup { get; set; } = false;

        /// <summary>
        /// تمكين النسخ الاحتياطي الكامل للنظام
        /// </summary>
        public bool EnableFullSystemBackup { get; set; } = false;

        /// <summary>
        /// الفترة الزمنية للنسخ الاحتياطي الكامل للنظام (بالأيام)
        /// </summary>
        public int FullSystemBackupIntervalDays { get; set; } = 30;

        /// <summary>
        /// مؤشر وقت النسخ الاحتياطي الكامل للنظام
        /// </summary>
        public int FullSystemBackupTimeIndex { get; set; } = 0;

        /// <summary>
        /// مجلد الطوارئ
        /// </summary>
        public string EmergencyDirectory { get; set; } = "";

        /// <summary>
        /// مجلد السحابة
        /// </summary>
        public string CloudDirectory { get; set; } = "";
    }
}
