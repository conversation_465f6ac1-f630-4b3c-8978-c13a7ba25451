<Window x:Class="GoPOS.UI.Views.AboutWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="حول البرنامج - GoPOS" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- الشعار والعنوان -->
        <StackPanel Grid.Row="0" Margin="20" HorizontalAlignment="Center">
            <TextBlock Text="🏪" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
            <TextBlock Text="نظام نقاط البيع GoPOS"
                       FontSize="28" FontWeight="Bold"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="الإصدار 1.0.0"
                       FontSize="16"
                       HorizontalAlignment="Center"
                       Foreground="#666666"
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- معلومات البرنامج -->
        <ScrollViewer Grid.Row="1" Margin="40,20,40,20">
            <StackPanel>
                <!-- معلومات المطور -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🎨 تصميم وتطوير"
                                   FontSize="18" FontWeight="Bold"
                                   Foreground="#2196F3"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="GoLx"
                                   FontSize="24" FontWeight="Bold"
                                   Foreground="#1976D2"
                                   Margin="0,0,0,5"/>
                        <TextBlock Text="مطور ومصمم أنظمة متخصص في تطوير حلول نقاط البيع والإدارة"
                                   FontSize="14"
                                   Foreground="#666666"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- وصف البرنامج -->
                <Border Background="#E8F5E8" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📋 وصف البرنامج"
                                   FontSize="18" FontWeight="Bold"
                                   Foreground="#4CAF50"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="نظام نقاط البيع GoPOS هو حل متكامل وشامل لإدارة المبيعات والمخزون والعملاء. يوفر البرنامج واجهة سهلة الاستخدام مع ميزات متقدمة لتلبية احتياجات الأعمال التجارية المختلفة."
                                   FontSize="14"
                                   TextWrapping="Wrap"
                                   LineHeight="22"/>
                    </StackPanel>
                </Border>

                <!-- الميزات الرئيسية -->
                <Border Background="#FFF3E0" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="⭐ الميزات الرئيسية"
                                   FontSize="18" FontWeight="Bold"
                                   Foreground="#FF9800"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="• نظام نقاط بيع متكامل وسريع
• إدارة شاملة للمنتجات والمخزون
• نظام إدارة العملاء والموردين
• تقارير مفصلة وتحليلات متقدمة
• نظام الأقساط والدفع المرن
• نظام الصيانة والنسخ الاحتياطي
• واجهة مستخدم عصرية وسهلة الاستخدام
• دعم الباركود والطباعة
• نظام الصلاحيات والأمان"
                                   FontSize="14"
                                   TextWrapping="Wrap"
                                   LineHeight="22"/>
                    </StackPanel>
                </Border>

                <!-- التقنيات المستخدمة -->
                <Border Background="#E3F2FD" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🔧 التقنيات المستخدمة"
                                   FontSize="18" FontWeight="Bold"
                                   Foreground="#2196F3"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="• C# و .NET Framework
• WPF للواجهة الرسومية
• SQL Server لقاعدة البيانات
• Entity Framework للوصول للبيانات
• MVVM Pattern للهيكلة
• Material Design للتصميم"
                                   FontSize="14"
                                   TextWrapping="Wrap"
                                   LineHeight="22"/>
                    </StackPanel>
                </Border>

                <!-- معلومات الاتصال -->
                <Border Background="#F3E5F5" CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📞 معلومات الاتصال"
                                   FontSize="18" FontWeight="Bold"
                                   Foreground="#9C27B0"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="للدعم الفني والاستفسارات، يرجى التواصل مع فريق GoLx"
                                   FontSize="14"
                                   TextWrapping="Wrap"
                                   LineHeight="22"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20">
            <Button Content="موافق" Width="100" Height="35" 
                    Background="#2196F3" Foreground="White"
                    FontWeight="Bold" Click="OkButton_Click">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </StackPanel>
    </Grid>
</Window>
