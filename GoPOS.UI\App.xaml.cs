﻿using System;
using System.IO;
using System.Windows;
using System.Windows.Threading;
using GoPOS.UI.Views;
using GoPOS.Data.Models;

namespace GoPOS.UI;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    /// <summary>
    /// المستخدم الحالي المسجل دخوله في النظام
    /// </summary>
    public static User CurrentUser { get; set; }
    protected override void OnStartup(StartupEventArgs e)
    {
        // تسجيل معالج الاستثناءات غير المعالجة
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        DispatcherUnhandledException += App_DispatcherUnhandledException;

        base.OnStartup(e);

        try
        {
            // بدء التطبيق بنافذة تسجيل الدخول
            LoginWindow loginWindow = new LoginWindow();
            loginWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء بدء التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            LogError(ex);
            Shutdown(1);
        }
    }

    private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        try
        {
            e.Handled = true;
            MessageBox.Show($"حدث خطأ غير متوقع: {e.Exception.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            LogError(e.Exception);
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ، نقوم بإغلاق التطبيق
            Shutdown(1);
        }
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            Exception ex = e.ExceptionObject as Exception;
            MessageBox.Show($"حدث خطأ غير متوقع: {ex?.Message ?? "خطأ غير معروف"}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            LogError(ex);

            if (e.IsTerminating)
            {
                MessageBox.Show("سيتم إغلاق التطبيق بسبب خطأ خطير.", "إغلاق التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ، نقوم بإغلاق التطبيق
            Shutdown(1);
        }
    }

    private void LogError(Exception ex)
    {
        try
        {
            string logFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logFolder))
            {
                Directory.CreateDirectory(logFolder);
            }

            string logFile = Path.Combine(logFolder, $"Error_{DateTime.Now:yyyyMMdd}.log");
            string errorMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {ex?.GetType().Name}: {ex?.Message}\r\n{ex?.StackTrace}\r\n\r\n";

            File.AppendAllText(logFile, errorMessage);
        }
        catch
        {
            // تجاهل أي خطأ أثناء تسجيل الخطأ
        }
    }
}
