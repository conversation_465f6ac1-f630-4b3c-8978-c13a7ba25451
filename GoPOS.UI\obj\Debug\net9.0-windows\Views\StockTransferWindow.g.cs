﻿#pragma checksum "..\..\..\..\Views\StockTransferWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DC098990CF02136F9FFB413A83A3D949270DD6E8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// StockTransferWindow
    /// </summary>
    public partial class StockTransferWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 47 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProductComboBox;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScanBarcodeButton;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BarcodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SourceWarehouseComboBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SourceQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TargetWarehouseComboBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TargetQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TransferQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SourceFinalQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TargetFinalQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransferButton;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\StockTransferWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/stocktransferwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\StockTransferWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProductComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 47 "..\..\..\..\Views\StockTransferWindow.xaml"
            this.ProductComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProductComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ScanBarcodeButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\..\Views\StockTransferWindow.xaml"
            this.ScanBarcodeButton.Click += new System.Windows.RoutedEventHandler(this.ScanBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BarcodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.SourceWarehouseComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 57 "..\..\..\..\Views\StockTransferWindow.xaml"
            this.SourceWarehouseComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SourceWarehouseComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SourceQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TargetWarehouseComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 69 "..\..\..\..\Views\StockTransferWindow.xaml"
            this.TargetWarehouseComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TargetWarehouseComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TargetQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.TransferQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 81 "..\..\..\..\Views\StockTransferWindow.xaml"
            this.TransferQuantityTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TransferQuantityTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SourceFinalQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TargetFinalQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.TransferButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\Views\StockTransferWindow.xaml"
            this.TransferButton.Click += new System.Windows.RoutedEventHandler(this.TransferButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\..\Views\StockTransferWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

