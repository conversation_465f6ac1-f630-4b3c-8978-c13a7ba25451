using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for DashboardView.xaml
    /// </summary>
    public partial class DashboardView : UserControl
    {
        private ObservableCollection<TopProductViewModel> _topProducts;
        private ObservableCollection<TopCustomerViewModel> _topCustomers;
        private Random _random; // للبيانات التجريبية

        public DashboardView()
        {
            try
            {
                InitializeComponent();
                _random = new Random();

                // تهيئة البيانات الأولية
                _topProducts = new ObservableCollection<TopProductViewModel>();
                _topCustomers = new ObservableCollection<TopCustomerViewModel>();

                // إضافة معالج حدث Loaded
                this.Loaded += DashboardView_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة لوحة المعلومات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DashboardView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تأخير تحميل البيانات لضمان تحميل الواجهة بالكامل
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // إظهار مؤشر الانتظار
                        Cursor = System.Windows.Input.Cursors.Wait;

                        // تحميل البيانات
                        LoadDashboardData();

                        // إعادة المؤشر إلى الوضع الطبيعي
                        Cursor = System.Windows.Input.Cursors.Arrow;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"حدث خطأ أثناء تحميل بيانات لوحة المعلومات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        Cursor = System.Windows.Input.Cursors.Arrow;
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل لوحة المعلومات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDashboardData()
        {
            try
            {
                // التحقق من أن عناصر واجهة المستخدم موجودة
                if (TotalSalesTextBlock == null || OrderCountTextBlock == null ||
                    AverageOrderTextBlock == null || NewCustomersTextBlock == null ||
                    SalesChartCanvas == null || CategoryChartCanvas == null ||
                    TopProductsListView == null || TopCustomersListView == null)
                {
                    MessageBox.Show("لم يتم تهيئة عناصر واجهة المستخدم بشكل صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // تحميل البيانات من قاعدة البيانات
                LoadKPIs();

                // التأكد من أن Canvas له أبعاد صالحة قبل الرسم
                if (SalesChartCanvas.ActualWidth > 0 && SalesChartCanvas.ActualHeight > 0)
                {
                    LoadSalesChart();
                }
                else
                {
                    // إذا لم يكن Canvas جاهزًا، قم بتأخير الرسم
                    SalesChartCanvas.LayoutUpdated += (s, e) =>
                    {
                        if (SalesChartCanvas.ActualWidth > 0 && SalesChartCanvas.ActualHeight > 0)
                        {
                            LoadSalesChart();
                            // إزالة معالج الحدث بعد الاستخدام
                            SalesChartCanvas.LayoutUpdated -= (s2, e2) => { };
                        }
                    };
                }

                // التأكد من أن Canvas له أبعاد صالحة قبل الرسم
                if (CategoryChartCanvas.ActualWidth > 0 && CategoryChartCanvas.ActualHeight > 0)
                {
                    LoadCategoryChart();
                }
                else
                {
                    // إذا لم يكن Canvas جاهزًا، قم بتأخير الرسم
                    CategoryChartCanvas.LayoutUpdated += (s, e) =>
                    {
                        if (CategoryChartCanvas.ActualWidth > 0 && CategoryChartCanvas.ActualHeight > 0)
                        {
                            LoadCategoryChart();
                            // إزالة معالج الحدث بعد الاستخدام
                            CategoryChartCanvas.LayoutUpdated -= (s2, e2) => { };
                        }
                    };
                }

                LoadTopProducts();
                LoadTopCustomers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadKPIs()
        {
            // بيانات مؤشرات الأداء الرئيسية
            decimal totalSales = _random.Next(5000, 50000);
            int orderCount = _random.Next(50, 500);
            decimal averageOrder = totalSales / (orderCount > 0 ? orderCount : 1);
            int newCustomers = _random.Next(5, 50);

            // عرض البيانات
            TotalSalesTextBlock.Text = $"{totalSales:N2} ر.س";
            OrderCountTextBlock.Text = orderCount.ToString();
            AverageOrderTextBlock.Text = $"{averageOrder:N2} ر.س";
            NewCustomersTextBlock.Text = newCustomers.ToString();

            // نسب التغيير (بيانات تجريبية)
            double salesChange = _random.Next(-20, 50) / 10.0;
            double ordersChange = _random.Next(-20, 50) / 10.0;
            double averageChange = _random.Next(-20, 50) / 10.0;
            double customersChange = _random.Next(-20, 50) / 10.0;

            // عرض نسب التغيير مع اللون المناسب
            SalesChangeTextBlock.Text = $"{(salesChange >= 0 ? "+" : "")}{salesChange:N1}%";
            SalesChangeTextBlock.Foreground = salesChange >= 0 ? Brushes.White : Brushes.LightCoral;

            OrdersChangeTextBlock.Text = $"{(ordersChange >= 0 ? "+" : "")}{ordersChange:N1}%";
            OrdersChangeTextBlock.Foreground = ordersChange >= 0 ? Brushes.White : Brushes.LightCoral;

            AverageChangeTextBlock.Text = $"{(averageChange >= 0 ? "+" : "")}{averageChange:N1}%";
            AverageChangeTextBlock.Foreground = averageChange >= 0 ? Brushes.White : Brushes.LightCoral;

            CustomersChangeTextBlock.Text = $"{(customersChange >= 0 ? "+" : "")}{customersChange:N1}%";
            CustomersChangeTextBlock.Foreground = customersChange >= 0 ? Brushes.White : Brushes.LightCoral;
        }

        private void LoadSalesChart()
        {
            try
            {
                // التأكد من أن Canvas موجود
                if (SalesChartCanvas == null)
                {
                    return;
                }

                // مسح الرسم البياني السابق
                SalesChartCanvas.Children.Clear();

                // بيانات المبيعات
                double[] salesData = new double[7];
                for (int i = 0; i < salesData.Length; i++)
                {
                    salesData[i] = _random.Next(1000, 10000);
                }

                // رسم المحاور
                DrawAxes(SalesChartCanvas);

                // رسم البيانات
                DrawSalesData(SalesChartCanvas, salesData);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل رسم المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DrawAxes(Canvas canvas)
        {
            try
            {
                // التأكد من أن Canvas له أبعاد صالحة
                if (canvas == null)
                {
                    return;
                }

                double width = canvas.ActualWidth > 0 ? canvas.ActualWidth : 300;
                double height = canvas.ActualHeight > 0 ? canvas.ActualHeight : 200;

                // المحور الأفقي (X)
                Line xAxis = new Line
                {
                    X1 = 0,
                    Y1 = height - 20,
                    X2 = width,
                    Y2 = height - 20,
                    Stroke = Brushes.Gray,
                    StrokeThickness = 1
                };
                canvas.Children.Add(xAxis);

                // المحور الرأسي (Y)
                Line yAxis = new Line
                {
                    X1 = 30,
                    Y1 = 0,
                    X2 = 30,
                    Y2 = height - 20,
                    Stroke = Brushes.Gray,
                    StrokeThickness = 1
                };
                canvas.Children.Add(yAxis);

                // تسميات المحور الأفقي (أيام الأسبوع)
                string[] days = { "الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت" };
                double xStep = (width - 40) / (days.Length - 1);

                for (int i = 0; i < days.Length; i++)
                {
                    TextBlock dayLabel = new TextBlock
                    {
                        Text = days[i],
                        FontSize = 10,
                        TextAlignment = TextAlignment.Center,
                        Width = 50
                    };
                    Canvas.SetLeft(dayLabel, 30 + i * xStep - 25);
                    Canvas.SetTop(dayLabel, height - 15);
                    canvas.Children.Add(dayLabel);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رسم المحاور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DrawSalesData(Canvas canvas, double[] data)
        {
            try
            {
                // التأكد من أن Canvas له أبعاد صالحة
                if (canvas == null || data == null || data.Length == 0)
                {
                    return;
                }

                double width = canvas.ActualWidth > 0 ? canvas.ActualWidth : 300;
                double height = canvas.ActualHeight > 0 ? canvas.ActualHeight : 200;
                double chartHeight = height - 30;

                // العثور على القيمة القصوى للبيانات
                double maxValue = 0;
                foreach (double value in data)
                {
                    if (value > maxValue) maxValue = value;
                }

                // حساب المقياس
                double scale = maxValue > 0 ? chartHeight / maxValue : 1;
                double xStep = (width - 40) / (Math.Max(data.Length - 1, 1));

                // رسم خط المبيعات
                Polyline salesLine = new Polyline
                {
                    Stroke = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                    StrokeThickness = 2
                };

                PointCollection points = new PointCollection();

                for (int i = 0; i < data.Length; i++)
                {
                    double x = 30 + i * xStep;
                    double y = chartHeight - (data[i] * scale) + 10;
                    points.Add(new Point(x, y));

                    // إضافة نقطة على الخط
                    Ellipse point = new Ellipse
                    {
                        Width = 6,
                        Height = 6,
                        Fill = new SolidColorBrush(Color.FromRgb(33, 150, 243))
                    };
                    Canvas.SetLeft(point, x - 3);
                    Canvas.SetTop(point, y - 3);
                    canvas.Children.Add(point);

                    // إضافة قيمة المبيعات فوق النقطة
                    TextBlock valueLabel = new TextBlock
                    {
                        Text = $"{data[i]:N0}",
                        FontSize = 10,
                        TextAlignment = TextAlignment.Center,
                        Width = 50
                    };
                    Canvas.SetLeft(valueLabel, x - 25);
                    Canvas.SetTop(valueLabel, y - 20);
                    canvas.Children.Add(valueLabel);
                }

                salesLine.Points = points;
                canvas.Children.Add(salesLine);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رسم بيانات المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCategoryChart()
        {
            try
            {
                // التأكد من أن Canvas موجود
                if (CategoryChartCanvas == null)
                {
                    return;
                }

                // مسح الرسم البياني السابق
                CategoryChartCanvas.Children.Clear();

                // بيانات توزيع المبيعات حسب الفئة
                CategorySalesData[] categoryData = new CategorySalesData[]
                {
                    new CategorySalesData { Category = "مشروبات", Sales = _random.Next(1000, 5000), Color = Color.FromRgb(33, 150, 243) },
                    new CategorySalesData { Category = "وجبات سريعة", Sales = _random.Next(1000, 5000), Color = Color.FromRgb(76, 175, 80) },
                    new CategorySalesData { Category = "حلويات", Sales = _random.Next(1000, 5000), Color = Color.FromRgb(255, 152, 0) }
                };

                // رسم الرسم البياني الدائري
                DrawPieChart(CategoryChartCanvas, categoryData);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل رسم الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DrawPieChart(Canvas canvas, CategorySalesData[] data)
        {
            try
            {
                // التأكد من أن Canvas له أبعاد صالحة
                if (canvas == null || data == null || data.Length == 0)
                {
                    return;
                }

                double width = canvas.ActualWidth > 0 ? canvas.ActualWidth : 200;
                double height = canvas.ActualHeight > 0 ? canvas.ActualHeight : 200;

                // حساب إجمالي المبيعات
                double totalSales = 0;
                foreach (var category in data)
                {
                    totalSales += category.Sales;
                }

                // رسم الرسم البياني الدائري
                double radius = Math.Min(width, height) / 2 - 20;
                Point center = new Point(width / 2, height / 2);

                double startAngle = 0;

                for (int i = 0; i < data.Length; i++)
                {
                    double percentage = totalSales > 0 ? data[i].Sales / totalSales : 0;
                    double sweepAngle = percentage * 360;

                    // رسم قطاع الدائرة
                    DrawPieSlice(canvas, center, radius, startAngle, sweepAngle, new SolidColorBrush(data[i].Color));

                    // إضافة تسمية
                    double labelAngle = startAngle + (sweepAngle / 2);
                    double labelRadius = radius * 0.7;
                    Point labelPoint = new Point(
                        center.X + labelRadius * Math.Cos(labelAngle * Math.PI / 180),
                        center.Y + labelRadius * Math.Sin(labelAngle * Math.PI / 180)
                    );

                    TextBlock label = new TextBlock
                    {
                        Text = $"{data[i].Category}\n{percentage:P1}",
                        FontSize = 10,
                        TextAlignment = TextAlignment.Center,
                        Foreground = Brushes.White,
                        FontWeight = FontWeights.SemiBold
                    };
                    Canvas.SetLeft(label, labelPoint.X - 25);
                    Canvas.SetTop(label, labelPoint.Y - 10);
                    canvas.Children.Add(label);

                    startAngle += sweepAngle;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رسم الرسم البياني الدائري: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DrawPieSlice(Canvas canvas, Point center, double radius, double startAngle, double sweepAngle, Brush fill)
        {
            try
            {
                // التأكد من أن Canvas موجود
                if (canvas == null)
                {
                    return;
                }

                double startRadians = startAngle * Math.PI / 180;
                double endRadians = (startAngle + sweepAngle) * Math.PI / 180;

                Point startPoint = new Point(
                    center.X + radius * Math.Cos(startRadians),
                    center.Y + radius * Math.Sin(startRadians)
                );

                Point endPoint = new Point(
                    center.X + radius * Math.Cos(endRadians),
                    center.Y + radius * Math.Sin(endRadians)
                );

                Path path = new Path
                {
                    Fill = fill,
                    Stroke = Brushes.White,
                    StrokeThickness = 1
                };

                PathFigure figure = new PathFigure
                {
                    StartPoint = center,
                    IsClosed = true
                };

                figure.Segments.Add(new LineSegment(startPoint, true));

                figure.Segments.Add(new ArcSegment
                {
                    Point = endPoint,
                    Size = new Size(radius, radius),
                    SweepDirection = SweepDirection.Clockwise,
                    IsLargeArc = sweepAngle > 180
                });

                PathGeometry geometry = new PathGeometry();
                geometry.Figures.Add(figure);

                path.Data = geometry;
                canvas.Children.Add(path);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رسم قطاع الدائرة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadTopProducts()
        {
            try
            {
                // التأكد من أن ListView موجود
                if (TopProductsListView == null)
                {
                    return;
                }

                // بيانات أفضل المنتجات مبيعاً
                _topProducts.Clear();
                _topProducts.Add(new TopProductViewModel { Name = "قهوة", Sales = _random.Next(50, 500) });
                _topProducts.Add(new TopProductViewModel { Name = "شاي", Sales = _random.Next(50, 500) });
                _topProducts.Add(new TopProductViewModel { Name = "عصير برتقال", Sales = _random.Next(50, 500) });
                _topProducts.Add(new TopProductViewModel { Name = "ساندويتش", Sales = _random.Next(50, 500) });
                _topProducts.Add(new TopProductViewModel { Name = "بيتزا", Sales = _random.Next(50, 500) });

                // ترتيب المنتجات تنازلياً حسب المبيعات
                var sortedProducts = new ObservableCollection<TopProductViewModel>(
                    _topProducts.OrderByDescending(p => p.Sales)
                );

                // تعيين مصدر البيانات
                TopProductsListView.ItemsSource = sortedProducts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل أفضل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadTopCustomers()
        {
            try
            {
                // التأكد من أن ListView موجود
                if (TopCustomersListView == null)
                {
                    return;
                }

                // بيانات تجريبية لأفضل العملاء
                _topCustomers.Clear();
                _topCustomers.Add(new TopCustomerViewModel { Name = "أحمد محمد", Total = _random.Next(1000, 10000) });
                _topCustomers.Add(new TopCustomerViewModel { Name = "سارة أحمد", Total = _random.Next(1000, 10000) });
                _topCustomers.Add(new TopCustomerViewModel { Name = "محمد علي", Total = _random.Next(1000, 10000) });
                _topCustomers.Add(new TopCustomerViewModel { Name = "فاطمة خالد", Total = _random.Next(1000, 10000) });
                _topCustomers.Add(new TopCustomerViewModel { Name = "خالد عبدالله", Total = _random.Next(1000, 10000) });

                // ترتيب العملاء تنازلياً حسب إجمالي المشتريات
                var sortedCustomers = new ObservableCollection<TopCustomerViewModel>(
                    _topCustomers.OrderByDescending(c => c.Total)
                );

                // تعيين مصدر البيانات
                TopCustomersListView.ItemsSource = sortedCustomers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل أفضل العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تجنب تحميل البيانات عند التهيئة الأولية
                if (IsLoaded)
                {
                    // إظهار مؤشر الانتظار
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // إعادة تحميل البيانات عند تغيير الفترة
                    LoadDashboardData();

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تغيير الفترة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل زر التحديث لمنع النقر المتكرر
                RefreshButton.IsEnabled = false;

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // تحديث البيانات
                LoadDashboardData();

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;

                // إعادة تمكين زر التحديث
                RefreshButton.IsEnabled = true;

                MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
                RefreshButton.IsEnabled = true;
            }
        }
    }

    // فئات مساعدة
    public class TopProductViewModel
    {
        public string Name { get; set; }
        public int Sales { get; set; }
    }

    public class TopCustomerViewModel
    {
        public string Name { get; set; }
        public decimal Total { get; set; }
    }

    public class CategorySalesData
    {
        public string Category { get; set; }
        public double Sales { get; set; }
        public Color Color { get; set; }
    }
}
