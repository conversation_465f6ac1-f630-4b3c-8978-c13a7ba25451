<Window x:Class="GoPOS.UI.Views.MaintenanceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="وضع الصيانة - GoPOS"
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        Background="#F5F5F5"
        Icon="/GoPOS.UI;component/Resources/Images/logo.png">

    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#BDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="TabItem">
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#BBDEFB"/>
        </Style>

        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="BorderBrush" Value="#BDBDBD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#1565C0" Padding="15">
            <StackPanel Orientation="Horizontal">
                <Image Source="/GoPOS.UI;component/Resources/Images/maintenance.png" Width="32" Height="32" Margin="0,0,10,0"/>
                <TextBlock Text="وضع الصيانة - GoPOS" FontSize="20" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- علامة تبويب النسخ الاحتياطي -->
            <TabItem Header="النسخ الاحتياطي واستعادة البيانات">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- أزرار النسخ الاحتياطي -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <Button x:Name="CreateBackupButton" Content="إنشاء نسخة احتياطية جديدة" Click="CreateBackupButton_Click" Width="200"/>
                        <Button x:Name="RestoreBackupButton" Content="استعادة نسخة احتياطية" Click="RestoreBackupButton_Click" Width="200"/>
                        <Button x:Name="AutoBackupSettingsButton" Content="إعدادات النسخ التلقائي" Click="AutoBackupSettingsButton_Click" Width="200"/>
                    </StackPanel>

                    <!-- قائمة النسخ الاحتياطية -->
                    <GroupBox Grid.Row="1" Header="النسخ الاحتياطية المتاحة">
                        <ListView x:Name="BackupsListView" SelectionMode="Single" Margin="5">
                            <ListView.View>
                                <GridView>
                                    <GridViewColumn Header="اسم الملف" DisplayMemberBinding="{Binding FileName}" Width="250"/>
                                    <GridViewColumn Header="تاريخ الإنشاء" DisplayMemberBinding="{Binding CreationDate}" Width="150"/>
                                    <GridViewColumn Header="حجم الملف" DisplayMemberBinding="{Binding FileSize}" Width="100"/>
                                    <GridViewColumn Header="النوع" DisplayMemberBinding="{Binding BackupType}" Width="100"/>
                                    <GridViewColumn Header="المسار الكامل" DisplayMemberBinding="{Binding FullPath}" Width="250"/>
                                </GridView>
                            </ListView.View>
                            <ListView.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="استعادة" Click="RestoreBackupMenuItem_Click"/>
                                    <MenuItem Header="حذف" Click="DeleteBackupMenuItem_Click"/>
                                    <MenuItem Header="نسخ إلى مجلد" Click="CopyBackupMenuItem_Click"/>
                                    <MenuItem Header="عرض معلومات" Click="ViewBackupInfoMenuItem_Click"/>
                                </ContextMenu>
                            </ListView.ContextMenu>
                        </ListView>
                    </GroupBox>

                    <!-- حالة العملية -->
                    <TextBlock Grid.Row="2" x:Name="BackupStatusTextBlock" Margin="10" TextWrapping="Wrap" FontWeight="SemiBold"/>
                </Grid>
            </TabItem>

            <!-- علامة تبويب التشخيص وإصلاح قاعدة البيانات -->
            <TabItem Header="التشخيص وإصلاح قاعدة البيانات">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- معلومات تحذيرية -->
                    <TextBlock Grid.Row="0" Text="تحذير: عملية إصلاح قاعدة البيانات قد تؤدي إلى فقدان بعض البيانات في حالة وجود تلف شديد. يرجى إنشاء نسخة احتياطية قبل المتابعة."
                               Foreground="Red" TextWrapping="Wrap" Margin="10" FontWeight="Bold"/>

                    <!-- خيارات التشخيص والإصلاح -->
                    <GroupBox Grid.Row="1" Header="خيارات التشخيص والإصلاح">
                        <StackPanel>
                            <RadioButton x:Name="DiagnoseOnlyRadioButton" Content="تشخيص المشاكل فقط (بدون إصلاح)" IsChecked="True" Margin="5"/>
                            <RadioButton x:Name="DiagnoseAndFixRadioButton" Content="تشخيص وإصلاح المشاكل تلقائيًا" Margin="5"/>
                            <RadioButton x:Name="RepairDatabaseRadioButton" Content="إصلاح قاعدة البيانات (مع السماح بفقدان البيانات إذا لزم الأمر)" Margin="5"/>
                            <CheckBox x:Name="ScheduleDiagnosticCheckBox" Content="جدولة التشخيص التلقائي" Margin="5"/>
                            <StackPanel Orientation="Horizontal" Margin="25,5,5,5" IsEnabled="{Binding ElementName=ScheduleDiagnosticCheckBox, Path=IsChecked}">
                                <TextBlock Text="كل" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <ComboBox x:Name="DiagnosticIntervalComboBox" Width="50">
                                    <ComboBoxItem Content="1"/>
                                    <ComboBoxItem Content="2"/>
                                    <ComboBoxItem Content="3" IsSelected="True"/>
                                    <ComboBoxItem Content="7"/>
                                    <ComboBoxItem Content="14"/>
                                    <ComboBoxItem Content="30"/>
                                </ComboBox>
                                <TextBlock Text="يوم" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                            <Button x:Name="StartDiagnosticButton" Content="بدء التشخيص" Click="StartDiagnosticButton_Click" Width="200" Margin="0,20,0,0" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- نتائج التشخيص -->
                    <GroupBox Grid.Row="2" Header="نتائج التشخيص">
                        <DataGrid x:Name="DiagnosticResultsDataGrid" AutoGenerateColumns="False" IsReadOnly="True" Margin="5">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="نوع المشكلة" Binding="{Binding IssueType}" Width="150"/>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                                <DataGridTextColumn Header="الخطورة" Binding="{Binding Severity}" Width="100"/>
                                <DataGridCheckBoxColumn Header="تم الإصلاح" Binding="{Binding Fixed}" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </GroupBox>

                    <!-- حالة العملية -->
                    <TextBlock Grid.Row="3" x:Name="DiagnosticStatusTextBlock" Margin="10" TextWrapping="Wrap" FontWeight="SemiBold"/>
                </Grid>
            </TabItem>

            <!-- علامة تبويب الاستعلامات المباشرة -->
            <TabItem Header="الاستعلامات المباشرة">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- معلومات تحذيرية -->
                    <TextBlock Grid.Row="0" Text="تحذير: استخدام الاستعلامات المباشرة قد يؤدي إلى تلف قاعدة البيانات. هذه الميزة متاحة فقط للمطورين المعتمدين."
                               Foreground="Red" TextWrapping="Wrap" Margin="10" FontWeight="Bold"/>

                    <!-- محرر الاستعلام -->
                    <GroupBox Grid.Row="1" Header="محرر الاستعلام">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox Grid.Row="0" x:Name="QueryTextBox" AcceptsReturn="True" TextWrapping="Wrap"
                                     FontFamily="Consolas" FontSize="14" MinHeight="100" Margin="5"/>

                            <Button Grid.Row="1" x:Name="ExecuteQueryButton" Content="تنفيذ الاستعلام"
                                    Click="ExecuteQueryButton_Click" Width="200" Margin="0,10,0,0" HorizontalAlignment="Center"/>
                        </Grid>
                    </GroupBox>

                    <!-- نتائج الاستعلام -->
                    <GroupBox Grid.Row="2" Header="نتائج الاستعلام">
                        <DataGrid x:Name="QueryResultsDataGrid" AutoGenerateColumns="True" IsReadOnly="True" Margin="5"/>
                    </GroupBox>

                    <!-- حالة العملية -->
                    <TextBlock Grid.Row="3" x:Name="QueryStatusTextBlock" Margin="10" TextWrapping="Wrap" FontWeight="SemiBold"/>
                </Grid>
            </TabItem>

            <!-- علامة تبويب مراقبة الأداء -->
            <TabItem Header="مراقبة الأداء">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- أزرار التحكم -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <Button x:Name="StartMonitoringButton" Content="بدء المراقبة" Click="StartMonitoringButton_Click" Width="150"/>
                        <Button x:Name="StopMonitoringButton" Content="إيقاف المراقبة" Click="StopMonitoringButton_Click" Width="150" IsEnabled="False"/>
                        <Button x:Name="MonitoringSettingsButton" Content="إعدادات المراقبة" Click="MonitoringSettingsButton_Click" Width="150"/>
                    </StackPanel>

                    <!-- بيانات الأداء -->
                    <TabControl Grid.Row="1">
                        <TabItem Header="مؤشرات الأداء الحالية">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- وحدة المعالجة المركزية -->
                                <GroupBox Grid.Row="0" Grid.Column="0" Header="استخدام وحدة المعالجة المركزية" Margin="5">
                                    <StackPanel>
                                        <TextBlock x:Name="CpuUsageTextBlock" Text="0%" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                        <ProgressBar x:Name="CpuUsageProgressBar" Height="20" Margin="10" Maximum="100" Value="0"/>
                                    </StackPanel>
                                </GroupBox>

                                <!-- الذاكرة -->
                                <GroupBox Grid.Row="0" Grid.Column="1" Header="استخدام الذاكرة" Margin="5">
                                    <StackPanel>
                                        <TextBlock x:Name="MemoryUsageTextBlock" Text="0 ميجابايت" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                        <ProgressBar x:Name="MemoryUsageProgressBar" Height="20" Margin="10" Maximum="100" Value="0"/>
                                    </StackPanel>
                                </GroupBox>

                                <!-- القرص -->
                                <GroupBox Grid.Row="1" Grid.Column="0" Header="استخدام القرص" Margin="5">
                                    <StackPanel>
                                        <TextBlock x:Name="DiskUsageTextBlock" Text="0%" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                        <ProgressBar x:Name="DiskUsageProgressBar" Height="20" Margin="10" Maximum="100" Value="0"/>
                                    </StackPanel>
                                </GroupBox>

                                <!-- قاعدة البيانات -->
                                <GroupBox Grid.Row="1" Grid.Column="1" Header="معلومات قاعدة البيانات" Margin="5">
                                    <StackPanel>
                                        <TextBlock x:Name="DatabaseSizeTextBlock" Text="0 ميجابايت" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                        <TextBlock x:Name="DatabaseConnectionsTextBlock" Text="0 اتصال" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                    </StackPanel>
                                </GroupBox>
                            </Grid>
                        </TabItem>

                        <TabItem Header="تنبيهات الأداء">
                            <DataGrid x:Name="PerformanceAlertsDataGrid" AutoGenerateColumns="False" IsReadOnly="True" Margin="5">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="التاريخ والوقت" Binding="{Binding Timestamp}" Width="150"/>
                                    <DataGridTextColumn Header="نوع التنبيه" Binding="{Binding AlertType}" Width="150"/>
                                    <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                                    <DataGridTextColumn Header="الخطورة" Binding="{Binding Severity}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>

                        <TabItem Header="سجل الأداء">
                            <DataGrid x:Name="PerformanceLogsDataGrid" AutoGenerateColumns="False" IsReadOnly="True" Margin="5">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="التاريخ والوقت" Binding="{Binding Timestamp}" Width="150"/>
                                    <DataGridTextColumn Header="وحدة المعالجة المركزية" Binding="{Binding CpuUsage}" Width="120"/>
                                    <DataGridTextColumn Header="الذاكرة المتاحة" Binding="{Binding AvailableMemory}" Width="120"/>
                                    <DataGridTextColumn Header="استخدام القرص" Binding="{Binding DiskUsage}" Width="120"/>
                                    <DataGridTextColumn Header="حجم قاعدة البيانات" Binding="{Binding DatabaseSize}" Width="120"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                    </TabControl>

                    <!-- حالة المراقبة -->
                    <TextBlock Grid.Row="2" x:Name="MonitoringStatusTextBlock" Margin="10" TextWrapping="Wrap" FontWeight="SemiBold"/>
                </Grid>
            </TabItem>

            <!-- علامة تبويب استعادة الطوارئ -->
            <TabItem Header="استعادة الطوارئ">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- أزرار التحكم -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <Button x:Name="CreateEmergencyBackupButton" Content="إنشاء نسخة طوارئ" Click="CreateEmergencyBackupButton_Click" Width="200"/>
                        <Button x:Name="CreateFullSystemBackupButton" Content="نسخة احتياطية كاملة للنظام" Click="CreateFullSystemBackupButton_Click" Width="200"/>
                        <Button x:Name="RecoverSystemButton" Content="استعادة النظام" Click="RecoverSystemButton_Click" Width="200"/>
                    </StackPanel>

                    <!-- معلومات استعادة الطوارئ -->
                    <GroupBox Grid.Row="1" Header="معلومات استعادة الطوارئ">
                        <StackPanel>
                            <TextBlock Text="نظام استعادة الطوارئ يسمح باستعادة النظام بسرعة في حالة حدوث مشاكل خطيرة. يمكنك إنشاء نسخة احتياطية للطوارئ يتم تخزينها في مواقع متعددة لضمان توفرها عند الحاجة."
                                       TextWrapping="Wrap" Margin="10"/>

                            <GroupBox Header="حالة النسخ الاحتياطي للطوارئ" Margin="10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="آخر نسخة طوارئ:" FontWeight="Bold" Margin="5"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="LastEmergencyBackupTextBlock" Text="لا يوجد" Margin="5"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="آخر نسخة كاملة للنظام:" FontWeight="Bold" Margin="5"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="LastFullSystemBackupTextBlock" Text="لا يوجد" Margin="5"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="حالة النسخ السحابي:" FontWeight="Bold" Margin="5"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="CloudBackupStatusTextBlock" Text="غير متصل" Margin="5"/>

                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="عدد النسخ المتاحة:" FontWeight="Bold" Margin="5"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" x:Name="AvailableBackupsCountTextBlock" Text="0" Margin="5"/>
                                </Grid>
                            </GroupBox>
                        </StackPanel>
                    </GroupBox>

                    <!-- حالة العملية -->
                    <TextBlock Grid.Row="2" x:Name="EmergencyRecoveryStatusTextBlock" Margin="10" TextWrapping="Wrap" FontWeight="SemiBold"/>
                </Grid>
            </TabItem>

            <!-- علامة تبويب سجل العمليات -->
            <TabItem Header="سجل العمليات">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- أزرار التحكم -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <Button x:Name="RefreshLogsButton" Content="تحديث السجل" Click="RefreshLogsButton_Click" Width="150"/>
                        <Button x:Name="ExportLogsButton" Content="تصدير السجل" Click="ExportLogsButton_Click" Width="150"/>
                        <Button x:Name="FilterLogsButton" Content="تصفية السجل" Click="FilterLogsButton_Click" Width="150"/>
                    </StackPanel>

                    <!-- جدول السجلات -->
                    <DataGrid Grid.Row="1" x:Name="LogsDataGrid" AutoGenerateColumns="False" IsReadOnly="True" Margin="5">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم العملية" Binding="{Binding Id}" Width="80"/>
                            <DataGridTextColumn Header="المستخدم" Binding="{Binding UserName}" Width="120"/>
                            <DataGridTextColumn Header="نوع العملية" Binding="{Binding OperationType}" Width="150"/>
                            <DataGridTextColumn Header="التاريخ والوقت" Binding="{Binding OperationDate}" Width="150"/>
                            <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- شريط الحالة -->
        <StatusBar Grid.Row="2" Background="#E0E0E0">
            <StatusBarItem>
                <TextBlock x:Name="StatusTextBlock" Text="جاهز" Margin="10,0"/>
            </StatusBarItem>
        </StatusBar>

        <!-- مؤشر التحميل -->
        <Grid x:Name="LoadingIndicator" Grid.Row="0" Grid.RowSpan="3" Background="#80000000" Visibility="Collapsed">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="جاري التنفيذ..." Foreground="White" FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="20"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
