using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for StockTransferWindow.xaml
    /// </summary>
    public partial class StockTransferWindow : Window
    {
        private InventoryItemViewModel _selectedProduct;
        private List<TransferProductViewModel> _products;
        private int _sourceQuantity = 0;
        private int _targetQuantity = 0;
        private int _transferQuantity = 0;
        private int _sourceFinalQuantity = 0;
        private int _targetFinalQuantity = 0;

        public StockTransferWindow(InventoryItemViewModel? selectedProduct = null)
        {
            InitializeComponent();
            _selectedProduct = selectedProduct;

            LoadProducts();

            // إذا تم تمرير منتج محدد، قم بتحديده تلقائياً
            if (_selectedProduct != null)
            {
                foreach (var product in _products)
                {
                    if (product.Id == _selectedProduct.Id)
                    {
                        ProductComboBox.SelectedItem = product;

                        // تحديد المخزن المصدر بناءً على المنتج المحدد
                        for (int i = 0; i < SourceWarehouseComboBox.Items.Count; i++)
                        {
                            ComboBoxItem item = SourceWarehouseComboBox.Items[i] as ComboBoxItem;
                            if (item.Content.ToString() == _selectedProduct.WarehouseName)
                            {
                                SourceWarehouseComboBox.SelectedIndex = i;
                                break;
                            }
                        }

                        break;
                    }
                }
            }
        }

        private void LoadProducts()
        {
            // تحميل قائمة المنتجات (سيتم استبدالها بقراءة من قاعدة البيانات)
            _products = new List<TransferProductViewModel>();
            Random random = new Random();

            // بيانات تجريبية
            for (int i = 1; i <= 20; i++)
            {
                int categoryIndex = random.Next(0, 3);
                string categoryName = categoryIndex == 0 ? "مشروبات" : (categoryIndex == 1 ? "وجبات سريعة" : "حلويات");

                string productName = GetRandomProductName(categoryIndex);

                _products.Add(new TransferProductViewModel
                {
                    Id = i,
                    Barcode = $"100{i:D4}",
                    Name = productName,
                    CategoryName = categoryName,
                    Quantity = random.Next(10, 100)
                });
            }

            ProductComboBox.ItemsSource = _products;
        }

        private string GetRandomProductName(int categoryIndex)
        {
            string[] drinks = { "قهوة", "شاي", "عصير برتقال", "عصير تفاح", "ماء معدني", "مشروب غازي", "عصير مانجو" };
            string[] fastFood = { "برجر", "بيتزا", "ساندويتش", "دجاج مقلي", "بطاطس", "ناجتس", "شاورما" };
            string[] desserts = { "كيك", "آيس كريم", "كوكيز", "دونات", "تشيز كيك", "بودينج", "كنافة" };

            Random random = new Random();

            if (categoryIndex == 0)
                return drinks[random.Next(drinks.Length)];
            else if (categoryIndex == 1)
                return fastFood[random.Next(fastFood.Length)];
            else
                return desserts[random.Next(desserts.Length)];
        }

        private void ProductComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            TransferProductViewModel selectedProduct = ProductComboBox.SelectedItem as TransferProductViewModel;
            if (selectedProduct != null)
            {
                BarcodeTextBox.Text = selectedProduct.Barcode;
                UpdateWarehouseQuantities();
            }
        }

        private void SourceWarehouseComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // التأكد من أن المخزن المصدر والهدف مختلفان
            if (SourceWarehouseComboBox.SelectedIndex == TargetWarehouseComboBox.SelectedIndex)
            {
                // اختيار مخزن هدف مختلف
                for (int i = 0; i < TargetWarehouseComboBox.Items.Count; i++)
                {
                    if (i != SourceWarehouseComboBox.SelectedIndex)
                    {
                        TargetWarehouseComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            UpdateWarehouseQuantities();
        }

        private void TargetWarehouseComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // التأكد من أن المخزن المصدر والهدف مختلفان
            if (TargetWarehouseComboBox.SelectedIndex == SourceWarehouseComboBox.SelectedIndex)
            {
                // اختيار مخزن مصدر مختلف
                for (int i = 0; i < SourceWarehouseComboBox.Items.Count; i++)
                {
                    if (i != TargetWarehouseComboBox.SelectedIndex)
                    {
                        SourceWarehouseComboBox.SelectedIndex = i;
                        break;
                    }
                }
            }

            UpdateWarehouseQuantities();
        }

        private void UpdateWarehouseQuantities()
        {
            if (ProductComboBox.SelectedItem == null)
                return;

            // محاكاة الحصول على كميات المخزون في المخازن المختلفة
            Random random = new Random();

            // الكمية في المخزن المصدر
            _sourceQuantity = _selectedProduct != null ? _selectedProduct.Quantity : random.Next(10, 100);
            SourceQuantityTextBox.Text = _sourceQuantity.ToString();

            // الكمية في المخزن الهدف
            _targetQuantity = random.Next(0, 50);
            TargetQuantityTextBox.Text = _targetQuantity.ToString();

            // إعادة تعيين الكمية المنقولة
            TransferQuantityTextBox.Text = "0";
            UpdateFinalQuantities();
        }

        private void TransferQuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateFinalQuantities();
        }

        private void UpdateFinalQuantities()
        {
            if (int.TryParse(TransferQuantityTextBox.Text, out _transferQuantity))
            {
                if (_transferQuantity > _sourceQuantity)
                {
                    _transferQuantity = _sourceQuantity;
                    TransferQuantityTextBox.Text = _transferQuantity.ToString();
                }

                _sourceFinalQuantity = _sourceQuantity - _transferQuantity;
                _targetFinalQuantity = _targetQuantity + _transferQuantity;

                SourceFinalQuantityTextBox.Text = _sourceFinalQuantity.ToString();
                TargetFinalQuantityTextBox.Text = _targetFinalQuantity.ToString();
            }
            else
            {
                SourceFinalQuantityTextBox.Text = _sourceQuantity.ToString();
                TargetFinalQuantityTextBox.Text = _targetQuantity.ToString();
            }
        }

        private void ScanBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            // محاكاة مسح الباركود
            Random random = new Random();
            int productIndex = random.Next(0, _products.Count);
            ProductComboBox.SelectedIndex = productIndex;
        }

        private void TransferButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة الإدخال
            if (ProductComboBox.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار منتج", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                ProductComboBox.Focus();
                return;
            }

            if (!int.TryParse(TransferQuantityTextBox.Text, out int transferQuantity) || transferQuantity <= 0)
            {
                MessageBox.Show("الرجاء إدخال كمية نقل صحيحة أكبر من صفر", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                TransferQuantityTextBox.Focus();
                return;
            }

            if (transferQuantity > _sourceQuantity)
            {
                MessageBox.Show("كمية النقل أكبر من الكمية المتاحة في المخزن المصدر", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                TransferQuantityTextBox.Focus();
                return;
            }

            // حفظ نقل المخزون (سيتم استبداله بالحفظ في قاعدة البيانات)
            string sourceWarehouse = ((ComboBoxItem)SourceWarehouseComboBox.SelectedItem).Content.ToString();
            string targetWarehouse = ((ComboBoxItem)TargetWarehouseComboBox.SelectedItem).Content.ToString();
            string productName = ((TransferProductViewModel)ProductComboBox.SelectedItem).Name;

            MessageBox.Show($"تم نقل {transferQuantity} وحدة من {productName} من {sourceWarehouse} إلى {targetWarehouse} بنجاح", "تم النقل", MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // فئة عرض بيانات المنتج للنقل
    internal class TransferProductViewModel
    {
        public int Id { get; set; }
        public required string Barcode { get; set; }
        public required string Name { get; set; }
        public required string CategoryName { get; set; }
        public int Quantity { get; set; }
    }
}
