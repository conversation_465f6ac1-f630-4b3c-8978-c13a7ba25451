<Window x:Class="GoPOS.UI.Views.UserEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إدارة المستخدم" 
        Height="550" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock x:Name="TitleTextBlock" Grid.Row="0" Text="إضافة مستخدم جديد" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- نموذج البيانات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- اسم المستخدم -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="اسم المستخدم:" Margin="0,0,0,5"/>
                    <TextBox x:Name="UsernameTextBox" Height="30"/>
                </StackPanel>
                
                <!-- كلمة المرور -->
                <StackPanel x:Name="PasswordPanel" Margin="0,0,0,15">
                    <TextBlock Text="كلمة المرور:" Margin="0,0,0,5"/>
                    <PasswordBox x:Name="PasswordBox" Height="30"/>
                </StackPanel>
                
                <!-- تأكيد كلمة المرور -->
                <StackPanel x:Name="ConfirmPasswordPanel" Margin="0,0,0,15">
                    <TextBlock Text="تأكيد كلمة المرور:" Margin="0,0,0,5"/>
                    <PasswordBox x:Name="ConfirmPasswordBox" Height="30"/>
                </StackPanel>
                
                <!-- الاسم الكامل -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="الاسم الكامل:" Margin="0,0,0,5"/>
                    <TextBox x:Name="FullNameTextBox" Height="30"/>
                </StackPanel>
                
                <!-- البريد الإلكتروني -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="البريد الإلكتروني:" Margin="0,0,0,5"/>
                    <TextBox x:Name="EmailTextBox" Height="30"/>
                </StackPanel>
                
                <!-- الدور -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="الدور:" Margin="0,0,0,5"/>
                    <ComboBox x:Name="RoleComboBox" Height="30">
                        <ComboBoxItem Content="مدير النظام" Tag="Administrator"/>
                        <ComboBoxItem Content="مدير" Tag="Manager"/>
                        <ComboBoxItem Content="كاشير" Tag="Cashier"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- الصلاحيات -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="الصلاحيات:" Margin="0,0,0,5" FontWeight="SemiBold"/>
                    
                    <GroupBox Header="صلاحيات المبيعات" Margin="0,5,0,10">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="SalesPermissionCheckBox" Content="إدارة المبيعات" IsChecked="True" Margin="0,0,0,5"/>
                            <CheckBox x:Name="InvoicesPermissionCheckBox" Content="إدارة الفواتير" IsChecked="True" Margin="0,0,0,5"/>
                            <CheckBox x:Name="ReturnInvoicesPermissionCheckBox" Content="إرجاع الفواتير" IsChecked="True" Margin="0,0,0,5"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="صلاحيات المخزون" Margin="0,0,0,10">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="ProductsPermissionCheckBox" Content="إدارة المنتجات" IsChecked="True" Margin="0,0,0,5"/>
                            <CheckBox x:Name="CategoriesPermissionCheckBox" Content="إدارة الفئات" IsChecked="True" Margin="0,0,0,5"/>
                            <CheckBox x:Name="InventoryPermissionCheckBox" Content="إدارة المخزون" IsChecked="True" Margin="0,0,0,5"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="صلاحيات النظام" Margin="0,0,0,10">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="UsersPermissionCheckBox" Content="إدارة المستخدمين" IsChecked="False" Margin="0,0,0,5"/>
                            <CheckBox x:Name="ReportsPermissionCheckBox" Content="عرض التقارير" IsChecked="True" Margin="0,0,0,5"/>
                            <CheckBox x:Name="SettingsPermissionCheckBox" Content="إعدادات النظام" IsChecked="False" Margin="0,0,0,5"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
                
                <!-- حالة المستخدم -->
                <StackPanel Margin="0,0,0,15">
                    <CheckBox x:Name="IsActiveCheckBox" Content="مستخدم نشط" IsChecked="True"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
        
        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button x:Name="SaveButton" Grid.Column="1" Content="حفظ" Width="100" Height="35" Margin="0,0,10,0" Click="SaveButton_Click" Background="#4CAF50" Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
            
            <Button x:Name="CancelButton" Grid.Column="2" Content="إلغاء" Width="100" Height="35" Click="CancelButton_Click" Background="#F44336" Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </Grid>
    </Grid>
</Window>
