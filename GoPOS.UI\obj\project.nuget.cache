{"version": 2, "dgSpecHash": "4lh6jQI0UuU=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\gopos\\GoPOS.UI\\GoPOS.UI.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://api.nuget.org/v3/index.json.\r\n  No such host is known. (api.nuget.org:443)\r\n  No such host is known.", "libraryId": "System.Data.SqlClient"}]}