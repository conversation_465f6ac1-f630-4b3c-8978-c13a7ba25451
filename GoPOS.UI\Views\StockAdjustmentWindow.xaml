<Window x:Class="GoPOS.UI.Views.StockAdjustmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تعديل المخزون" Height="450" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock x:Name="TitleTextBlock" Grid.Row="0" Text="إضافة مخزون" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- نموذج تعديل المخزون -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="150"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- المنتج -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="المنتج:" VerticalAlignment="Center" Margin="0,5"/>
            <Grid Grid.Row="0" Grid.Column="1" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <ComboBox x:Name="ProductComboBox" Grid.Column="0" Height="30" DisplayMemberPath="Name" SelectionChanged="ProductComboBox_SelectionChanged"/>
                <Button x:Name="ScanBarcodeButton" Grid.Column="1" Content="مسح الباركود" Width="100" Height="30" Margin="5,0,0,0" Click="ScanBarcodeButton_Click"/>
            </Grid>

            <!-- الباركود -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="الباركود:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="BarcodeTextBox" Grid.Row="1" Grid.Column="1" Height="30" IsReadOnly="True" Margin="0,5"/>

            <!-- المخزن -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="المخزن:" VerticalAlignment="Center" Margin="0,5"/>
            <ComboBox x:Name="WarehouseComboBox" Grid.Row="2" Grid.Column="1" Height="30" Margin="0,5" SelectionChanged="WarehouseComboBox_SelectionChanged">
                <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                <ComboBoxItem Content="مخزن الفرع 1"/>
                <ComboBoxItem Content="مخزن الفرع 2"/>
            </ComboBox>

            <!-- الكمية الحالية -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="الكمية الحالية:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="CurrentQuantityTextBox" Grid.Row="3" Grid.Column="1" Height="30" IsReadOnly="True" Margin="0,5"/>

            <!-- الكمية الجديدة / التغيير -->
            <TextBlock x:Name="NewQuantityLabel" Grid.Row="4" Grid.Column="0" Text="الكمية الجديدة:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="NewQuantityTextBox" Grid.Row="4" Grid.Column="1" Height="30" Margin="0,5" TextChanged="NewQuantityTextBox_TextChanged"/>

            <!-- الكمية بعد التعديل -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="الكمية بعد التعديل:" VerticalAlignment="Center" Margin="0,5"/>
            <TextBox x:Name="FinalQuantityTextBox" Grid.Row="5" Grid.Column="1" Height="30" IsReadOnly="True" Margin="0,5"/>

            <!-- سعر التكلفة -->
            <TextBlock Grid.Row="6" Grid.Column="0" Text="سعر التكلفة:" VerticalAlignment="Center" Margin="0,5"/>
            <Grid Grid.Row="6" Grid.Column="1" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox x:Name="CostPriceTextBox" Grid.Column="0" Height="30"/>
                <TextBlock Grid.Column="1" Text="ر.س" VerticalAlignment="Center" Margin="5,0,0,0"/>
            </Grid>

            <!-- ملاحظات -->
            <TextBlock Grid.Row="7" Grid.Column="0" Text="ملاحظات:" VerticalAlignment="Top" Margin="0,5"/>
            <TextBox x:Name="NotesTextBox" Grid.Row="7" Grid.Column="1" Height="80" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,5"/>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="حفظ" Width="100" Height="40" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="40" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
