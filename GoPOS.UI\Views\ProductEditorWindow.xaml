<Window x:Class="GoPOS.UI.Views.ProductEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تحرير المنتج" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock x:Name="TitleTextBlock" Grid.Row="0" Text="إضافة منتج جديد" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- نموذج البيانات -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- العمود الأيمن -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="اسم المنتج:" Margin="0,0,0,5"/>
                <TextBox x:Name="NameTextBox" Height="30" Margin="0,0,0,10"/>

                <TextBlock Text="كود المنتج:" Margin="0,0,0,5"/>
                <TextBox x:Name="CodeTextBox" Height="30" Margin="0,0,0,10"/>

                <TextBlock Text="الباركود:" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBox x:Name="BarcodeTextBox" Grid.Column="0" Height="30"/>
                    <Button x:Name="GenerateBarcodeButton" Grid.Column="1" Content="توليد" Width="60" Height="30" Margin="5,0,0,0" Click="GenerateBarcodeButton_Click"/>
                </Grid>

                <TextBlock Text="سعر التكلفة:" Margin="0,0,0,5"/>
                <TextBox x:Name="CostPriceTextBox" Height="30" Margin="0,0,0,10"/>

                <TextBlock Text="سعر البيع:" Margin="0,0,0,5"/>
                <TextBox x:Name="PriceTextBox" Height="30" Margin="0,0,0,10"/>

                <TextBlock Text="الكمية:" Margin="0,0,0,5"/>
                <TextBox x:Name="QuantityTextBox" Height="30" Margin="0,0,0,10"/>

                <TextBlock Text="الفئة:" Margin="0,0,0,5"/>
                <ComboBox x:Name="CategoryComboBox" Height="30" DisplayMemberPath="Name" SelectedValuePath="Id" Margin="0,0,0,10"/>
            </StackPanel>

            <!-- العمود الأيسر -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <TextBlock Text="الوصف:" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" Height="80" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"/>

                <TextBlock Text="سعر الخصم:" Margin="0,0,0,5"/>
                <TextBox x:Name="DiscountPriceTextBox" Height="30" Margin="0,0,0,10"/>

                <TextBlock Text="الحد الأدنى للكمية:" Margin="0,0,0,5"/>
                <TextBox x:Name="MinimumQuantityTextBox" Height="30" Margin="0,0,0,10"/>

                <Grid Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <CheckBox x:Name="IsAvailableCheckBox" Grid.Column="0" Content="متاح للبيع" IsChecked="True" VerticalAlignment="Center"/>
                    <Button x:Name="CalculatePriceButton" Grid.Column="1" Content="حساب سعر البيع" Height="30" Click="CalculatePriceButton_Click"/>
                </Grid>

                <TextBlock Text="صورة المنتج:" Margin="0,0,0,5"/>
                <Grid Height="120">
                    <Border BorderBrush="#DDDDDD" BorderThickness="1">
                        <Image x:Name="ProductImage" Stretch="Uniform"/>
                    </Border>
                    <Button x:Name="SelectImageButton" Content="اختيار صورة" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,10" Click="SelectImageButton_Click"/>
                </Grid>

                <TextBlock x:Name="ProfitMarginTextBlock" Text="هامش الربح: 0%" Margin="0,10,0,0" FontWeight="Bold" Foreground="#4CAF50"/>
            </StackPanel>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="حفظ" Width="100" Height="40" Margin="0,0,10,0" Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="40" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
