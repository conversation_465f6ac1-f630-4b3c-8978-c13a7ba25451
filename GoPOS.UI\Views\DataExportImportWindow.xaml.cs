using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GoPOS.UI.Services;
using Microsoft.Win32;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for DataExportImportWindow.xaml
    /// </summary>
    public partial class DataExportImportWindow : Window
    {
        private readonly DataExportImportService _exportImportService;
        private string _selectedTable;
        private DataTable _previewData;

        public DataExportImportWindow()
        {
            InitializeComponent();
            
            // تهيئة خدمة التصدير والاستيراد
            _exportImportService = new DataExportImportService();
            
            // تحميل قائمة الجداول
            LoadTablesAsync();
        }

        private async void LoadTablesAsync()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;
                
                // تعطيل عناصر واجهة المستخدم
                TablesListBox.IsEnabled = false;
                StatusTextBlock.Text = "جاري تحميل قائمة الجداول...";
                
                // الحصول على قائمة الجداول
                List<string> tables = await _exportImportService.GetTablesAsync();
                
                // عرض قائمة الجداول
                TablesListBox.Items.Clear();
                foreach (string table in tables)
                {
                    TablesListBox.Items.Add(table);
                }
                
                // تحديث حالة واجهة المستخدم
                StatusTextBlock.Text = $"تم تحميل {tables.Count} جدول";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل قائمة الجداول: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "حدث خطأ أثناء تحميل قائمة الجداول";
            }
            finally
            {
                // إعادة تمكين عناصر واجهة المستخدم
                TablesListBox.IsEnabled = true;
                
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private void TablesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // الحصول على الجدول المحدد
            _selectedTable = TablesListBox.SelectedItem as string;
            
            // تحديث عنوان الجدول
            TableNameTextBlock.Text = _selectedTable ?? "اختر جدولاً من القائمة";
            
            // تحديث حالة الأزرار
            bool hasSelection = !string.IsNullOrWhiteSpace(_selectedTable);
            ExportToExcelButton.IsEnabled = hasSelection;
            ExportToCsvButton.IsEnabled = hasSelection;
            ImportButton.IsEnabled = hasSelection;
            
            // تحميل معاينة البيانات
            if (hasSelection)
            {
                LoadDataPreviewAsync(_selectedTable);
            }
            else
            {
                DataPreviewGrid.ItemsSource = null;
            }
        }

        private async void LoadDataPreviewAsync(string tableName)
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;
                
                // تعطيل عناصر واجهة المستخدم
                TablesListBox.IsEnabled = false;
                ExportToExcelButton.IsEnabled = false;
                ExportToCsvButton.IsEnabled = false;
                ImportButton.IsEnabled = false;
                
                StatusTextBlock.Text = $"جاري تحميل معاينة البيانات للجدول {tableName}...";
                
                // الحصول على بيانات الجدول
                _previewData = await Task.Run(() => _exportImportService.GetTableDataAsync(tableName));
                
                // عرض البيانات
                DataPreviewGrid.ItemsSource = _previewData.DefaultView;
                
                // تحديث حالة واجهة المستخدم
                StatusTextBlock.Text = $"تم تحميل {_previewData.Rows.Count} صف من الجدول {tableName}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل معاينة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "حدث خطأ أثناء تحميل معاينة البيانات";
                DataPreviewGrid.ItemsSource = null;
            }
            finally
            {
                // إعادة تمكين عناصر واجهة المستخدم
                TablesListBox.IsEnabled = true;
                ExportToExcelButton.IsEnabled = true;
                ExportToCsvButton.IsEnabled = true;
                ImportButton.IsEnabled = true;
                
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private async void ExportToExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(_selectedTable))
                {
                    return;
                }
                
                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;
                
                // تعطيل الأزرار
                ExportToExcelButton.IsEnabled = false;
                ExportToCsvButton.IsEnabled = false;
                
                StatusTextBlock.Text = $"جاري تصدير الجدول {_selectedTable} إلى Excel...";
                
                // تصدير الجدول إلى Excel
                string filePath = await _exportImportService.ExportTableToExcelAsync(_selectedTable);
                
                // عرض رسالة نجاح
                MessageBox.Show($"تم تصدير الجدول بنجاح إلى:\n{filePath}", "تم التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // سؤال المستخدم إذا كان يريد فتح الملف
                MessageBoxResult result = MessageBox.Show("هل ترغب في فتح الملف الآن؟", "فتح الملف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    // فتح الملف
                    System.Diagnostics.Process.Start(filePath);
                }
                
                // تحديث حالة واجهة المستخدم
                StatusTextBlock.Text = $"تم تصدير الجدول {_selectedTable} إلى Excel بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير الجدول إلى Excel: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "حدث خطأ أثناء تصدير الجدول";
            }
            finally
            {
                // إعادة تمكين الأزرار
                ExportToExcelButton.IsEnabled = true;
                ExportToCsvButton.IsEnabled = true;
                
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private async void ExportToCsvButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(_selectedTable))
                {
                    return;
                }
                
                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;
                
                // تعطيل الأزرار
                ExportToExcelButton.IsEnabled = false;
                ExportToCsvButton.IsEnabled = false;
                
                StatusTextBlock.Text = $"جاري تصدير الجدول {_selectedTable} إلى CSV...";
                
                // تصدير الجدول إلى CSV
                string filePath = await _exportImportService.ExportTableToCsvAsync(_selectedTable);
                
                // عرض رسالة نجاح
                MessageBox.Show($"تم تصدير الجدول بنجاح إلى:\n{filePath}", "تم التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // سؤال المستخدم إذا كان يريد فتح الملف
                MessageBoxResult result = MessageBox.Show("هل ترغب في فتح الملف الآن؟", "فتح الملف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    // فتح الملف
                    System.Diagnostics.Process.Start(filePath);
                }
                
                // تحديث حالة واجهة المستخدم
                StatusTextBlock.Text = $"تم تصدير الجدول {_selectedTable} إلى CSV بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير الجدول إلى CSV: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "حدث خطأ أثناء تصدير الجدول";
            }
            finally
            {
                // إعادة تمكين الأزرار
                ExportToExcelButton.IsEnabled = true;
                ExportToCsvButton.IsEnabled = true;
                
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private async void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(_selectedTable))
                {
                    return;
                }
                
                // فتح مربع حوار لاختيار ملف
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "ملفات Excel (*.xlsx)|*.xlsx|ملفات CSV (*.csv)|*.csv|جميع الملفات (*.*)|*.*",
                    Title = "اختيار ملف للاستيراد"
                };
                
                if (openFileDialog.ShowDialog() != true)
                {
                    return;
                }
                
                // التحقق من نوع الملف
                string filePath = openFileDialog.FileName;
                string fileExtension = Path.GetExtension(filePath).ToLower();
                
                // سؤال المستخدم إذا كان يريد حذف البيانات الموجودة
                MessageBoxResult truncateResult = MessageBox.Show(
                    "هل ترغب في حذف البيانات الموجودة في الجدول قبل الاستيراد؟",
                    "حذف البيانات الموجودة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                bool truncateTable = truncateResult == MessageBoxResult.Yes;
                
                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;
                
                // تعطيل الأزرار
                ImportButton.IsEnabled = false;
                
                StatusTextBlock.Text = $"جاري استيراد البيانات إلى الجدول {_selectedTable}...";
                
                // استيراد البيانات
                int rowsAffected = 0;
                
                if (fileExtension == ".xlsx")
                {
                    rowsAffected = await _exportImportService.ImportExcelToTableAsync(filePath, _selectedTable, truncateTable);
                }
                else if (fileExtension == ".csv")
                {
                    rowsAffected = await _exportImportService.ImportCsvToTableAsync(filePath, _selectedTable, truncateTable);
                }
                else
                {
                    throw new Exception("نوع الملف غير مدعوم");
                }
                
                // عرض رسالة نجاح
                MessageBox.Show($"تم استيراد {rowsAffected} صف بنجاح إلى الجدول {_selectedTable}", "تم الاستيراد", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // إعادة تحميل معاينة البيانات
                LoadDataPreviewAsync(_selectedTable);
                
                // تحديث حالة واجهة المستخدم
                StatusTextBlock.Text = $"تم استيراد {rowsAffected} صف بنجاح إلى الجدول {_selectedTable}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استيراد البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "حدث خطأ أثناء استيراد البيانات";
            }
            finally
            {
                // إعادة تمكين الأزرار
                ImportButton.IsEnabled = true;
                
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // إعادة تحميل قائمة الجداول
            LoadTablesAsync();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة
            Close();
        }
    }
}
