using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for InstallmentPlanWindow.xaml
    /// </summary>
    public partial class InstallmentPlanWindow : Window
    {
        private decimal _totalAmount;
        private decimal _downPayment;
        private decimal _remainingAmount;
        private int _numberOfInstallments;
        private int _frequencyInDays;
        private DateTime _startDate;
        private ObservableCollection<InstallmentViewModel> _installments;
        private List<CustomerViewModel> _customers;

        public InstallmentPlanViewModel InstallmentPlan { get; private set; }

        public InstallmentPlanWindow(decimal totalAmount, int orderId)
        {
            InitializeComponent();

            _totalAmount = totalAmount;
            _downPayment = 0;
            _remainingAmount = totalAmount;
            _numberOfInstallments = 3;
            _frequencyInDays = 30; // شهري
            _startDate = DateTime.Now.AddDays(30);
            _installments = new ObservableCollection<InstallmentViewModel>();

            OrderIdTextBlock.Text = orderId.ToString();
            TotalAmountTextBlock.Text = _totalAmount.ToString("N2");
            RemainingAmountTextBlock.Text = _remainingAmount.ToString("N2");
            StartDatePicker.SelectedDate = _startDate;

            NumberOfInstallmentsComboBox.SelectedIndex = 0;
            FrequencyComboBox.SelectedIndex = 2; // شهري

            InstallmentsDataGrid.ItemsSource = _installments;

            LoadCustomers();
            CalculateInstallments();
        }

        private void LoadCustomers()
        {
            // تحميل العملاء (سيتم استبدالها بقراءة من قاعدة البيانات)
            _customers = new List<CustomerViewModel>
            {
                new CustomerViewModel { Id = 1, Name = "أحمد محمد" },
                new CustomerViewModel { Id = 2, Name = "سارة أحمد" },
                new CustomerViewModel { Id = 3, Name = "محمد علي" },
                new CustomerViewModel { Id = 4, Name = "فاطمة خالد" },
                new CustomerViewModel { Id = 5, Name = "خالد عبدالله" }
            };

            CustomerComboBox.ItemsSource = _customers;
            CustomerComboBox.SelectedIndex = 0;
        }

        private void CalculateInstallments()
        {
            _installments.Clear();

            if (_numberOfInstallments <= 0 || _remainingAmount <= 0)
                return;

            decimal installmentAmount = Math.Round(_remainingAmount / _numberOfInstallments, 2);
            InstallmentAmountTextBlock.Text = installmentAmount.ToString("N2");

            DateTime dueDate = _startDate;

            for (int i = 1; i <= _numberOfInstallments; i++)
            {
                _installments.Add(new InstallmentViewModel
                {
                    InstallmentNumber = i,
                    Amount = i < _numberOfInstallments ? installmentAmount : _remainingAmount - (installmentAmount * (i - 1)),
                    DueDate = dueDate,
                    Status = 0, // Pending
                    StatusText = "قيد الانتظار"
                });

                dueDate = dueDate.AddDays(_frequencyInDays);
            }
        }

        private void CustomerComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
        }

        private void DownPaymentTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(DownPaymentTextBox.Text, out decimal downPayment))
            {
                if (downPayment < 0)
                {
                    downPayment = 0;
                    DownPaymentTextBox.Text = "0";
                }
                else if (downPayment > _totalAmount)
                {
                    downPayment = _totalAmount;
                    DownPaymentTextBox.Text = _totalAmount.ToString();
                }

                _downPayment = downPayment;
                _remainingAmount = _totalAmount - _downPayment;
                RemainingAmountTextBlock.Text = _remainingAmount.ToString("N2");

                CalculateInstallments();
            }
        }

        private void NumberOfInstallmentsComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ComboBoxItem selectedItem = NumberOfInstallmentsComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null && int.TryParse(selectedItem.Content.ToString(), out int numberOfInstallments))
            {
                _numberOfInstallments = numberOfInstallments;
                CalculateInstallments();
            }
        }

        private void FrequencyComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            int selectedIndex = FrequencyComboBox.SelectedIndex;
            switch (selectedIndex)
            {
                case 0: // أسبوعي
                    _frequencyInDays = 7;
                    break;
                case 1: // نصف شهري
                    _frequencyInDays = 15;
                    break;
                case 2: // شهري
                    _frequencyInDays = 30;
                    break;
                case 3: // ربع سنوي
                    _frequencyInDays = 90;
                    break;
            }

            CalculateInstallments();
        }

        private void StartDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (StartDatePicker.SelectedDate.HasValue)
            {
                _startDate = StartDatePicker.SelectedDate.Value;
                CalculateInstallments();
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (CustomerComboBox.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار عميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_downPayment <= 0)
            {
                MessageBox.Show("الرجاء إدخال دفعة مقدمة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_installments.Count == 0)
            {
                MessageBox.Show("لا يمكن إنشاء خطة أقساط فارغة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            CustomerViewModel selectedCustomer = CustomerComboBox.SelectedItem as CustomerViewModel;

            // إنشاء خطة الأقساط
            InstallmentPlan = new InstallmentPlanViewModel
            {
                OrderId = int.Parse(OrderIdTextBlock.Text),
                CustomerId = selectedCustomer.Id,
                CustomerName = selectedCustomer.Name,
                TotalAmount = _totalAmount,
                DownPayment = _downPayment,
                RemainingAmount = _remainingAmount,
                NumberOfInstallments = _numberOfInstallments,
                Frequency = FrequencyComboBox.SelectedIndex,
                StartDate = _startDate,
                Status = 0, // Active
                Installments = new List<InstallmentViewModel>(_installments)
            };

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // فئات عرض البيانات
    public class InstallmentViewModel
    {
        public int InstallmentNumber { get; set; }
        public decimal Amount { get; set; }
        public DateTime DueDate { get; set; }
        public int Status { get; set; }
        public string StatusText { get; set; }
        public DateTime? PaymentDate { get; set; }
    }

    public class InstallmentPlanViewModel
    {
        public int OrderId { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DownPayment { get; set; }
        public decimal RemainingAmount { get; set; }
        public int NumberOfInstallments { get; set; }
        public int Frequency { get; set; }
        public DateTime StartDate { get; set; }
        public int Status { get; set; }
        public string StatusText { get; set; }
        public string CanCancel { get; set; }
        public List<InstallmentViewModel> Installments { get; set; }
    }
}
