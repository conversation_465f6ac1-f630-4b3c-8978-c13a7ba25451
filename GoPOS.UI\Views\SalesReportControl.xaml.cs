using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using GoPOS.Core.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for SalesReportControl.xaml
    /// </summary>
    public partial class SalesReportControl : UserControl
    {
        private DateTime _fromDate;
        private DateTime _toDate;
        private ObservableCollection<InvoiceViewModel> _salesData;
        private Random _random = new Random();

        public SalesReportControl(DateTime fromDate, DateTime toDate)
        {
            InitializeComponent();
            
            _fromDate = fromDate;
            _toDate = toDate;
            
            LoadData();
            DrawSalesChart();
        }

        private void LoadData()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;
                
                // تحميل بيانات المبيعات (سيتم استبدالها بقراءة من قاعدة البيانات)
                _salesData = GetSampleSalesData();
                
                // عرض البيانات في الجدول
                SalesDataGrid.ItemsSource = _salesData;
                
                // حساب وعرض الإحصائيات
                CalculateAndDisplayStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private ObservableCollection<InvoiceViewModel> GetSampleSalesData()
        {
            // إنشاء بيانات تجريبية للمبيعات
            ObservableCollection<InvoiceViewModel> sales = new ObservableCollection<InvoiceViewModel>();
            
            // عدد الأيام في الفترة المحددة
            int days = (int)(_toDate - _fromDate).TotalDays + 1;
            
            // إنشاء فواتير تجريبية لكل يوم
            for (int i = 0; i < days; i++)
            {
                DateTime currentDate = _fromDate.AddDays(i);
                
                // إنشاء عدد عشوائي من الفواتير لكل يوم
                int invoicesPerDay = _random.Next(3, 10);
                
                for (int j = 0; j < invoicesPerDay; j++)
                {
                    // إنشاء وقت عشوائي خلال اليوم
                    int hour = _random.Next(8, 22);
                    int minute = _random.Next(0, 60);
                    DateTime invoiceDateTime = new DateTime(currentDate.Year, currentDate.Month, currentDate.Day, hour, minute, 0);
                    
                    // إنشاء قيمة عشوائية للفاتورة
                    decimal subtotal = _random.Next(50, 500);
                    decimal taxAmount = subtotal * 0.15m;
                    decimal total = subtotal + taxAmount;
                    
                    // تحديد حالة الفاتورة (معظمها مكتملة، وبعضها ملغاة أو مرتجعة)
                    InvoiceStatus status;
                    string statusText;
                    int statusRandom = _random.Next(0, 100);
                    if (statusRandom < 85)
                    {
                        status = InvoiceStatus.Completed;
                        statusText = "مكتملة";
                    }
                    else if (statusRandom < 95)
                    {
                        status = InvoiceStatus.Cancelled;
                        statusText = "ملغاة";
                        // الفواتير الملغاة ليس لها قيمة
                        subtotal = 0;
                        taxAmount = 0;
                        total = 0;
                    }
                    else
                    {
                        status = InvoiceStatus.Returned;
                        statusText = "مرتجعة";
                        // الفواتير المرتجعة لها قيمة سالبة
                        subtotal = -subtotal;
                        taxAmount = -taxAmount;
                        total = -total;
                    }
                    
                    // تحديد طريقة الدفع
                    InvoicePaymentMethod paymentMethod;
                    string paymentMethodText;
                    int paymentRandom = _random.Next(0, 100);
                    if (paymentRandom < 60)
                    {
                        paymentMethod = InvoicePaymentMethod.Cash;
                        paymentMethodText = "نقداً";
                    }
                    else if (paymentRandom < 90)
                    {
                        paymentMethod = InvoicePaymentMethod.CreditCard;
                        paymentMethodText = "بطاقة ائتمان";
                    }
                    else
                    {
                        paymentMethod = InvoicePaymentMethod.Other;
                        paymentMethodText = "أخرى";
                    }
                    
                    // إنشاء فاتورة جديدة
                    sales.Add(new InvoiceViewModel
                    {
                        Id = sales.Count + 1,
                        InvoiceNumber = $"INV-{currentDate:yyMMdd}-{j + 1:D2}",
                        Date = invoiceDateTime,
                        CustomerId = _random.Next(1, 10),
                        CustomerName = $"عميل {_random.Next(1, 10)}",
                        UserId = _random.Next(1, 3),
                        UserName = _random.Next(1, 3) == 1 ? "المدير" : "كاشير",
                        Subtotal = subtotal,
                        DiscountAmount = 0,
                        DiscountPercentage = 0,
                        IsDiscountPercentage = false,
                        TaxAmount = taxAmount,
                        TaxPercentage = 15,
                        Total = total,
                        Status = status,
                        StatusText = statusText,
                        PaymentMethod = paymentMethod,
                        PaymentMethodText = paymentMethodText,
                        IsPaid = status == InvoiceStatus.Completed,
                        PaidAmount = status == InvoiceStatus.Completed ? total : 0,
                        RemainingAmount = status == InvoiceStatus.Completed ? 0 : total,
                        IsInstallment = false,
                        InstallmentPlanId = null,
                        Notes = "",
                        CanEdit = Visibility.Collapsed,
                        CanDelete = Visibility.Collapsed
                    });
                }
            }
            
            // ترتيب الفواتير حسب التاريخ (من الأحدث إلى الأقدم)
            return new ObservableCollection<InvoiceViewModel>(sales.OrderByDescending(s => s.Date));
        }

        private void CalculateAndDisplayStatistics()
        {
            // حساب إجمالي المبيعات
            decimal totalSales = _salesData
                .Where(s => s.Status == InvoiceStatus.Completed)
                .Sum(s => s.Total);
            
            // حساب عدد الفواتير المكتملة
            int invoiceCount = _salesData
                .Count(s => s.Status == InvoiceStatus.Completed);
            
            // حساب متوسط قيمة الفاتورة
            decimal averageInvoice = invoiceCount > 0 ? totalSales / invoiceCount : 0;
            
            // حساب إجمالي الضرائب
            decimal totalTax = _salesData
                .Where(s => s.Status == InvoiceStatus.Completed)
                .Sum(s => s.TaxAmount);
            
            // عرض الإحصائيات
            TotalSalesTextBlock.Text = $"{totalSales:N2} ر.س";
            InvoiceCountTextBlock.Text = invoiceCount.ToString();
            AverageInvoiceTextBlock.Text = $"{averageInvoice:N2} ر.س";
            TotalTaxTextBlock.Text = $"{totalTax:N2} ر.س";
        }

        private void DrawSalesChart()
        {
            try
            {
                // مسح الرسم البياني الحالي
                SalesChartCanvas.Children.Clear();
                
                // التأكد من أن Canvas له أبعاد صالحة
                if (SalesChartCanvas.ActualWidth <= 0 || SalesChartCanvas.ActualHeight <= 0)
                {
                    // تعيين أبعاد افتراضية إذا لم تكن متاحة بعد
                    SalesChartCanvas.Width = 600;
                    SalesChartCanvas.Height = 200;
                }
                
                // الحصول على بيانات المبيعات اليومية
                var dailySales = GetDailySalesData();
                
                if (dailySales.Count == 0)
                    return;
                
                // تحديد أبعاد الرسم البياني
                double chartWidth = SalesChartCanvas.ActualWidth > 0 ? SalesChartCanvas.ActualWidth : 600;
                double chartHeight = SalesChartCanvas.ActualHeight > 0 ? SalesChartCanvas.ActualHeight : 200;
                double padding = 30;
                double graphWidth = chartWidth - 2 * padding;
                double graphHeight = chartHeight - 2 * padding;
                
                // رسم المحاور
                DrawAxes(padding, graphWidth, graphHeight);
                
                // رسم البيانات
                DrawData(dailySales, padding, graphWidth, graphHeight);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رسم الرسم البياني: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<KeyValuePair<DateTime, decimal>> GetDailySalesData()
        {
            // تجميع المبيعات حسب اليوم
            var dailySales = _salesData
                .Where(s => s.Status == InvoiceStatus.Completed)
                .GroupBy(s => s.Date.Date)
                .Select(g => new KeyValuePair<DateTime, decimal>(g.Key, g.Sum(s => s.Total)))
                .OrderBy(kv => kv.Key)
                .ToList();
            
            return dailySales;
        }

        private void DrawAxes(double padding, double graphWidth, double graphHeight)
        {
            // رسم المحور الأفقي (X)
            Line xAxis = new Line
            {
                X1 = padding,
                Y1 = padding + graphHeight,
                X2 = padding + graphWidth,
                Y2 = padding + graphHeight,
                Stroke = Brushes.Black,
                StrokeThickness = 1
            };
            SalesChartCanvas.Children.Add(xAxis);
            
            // رسم المحور الرأسي (Y)
            Line yAxis = new Line
            {
                X1 = padding,
                Y1 = padding,
                X2 = padding,
                Y2 = padding + graphHeight,
                Stroke = Brushes.Black,
                StrokeThickness = 1
            };
            SalesChartCanvas.Children.Add(yAxis);
        }

        private void DrawData(List<KeyValuePair<DateTime, decimal>> dailySales, double padding, double graphWidth, double graphHeight)
        {
            if (dailySales.Count == 0)
                return;
            
            // الحصول على القيم القصوى
            decimal maxSales = dailySales.Max(kv => kv.Value);
            
            // إنشاء مسار للرسم البياني
            PathFigure pathFigure = new PathFigure();
            PathGeometry pathGeometry = new PathGeometry();
            pathGeometry.Figures.Add(pathFigure);
            
            // إنشاء مسار للمنطقة تحت الخط
            PathFigure areaPathFigure = new PathFigure();
            PathGeometry areaPathGeometry = new PathGeometry();
            areaPathGeometry.Figures.Add(areaPathFigure);
            
            // رسم النقاط والخطوط
            for (int i = 0; i < dailySales.Count; i++)
            {
                // حساب الإحداثيات
                double x = padding + (i * graphWidth / (dailySales.Count - 1));
                double y = padding + graphHeight - (double)((dailySales[i].Value / maxSales) * graphHeight);
                
                // رسم نقطة
                Ellipse point = new Ellipse
                {
                    Width = 6,
                    Height = 6,
                    Fill = new SolidColorBrush(Color.FromRgb(103, 58, 183)), // #673AB7
                    Stroke = Brushes.White,
                    StrokeThickness = 1
                };
                Canvas.SetLeft(point, x - 3);
                Canvas.SetTop(point, y - 3);
                SalesChartCanvas.Children.Add(point);
                
                // إضافة تسمية للتاريخ
                if (dailySales.Count <= 10 || i % (dailySales.Count / 10 + 1) == 0)
                {
                    TextBlock dateLabel = new TextBlock
                    {
                        Text = dailySales[i].Key.ToString("MM/dd"),
                        FontSize = 10,
                        TextAlignment = TextAlignment.Center,
                        Width = 40
                    };
                    Canvas.SetLeft(dateLabel, x - 20);
                    Canvas.SetTop(dateLabel, padding + graphHeight + 5);
                    SalesChartCanvas.Children.Add(dateLabel);
                }
                
                // إضافة النقطة إلى المسار
                Point point2D = new Point(x, y);
                if (i == 0)
                {
                    pathFigure.StartPoint = point2D;
                    areaPathFigure.StartPoint = new Point(x, padding + graphHeight);
                    areaPathFigure.Segments.Add(new LineSegment(point2D, true));
                }
                else
                {
                    pathFigure.Segments.Add(new LineSegment(point2D, true));
                    areaPathFigure.Segments.Add(new LineSegment(point2D, true));
                }
                
                // إضافة تسمية للقيمة
                if (i == 0 || i == dailySales.Count - 1 || dailySales[i].Value == maxSales)
                {
                    TextBlock valueLabel = new TextBlock
                    {
                        Text = dailySales[i].Value.ToString("N0"),
                        FontSize = 10,
                        Foreground = new SolidColorBrush(Color.FromRgb(103, 58, 183)), // #673AB7
                        FontWeight = FontWeights.SemiBold
                    };
                    Canvas.SetLeft(valueLabel, x + 5);
                    Canvas.SetTop(valueLabel, y - 15);
                    SalesChartCanvas.Children.Add(valueLabel);
                }
            }
            
            // إكمال مسار المنطقة تحت الخط
            areaPathFigure.Segments.Add(new LineSegment(new Point(padding + graphWidth, padding + graphHeight), true));
            
            // رسم الخط
            Path linePath = new Path
            {
                Stroke = new SolidColorBrush(Color.FromRgb(103, 58, 183)), // #673AB7
                StrokeThickness = 2,
                Data = pathGeometry
            };
            SalesChartCanvas.Children.Add(linePath);
            
            // رسم المنطقة تحت الخط
            Path areaPath = new Path
            {
                Fill = new SolidColorBrush(Color.FromArgb(50, 103, 58, 183)), // #673AB7 with 20% opacity
                Data = areaPathGeometry
            };
            SalesChartCanvas.Children.Insert(0, areaPath); // إضافة في الخلفية
        }
    }
}
