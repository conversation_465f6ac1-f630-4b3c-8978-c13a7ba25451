using GoPOS.Core.Models;
using Microsoft.EntityFrameworkCore;
using System;

namespace GoPOS.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<InstallmentPlan> InstallmentPlans { get; set; }
        public DbSet<Installment> Installments { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<InventoryCheck> InventoryChecks { get; set; }
        public DbSet<InventoryCheckItem> InventoryCheckItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.Customer)
                .WithMany(c => c.Orders)
                .HasForeignKey(o => o.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Order>()
                .HasOne(o => o.User)
                .WithMany(u => u.Orders)
                .HasForeignKey(o => o.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.Order)
                .WithMany(o => o.OrderItems)
                .HasForeignKey(oi => oi.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<OrderItem>()
                .HasOne(oi => oi.Product)
                .WithMany(p => p.OrderItems)
                .HasForeignKey(oi => oi.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Order)
                .WithMany(o => o.Payments)
                .HasForeignKey(p => p.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            // تكوين علاقات الأقساط
            modelBuilder.Entity<InstallmentPlan>()
                .HasOne(ip => ip.Order)
                .WithOne(o => o.InstallmentPlan)
                .HasForeignKey<InstallmentPlan>(ip => ip.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<InstallmentPlan>()
                .HasOne(ip => ip.Customer)
                .WithMany()
                .HasForeignKey(ip => ip.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Installment>()
                .HasOne(i => i.InstallmentPlan)
                .WithMany(ip => ip.Installments)
                .HasForeignKey(i => i.InstallmentPlanId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Installment>()
                .HasOne(i => i.Order)
                .WithMany()
                .HasForeignKey(i => i.OrderId)
                .OnDelete(DeleteBehavior.Restrict);

            // إضافة بيانات أولية
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // إضافة مستخدم مسؤول افتراضي
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = "AQAAAAEAACcQAAAAEBLjouNqaeiVWbN0TbXUS3+ChW3d7aQIk/BQEkWBxlrdRRngp14b0BIH0Rp65qD6mA==", // كلمة المرور: Admin@123
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    Role = UserRole.Administrator,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }
            );

            // إضافة فئات افتراضية
            modelBuilder.Entity<Category>().HasData(
                new Category
                {
                    Id = 1,
                    Name = "مشروبات",
                    Code = "CAT1001",
                    Description = "جميع أنواع المشروبات",
                    Color = "#2196F3",
                    IsActive = true,
                    DisplayOrder = 1,
                    CreatedAt = DateTime.Now
                },
                new Category
                {
                    Id = 2,
                    Name = "وجبات سريعة",
                    Code = "CAT1002",
                    Description = "وجبات سريعة متنوعة",
                    Color = "#4CAF50",
                    IsActive = true,
                    DisplayOrder = 2,
                    CreatedAt = DateTime.Now
                },
                new Category
                {
                    Id = 3,
                    Name = "حلويات",
                    Code = "CAT1003",
                    Description = "حلويات متنوعة",
                    Color = "#FF9800",
                    IsActive = true,
                    DisplayOrder = 3,
                    CreatedAt = DateTime.Now
                },
                new Category
                {
                    Id = 4,
                    Name = "مستلزمات",
                    Code = "CAT1004",
                    Description = "مستلزمات متنوعة",
                    Color = "#9C27B0",
                    IsActive = true,
                    DisplayOrder = 4,
                    CreatedAt = DateTime.Now
                },
                new Category
                {
                    Id = 5,
                    Name = "إلكترونيات",
                    Code = "CAT1005",
                    Description = "منتجات إلكترونية",
                    Color = "#F44336",
                    IsActive = true,
                    DisplayOrder = 5,
                    CreatedAt = DateTime.Now
                }
            );
        }
    }
}
