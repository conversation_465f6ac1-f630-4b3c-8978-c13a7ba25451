using System;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج بيانات الخزانة
    /// </summary>
    public class CashRegister
    {
        /// <summary>
        /// معرف الخزانة
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// اسم الخزانة
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal CurrentBalance { get; set; }

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        public decimal OpeningBalance { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// معرف المستخدم المسؤول
        /// </summary>
        public int ResponsibleUserId { get; set; }

        /// <summary>
        /// اسم المستخدم المسؤول
        /// </summary>
        public string ResponsibleUserName { get; set; }

        /// <summary>
        /// هل الخزانة مفتوحة
        /// </summary>
        public bool IsOpen { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }
    }

    /// <summary>
    /// نموذج بيانات حركة الخزانة
    /// </summary>
    public class CashRegisterTransaction
    {
        /// <summary>
        /// معرف الحركة
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// معرف الخزانة
        /// </summary>
        public int CashRegisterId { get; set; }

        /// <summary>
        /// تاريخ الحركة
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// نوع الحركة
        /// </summary>
        public CashTransactionType Type { get; set; }

        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// الرصيد قبل الحركة
        /// </summary>
        public decimal BalanceBefore { get; set; }

        /// <summary>
        /// الرصيد بعد الحركة
        /// </summary>
        public decimal BalanceAfter { get; set; }

        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// الوصف
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// معرف المرجع (مثل معرف الفاتورة أو المنصرف)
        /// </summary>
        public int? ReferenceId { get; set; }

        /// <summary>
        /// نوع المرجع
        /// </summary>
        public ReferenceType? ReferenceType { get; set; }
    }

    /// <summary>
    /// أنواع حركات الخزانة
    /// </summary>
    public enum CashTransactionType
    {
        /// <summary>
        /// إيداع
        /// </summary>
        Deposit = 1,

        /// <summary>
        /// سحب
        /// </summary>
        Withdrawal = 2,

        /// <summary>
        /// مبيعات
        /// </summary>
        Sales = 3,

        /// <summary>
        /// منصرفات
        /// </summary>
        Expense = 4,

        /// <summary>
        /// رصيد افتتاحي
        /// </summary>
        OpeningBalance = 5,

        /// <summary>
        /// تسوية
        /// </summary>
        Adjustment = 6
    }

    /// <summary>
    /// أنواع المراجع
    /// </summary>
    public enum ReferenceType
    {
        /// <summary>
        /// فاتورة
        /// </summary>
        Invoice = 1,

        /// <summary>
        /// منصرف
        /// </summary>
        Expense = 2,

        /// <summary>
        /// إيداع
        /// </summary>
        Deposit = 3,

        /// <summary>
        /// سحب
        /// </summary>
        Withdrawal = 4
    }
}
