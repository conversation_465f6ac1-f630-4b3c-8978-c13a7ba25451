namespace GoPOS.Data.Models
{
    /// <summary>
    /// نموذج الفئة
    /// </summary>
    public class Category
    {
        /// <summary>
        /// معرف الفئة
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// وصف الفئة
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// لون الفئة
        /// </summary>
        public string Color { get; set; }
        
        /// <summary>
        /// معرف الفئة الأب
        /// </summary>
        public int? ParentId { get; set; }
        
        /// <summary>
        /// هل الفئة نشطة
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// عدد المنتجات في الفئة
        /// </summary>
        public int ProductCount { get; set; }
    }
}
