﻿#pragma checksum "..\..\..\..\Views\InventoryManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7FF31C37A12F90B5CB7A778CD896988479DDF0B1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// InventoryManagementView
    /// </summary>
    public partial class InventoryManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 27 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WarehouseComboBox;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddStockButton;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AdjustStockButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransferStockButton;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportInventoryButton;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintInventoryButton;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InventoryDataGrid;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalProductsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalQuantityTextBlock;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockTextBlock;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MovementTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid StockMovementDataGrid;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MarkAsReadButton;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteAlertsButton;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshAlertsButton;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\InventoryManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid AlertsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/inventorymanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\InventoryManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WarehouseComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 27 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.WarehouseComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WarehouseComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CategoryFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 33 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.CategoryFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CategoryFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 39 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.SearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.SearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AddStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.AddStockButton.Click += new System.Windows.RoutedEventHandler(this.AddStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AdjustStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.AdjustStockButton.Click += new System.Windows.RoutedEventHandler(this.AdjustStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TransferStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.TransferStockButton.Click += new System.Windows.RoutedEventHandler(this.TransferStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportInventoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.ExportInventoryButton.Click += new System.Windows.RoutedEventHandler(this.ExportInventoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PrintInventoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.PrintInventoryButton.Click += new System.Windows.RoutedEventHandler(this.PrintInventoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InventoryDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 65 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.InventoryDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InventoryDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TotalProductsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TotalQuantityTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.LowStockTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 142 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 145 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.MovementTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 147 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.MovementTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.MovementTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.StockMovementDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 18:
            this.MarkAsReadButton = ((System.Windows.Controls.Button)(target));
            
            #line 185 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.MarkAsReadButton.Click += new System.Windows.RoutedEventHandler(this.MarkAsReadButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.DeleteAlertsButton = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.DeleteAlertsButton.Click += new System.Windows.RoutedEventHandler(this.DeleteAlertsButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.RefreshAlertsButton = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\..\Views\InventoryManagementView.xaml"
            this.RefreshAlertsButton.Click += new System.Windows.RoutedEventHandler(this.RefreshAlertsButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.AlertsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 22:
            
            #line 217 "..\..\..\..\Views\InventoryManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddStockFromAlertButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

