using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Forms;
using GoPOS.UI.Services;
using Microsoft.Win32;
using MessageBox = System.Windows.MessageBox;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for BackupManagerWindow.xaml
    /// </summary>
    public partial class BackupManagerWindow : Window
    {
        private readonly BackupService _backupService;
        private ObservableCollection<BackupInfo> _backups;
        private BackupInfo _selectedBackup;
        private string _backupPath;

        public BackupManagerWindow()
        {
            InitializeComponent();

            // تهيئة خدمة النسخ الاحتياطي
            _backupService = new BackupService();

            // تهيئة مسار النسخ الاحتياطي
            _backupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "GoPOS", "Backup");
            BackupPathTextBox.Text = _backupPath;

            // تحميل النسخ الاحتياطية
            LoadBackups();
        }

        private void LoadBackups()
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                // الحصول على قائمة النسخ الاحتياطية
                List<BackupInfo> backupsList = _backupService.GetAvailableBackups(_backupPath);

                // تحويل القائمة إلى مجموعة قابلة للمراقبة
                _backups = new ObservableCollection<BackupInfo>(backupsList);

                // ربط البيانات بالجدول
                BackupsDataGrid.ItemsSource = _backups;

                // تحديث حالة الأزرار
                UpdateButtonsState();

                // تحديث نص الحالة
                UpdateStatusText();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل النسخ الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private void UpdateButtonsState()
        {
            // تحديث حالة الأزرار بناءً على وجود نسخة احتياطية محددة
            bool hasSelection = _selectedBackup != null;
            RestoreBackupButton.IsEnabled = hasSelection;
            DeleteBackupButton.IsEnabled = hasSelection;
            ViewBackupInfoButton.IsEnabled = hasSelection;
        }

        private void UpdateStatusText()
        {
            // تحديث نص الحالة بناءً على عدد النسخ الاحتياطية
            if (_backups.Count == 0)
            {
                StatusTextBlock.Text = "لا توجد نسخ احتياطية متاحة";
            }
            else
            {
                StatusTextBlock.Text = $"عدد النسخ الاحتياطية: {_backups.Count}";
            }
        }

        private async void CreateBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                // تعطيل الأزرار أثناء العملية
                CreateBackupButton.IsEnabled = false;
                RestoreBackupButton.IsEnabled = false;
                DeleteBackupButton.IsEnabled = false;

                // تحديث نص الحالة
                StatusTextBlock.Text = "جاري إنشاء نسخة احتياطية...";

                // إنشاء نسخة احتياطية
                string backupFilePath = await _backupService.CreateBackupAsync(_backupPath);

                // تحديث قائمة النسخ الاحتياطية
                LoadBackups();

                MessageBox.Show($"تم إنشاء نسخة احتياطية بنجاح في:\n{backupFilePath}", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين الأزرار
                CreateBackupButton.IsEnabled = true;

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private async void RestoreBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedBackup == null)
                    return;

                // طلب تأكيد استعادة النسخة الاحتياطية
                MessageBoxResult result = MessageBox.Show(
                    $"هل أنت متأكد من استعادة النسخة الاحتياطية '{_selectedBackup.FileName}'؟\n\nسيتم استبدال البيانات الحالية بالبيانات من النسخة الاحتياطية.\n\nملاحظة: سيتم إعادة تشغيل التطبيق بعد الاستعادة.",
                    "تأكيد استعادة النسخة الاحتياطية",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return;

                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                // تعطيل الأزرار أثناء العملية
                CreateBackupButton.IsEnabled = false;
                RestoreBackupButton.IsEnabled = false;
                DeleteBackupButton.IsEnabled = false;

                // تحديث نص الحالة
                StatusTextBlock.Text = "جاري استعادة النسخة الاحتياطية...";

                // استعادة النسخة الاحتياطية
                bool success = await _backupService.RestoreBackupAsync(_selectedBackup.FilePath);

                if (success)
                {
                    MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تشغيل التطبيق الآن.", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إغلاق النافذة
                    DialogResult = true;

                    // إعادة تشغيل التطبيق (محاكاة)
                    // في التطبيق الحقيقي، يمكن استخدام System.Windows.Forms.Application.Restart() أو طريقة مماثلة
                    Application.Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين الأزرار
                CreateBackupButton.IsEnabled = true;
                RestoreBackupButton.IsEnabled = _selectedBackup != null;
                DeleteBackupButton.IsEnabled = _selectedBackup != null;

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private void DeleteBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedBackup == null)
                    return;

                // طلب تأكيد حذف النسخة الاحتياطية
                MessageBoxResult result = MessageBox.Show(
                    $"هل أنت متأكد من حذف النسخة الاحتياطية '{_selectedBackup.FileName}'؟",
                    "تأكيد حذف النسخة الاحتياطية",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;

                // حذف ملف النسخة الاحتياطية
                File.Delete(_selectedBackup.FilePath);

                // إزالة النسخة الاحتياطية من القائمة
                _backups.Remove(_selectedBackup);

                // تحديث حالة الأزرار
                UpdateButtonsState();

                // تحديث نص الحالة
                UpdateStatusText();

                MessageBox.Show("تم حذف النسخة الاحتياطية بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // تحديث قائمة النسخ الاحتياطية
            LoadBackups();
        }

        private void BrowsePathButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح مربع حوار لاختيار مجلد النسخ الاحتياطي
                var dialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "اختيار مجلد النسخ الاحتياطي",
                    ShowNewFolderButton = true
                };

                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    // تحديث مسار النسخ الاحتياطي
                    _backupPath = dialog.SelectedPath;
                    BackupPathTextBox.Text = _backupPath;

                    // تحديث قائمة النسخ الاحتياطية
                    LoadBackups();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اختيار مجلد النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BackupsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تحديث النسخة الاحتياطية المحددة
            _selectedBackup = BackupsDataGrid.SelectedItem as BackupInfo;

            // تحديث حالة الأزرار
            UpdateButtonsState();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة
            Close();
        }

        private void ViewBackupInfoButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedBackup == null)
                    return;

                // فتح نافذة معلومات النسخة الاحتياطية
                BackupInfoWindow infoWindow = new BackupInfoWindow(_selectedBackup.FilePath);
                infoWindow.Owner = this;

                bool? result = infoWindow.ShowDialog();

                // إذا تم النقر على زر الاستعادة في نافذة المعلومات
                if (result == true)
                {
                    // استعادة النسخة الاحتياطية
                    RestoreBackup(infoWindow.GetBackupFilePath());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض معلومات النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RestoreBackupMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // استدعاء نفس الإجراء الخاص بزر الاستعادة
            RestoreBackupButton_Click(sender, e);
        }

        private void DeleteBackupMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // استدعاء نفس الإجراء الخاص بزر الحذف
            DeleteBackupButton_Click(sender, e);
        }

        private void ViewBackupInfoMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // استدعاء نفس الإجراء الخاص بزر عرض المعلومات
            ViewBackupInfoButton_Click(sender, e);
        }

        private void CopyBackupMenuItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedBackup == null)
                    return;

                // فتح مربع حوار لاختيار مجلد النسخ
                FolderBrowserDialog dialog = new FolderBrowserDialog
                {
                    Description = "اختيار مجلد لنسخ النسخة الاحتياطية إليه",
                    ShowNewFolderButton = true
                };

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    string destinationPath = Path.Combine(dialog.SelectedPath, _selectedBackup.FileName);

                    // التحقق من وجود الملف في المجلد المستهدف
                    if (File.Exists(destinationPath))
                    {
                        MessageBoxResult result = MessageBox.Show(
                            "يوجد ملف بنفس الاسم في المجلد المستهدف. هل تريد استبداله؟",
                            "تأكيد النسخ",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result != MessageBoxResult.Yes)
                            return;
                    }

                    // نسخ الملف
                    File.Copy(_selectedBackup.FilePath, destinationPath, true);

                    // نسخ ملف المعلومات إذا وجد
                    string infoFilePath = Path.ChangeExtension(_selectedBackup.FilePath, ".info");
                    if (File.Exists(infoFilePath))
                    {
                        string destinationInfoPath = Path.ChangeExtension(destinationPath, ".info");
                        File.Copy(infoFilePath, destinationInfoPath, true);
                    }

                    MessageBox.Show("تم نسخ النسخة الاحتياطية بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء نسخ النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RestoreBackup(string backupFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(backupFilePath) || !File.Exists(backupFilePath))
                {
                    MessageBox.Show("ملف النسخة الاحتياطية غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                // تعطيل الأزرار أثناء العملية
                CreateBackupButton.IsEnabled = false;
                RestoreBackupButton.IsEnabled = false;
                DeleteBackupButton.IsEnabled = false;

                // تحديث نص الحالة
                StatusTextBlock.Text = "جاري استعادة النسخة الاحتياطية...";

                // استعادة النسخة الاحتياطية
                bool success = await _backupService.RestoreBackupAsync(backupFilePath);

                if (success)
                {
                    MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تشغيل التطبيق الآن.", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إغلاق النافذة
                    DialogResult = true;

                    // إعادة تشغيل التطبيق
                    System.Windows.Application.Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تمكين الأزرار
                CreateBackupButton.IsEnabled = true;
                RestoreBackupButton.IsEnabled = _selectedBackup != null;
                DeleteBackupButton.IsEnabled = _selectedBackup != null;

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }
    }
}
