using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        [Required]
        public decimal Price { get; set; }

        public decimal? DiscountPrice { get; set; }

        [Required]
        public int Quantity { get; set; }

        [MaxLength(50)]
        public string Barcode { get; set; }

        public byte[] Image { get; set; }

        [Required]
        public decimal CostPrice { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public bool HasExpiryDate { get; set; }

        public int MinimumQuantity { get; set; }

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; }

        public int CategoryId { get; set; }
        public Category Category { get; set; }

        public int? SupplierId { get; set; }
        public Supplier Supplier { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public ICollection<OrderItem> OrderItems { get; set; }
        public ICollection<InventoryTransaction> InventoryTransactions { get; set; }
    }
}
