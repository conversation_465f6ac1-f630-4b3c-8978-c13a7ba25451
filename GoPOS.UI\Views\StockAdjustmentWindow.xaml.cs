using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for StockAdjustmentWindow.xaml
    /// </summary>
    public partial class StockAdjustmentWindow : Window
    {
        private StockAdjustmentType _adjustmentType;
        private InventoryItemViewModel _selectedProduct;
        private List<StockProductViewModel> _products;
        private int _currentQuantity = 0;
        private int _newQuantity = 0;
        private int _finalQuantity = 0;

        public StockAdjustmentWindow(StockAdjustmentType adjustmentType, InventoryItemViewModel? selectedProduct = null)
        {
            InitializeComponent();
            _adjustmentType = adjustmentType;
            _selectedProduct = selectedProduct;

            // تعيين عنوان النافذة وعنوان النموذج
            if (_adjustmentType == StockAdjustmentType.Add)
            {
                Title = "إضافة مخزون";
                TitleTextBlock.Text = "إضافة مخزون";
                NewQuantityLabel.Text = "الكمية المضافة:";
            }
            else
            {
                Title = "تعديل المخزون";
                TitleTextBlock.Text = "تعديل المخزون";
                NewQuantityLabel.Text = "الكمية الجديدة:";
            }

            LoadProducts();

            // إذا تم تمرير منتج محدد، قم بتحديده تلقائياً
            if (_selectedProduct != null)
            {
                foreach (var product in _products)
                {
                    if (product.Id == _selectedProduct.Id)
                    {
                        ProductComboBox.SelectedItem = product;
                        break;
                    }
                }
            }
        }

        private void LoadProducts()
        {
            // تحميل قائمة المنتجات (سيتم استبدالها بقراءة من قاعدة البيانات)
            _products = new List<StockProductViewModel>();
            Random random = new Random();

            // بيانات تجريبية
            for (int i = 1; i <= 20; i++)
            {
                int categoryIndex = random.Next(0, 3);
                string categoryName = categoryIndex == 0 ? "مشروبات" : (categoryIndex == 1 ? "وجبات سريعة" : "حلويات");

                string productName = GetRandomProductName(categoryIndex);

                _products.Add(new StockProductViewModel
                {
                    Id = i,
                    Barcode = $"100{i:D4}",
                    Name = productName,
                    CategoryName = categoryName,
                    Quantity = random.Next(0, 100),
                    CostPrice = (decimal)Math.Round(random.Next(5, 50) + random.NextDouble(), 2)
                });
            }

            ProductComboBox.ItemsSource = _products;
        }

        private string GetRandomProductName(int categoryIndex)
        {
            string[] drinks = { "قهوة", "شاي", "عصير برتقال", "عصير تفاح", "ماء معدني", "مشروب غازي", "عصير مانجو" };
            string[] fastFood = { "برجر", "بيتزا", "ساندويتش", "دجاج مقلي", "بطاطس", "ناجتس", "شاورما" };
            string[] desserts = { "كيك", "آيس كريم", "كوكيز", "دونات", "تشيز كيك", "بودينج", "كنافة" };

            Random random = new Random();

            if (categoryIndex == 0)
                return drinks[random.Next(drinks.Length)];
            else if (categoryIndex == 1)
                return fastFood[random.Next(fastFood.Length)];
            else
                return desserts[random.Next(desserts.Length)];
        }

        private void ProductComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            StockProductViewModel selectedProduct = ProductComboBox.SelectedItem as StockProductViewModel;
            if (selectedProduct != null)
            {
                BarcodeTextBox.Text = selectedProduct.Barcode;
                _currentQuantity = selectedProduct.Quantity;
                CurrentQuantityTextBox.Text = _currentQuantity.ToString();
                CostPriceTextBox.Text = selectedProduct.CostPrice.ToString("N2");

                // إعادة تعيين الكمية الجديدة والنهائية
                NewQuantityTextBox.Text = _adjustmentType == StockAdjustmentType.Add ? "0" : _currentQuantity.ToString();
                UpdateFinalQuantity();
            }
        }

        private void WarehouseComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
            // مثل تحديث الكمية الحالية بناءً على المخزن المحدد
        }

        private void NewQuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateFinalQuantity();
        }

        private void UpdateFinalQuantity()
        {
            if (int.TryParse(NewQuantityTextBox.Text, out _newQuantity))
            {
                if (_adjustmentType == StockAdjustmentType.Add)
                {
                    _finalQuantity = _currentQuantity + _newQuantity;
                }
                else
                {
                    _finalQuantity = _newQuantity;
                }

                FinalQuantityTextBox.Text = _finalQuantity.ToString();
            }
            else
            {
                FinalQuantityTextBox.Text = _currentQuantity.ToString();
            }
        }

        private void ScanBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            // محاكاة مسح الباركود
            Random random = new Random();
            int productIndex = random.Next(0, _products.Count);
            ProductComboBox.SelectedIndex = productIndex;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة الإدخال
            if (ProductComboBox.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار منتج", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                ProductComboBox.Focus();
                return;
            }

            if (!int.TryParse(NewQuantityTextBox.Text, out int newQuantity) || newQuantity < 0)
            {
                MessageBox.Show("الرجاء إدخال كمية صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                NewQuantityTextBox.Focus();
                return;
            }

            if (_adjustmentType == StockAdjustmentType.Add && newQuantity <= 0)
            {
                MessageBox.Show("الرجاء إدخال كمية أكبر من صفر", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                NewQuantityTextBox.Focus();
                return;
            }

            if (!decimal.TryParse(CostPriceTextBox.Text, out decimal costPrice) || costPrice < 0)
            {
                MessageBox.Show("الرجاء إدخال سعر تكلفة صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                CostPriceTextBox.Focus();
                return;
            }

            // حفظ تعديل المخزون (سيتم استبداله بالحفظ في قاعدة البيانات)
            string actionText = _adjustmentType == StockAdjustmentType.Add ? "إضافة" : "تعديل";
            MessageBox.Show($"تم {actionText} المخزون بنجاح", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    // فئة عرض بيانات المنتج للتعديل
    public class StockProductViewModel
    {
        public int Id { get; set; }
        public required string Barcode { get; set; }
        public required string Name { get; set; }
        public required string CategoryName { get; set; }
        public int Quantity { get; set; }
        public decimal CostPrice { get; set; }
    }
}
