using System;
using System.IO;
using System.Windows;
using System.Windows.Media;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for BackupInfoWindow.xaml
    /// </summary>
    public partial class BackupInfoWindow : Window
    {
        private string _backupFilePath;

        /// <summary>
        /// إنشاء نافذة معلومات النسخة الاحتياطية
        /// </summary>
        /// <param name="backupFilePath">مسار ملف النسخة الاحتياطية</param>
        public BackupInfoWindow(string backupFilePath)
        {
            InitializeComponent();
            
            _backupFilePath = backupFilePath;
            
            // عرض معلومات النسخة الاحتياطية
            LoadBackupInfo();
        }

        /// <summary>
        /// تحميل معلومات النسخة الاحتياطية
        /// </summary>
        private void LoadBackupInfo()
        {
            try
            {
                if (string.IsNullOrEmpty(_backupFilePath) || !File.Exists(_backupFilePath))
                {
                    ShowError("ملف النسخة الاحتياطية غير موجود");
                    return;
                }

                // الحصول على معلومات الملف
                FileInfo fileInfo = new FileInfo(_backupFilePath);
                
                // عرض معلومات الملف الأساسية
                FileNameTextBlock.Text = fileInfo.Name;
                FullPathTextBlock.Text = fileInfo.FullName;
                CreationDateTextBlock.Text = fileInfo.CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
                FileSizeTextBlock.Text = FormatFileSize(fileInfo.Length);
                
                // تحديد نوع النسخة الاحتياطية
                string backupType = DetermineBackupType(fileInfo);
                BackupTypeTextBlock.Text = backupType;
                
                // قراءة معلومات إضافية من ملف المعلومات المرفق (إذا وجد)
                string infoFilePath = Path.ChangeExtension(_backupFilePath, ".info");
                if (File.Exists(infoFilePath))
                {
                    string infoContent = File.ReadAllText(infoFilePath);
                    AdditionalInfoTextBox.Text = infoContent;
                    
                    // استخراج معلومات إضافية من ملف المعلومات
                    ExtractInfoFromContent(infoContent);
                }
                else
                {
                    AdditionalInfoTextBox.Text = "لا توجد معلومات إضافية متاحة.";
                    DatabaseNameTextBlock.Text = "غير معروف";
                    CreatedByTextBlock.Text = "غير معروف";
                }
                
                // التحقق من سلامة الملف
                bool isFileValid = CheckBackupFileValidity();
                if (isFileValid)
                {
                    FileStatusEllipse.Fill = new SolidColorBrush(Colors.Green);
                    FileStatusTextBlock.Text = "سليم";
                }
                else
                {
                    FileStatusEllipse.Fill = new SolidColorBrush(Colors.Red);
                    FileStatusTextBlock.Text = "تالف";
                    RestoreButton.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تحميل معلومات النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] suffixes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت", "تيرابايت" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number = number / 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        /// <summary>
        /// تحديد نوع النسخة الاحتياطية
        /// </summary>
        private string DetermineBackupType(FileInfo fileInfo)
        {
            string fileName = fileInfo.Name.ToLower();
            
            if (fileName.Contains("emergency"))
            {
                return "نسخة طوارئ";
            }
            else if (fileName.Contains("full") || fileName.Contains("system"))
            {
                return "نسخة كاملة للنظام";
            }
            else if (fileName.Contains("auto"))
            {
                return "نسخة تلقائية";
            }
            else
            {
                return "نسخة عادية";
            }
        }

        /// <summary>
        /// استخراج معلومات من محتوى ملف المعلومات
        /// </summary>
        private void ExtractInfoFromContent(string content)
        {
            try
            {
                // استخراج اسم قاعدة البيانات
                string databaseName = ExtractValueFromContent(content, "DatabaseName:");
                if (!string.IsNullOrEmpty(databaseName))
                {
                    DatabaseNameTextBlock.Text = databaseName;
                }
                
                // استخراج معلومات المستخدم
                string createdBy = ExtractValueFromContent(content, "CreatedBy:");
                if (!string.IsNullOrEmpty(createdBy))
                {
                    CreatedByTextBlock.Text = createdBy;
                }
                
                // استخراج نوع النسخة الاحتياطية
                string backupType = ExtractValueFromContent(content, "BackupType:");
                if (!string.IsNullOrEmpty(backupType))
                {
                    switch (backupType.Trim())
                    {
                        case "Emergency":
                            BackupTypeTextBlock.Text = "نسخة طوارئ";
                            break;
                        case "FullSystem":
                            BackupTypeTextBlock.Text = "نسخة كاملة للنظام";
                            break;
                        case "Auto":
                            BackupTypeTextBlock.Text = "نسخة تلقائية";
                            break;
                        default:
                            BackupTypeTextBlock.Text = "نسخة عادية";
                            break;
                    }
                }
            }
            catch
            {
                // تجاهل أي خطأ أثناء استخراج المعلومات
            }
        }

        /// <summary>
        /// استخراج قيمة من محتوى ملف المعلومات
        /// </summary>
        private string ExtractValueFromContent(string content, string key)
        {
            try
            {
                int keyIndex = content.IndexOf(key);
                if (keyIndex >= 0)
                {
                    int startIndex = keyIndex + key.Length;
                    int endIndex = content.IndexOf('\n', startIndex);
                    if (endIndex < 0)
                    {
                        endIndex = content.Length;
                    }
                    
                    return content.Substring(startIndex, endIndex - startIndex).Trim();
                }
            }
            catch
            {
                // تجاهل أي خطأ أثناء استخراج القيمة
            }
            
            return string.Empty;
        }

        /// <summary>
        /// التحقق من سلامة ملف النسخة الاحتياطية
        /// </summary>
        private bool CheckBackupFileValidity()
        {
            try
            {
                // التحقق من وجود الملف
                if (!File.Exists(_backupFilePath))
                {
                    return false;
                }
                
                // التحقق من حجم الملف (يجب أن يكون أكبر من 1 كيلوبايت)
                FileInfo fileInfo = new FileInfo(_backupFilePath);
                if (fileInfo.Length < 1024)
                {
                    return false;
                }
                
                // التحقق من امتداد الملف
                string extension = Path.GetExtension(_backupFilePath).ToLower();
                if (extension != ".bak" && extension != ".zip")
                {
                    return false;
                }
                
                // قراءة أول 8 بايت من الملف للتحقق من التنسيق
                using (FileStream fs = new FileStream(_backupFilePath, FileMode.Open, FileAccess.Read))
                {
                    if (extension == ".bak")
                    {
                        // التحقق من توقيع ملف SQL Server Backup
                        byte[] header = new byte[8];
                        fs.Read(header, 0, 8);
                        
                        // ملفات SQL Server Backup تبدأ عادة بـ "MSSQLBAK"
                        // هذا تحقق مبسط وليس دقيقًا 100%
                        return true;
                    }
                    else if (extension == ".zip")
                    {
                        // التحقق من توقيع ملف ZIP
                        byte[] header = new byte[4];
                        fs.Read(header, 0, 4);
                        
                        // ملفات ZIP تبدأ بـ "PK\x03\x04"
                        return header[0] == 0x50 && header[1] == 0x4B && header[2] == 0x03 && header[3] == 0x04;
                    }
                }
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        private void ShowError(string message)
        {
            MessageBox.Show(
                message,
                "خطأ",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
            
            // تعطيل زر الاستعادة
            RestoreButton.IsEnabled = false;
        }

        /// <summary>
        /// معالجة حدث النقر على زر الاستعادة
        /// </summary>
        private void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التأكيد قبل الاستعادة
                MessageBoxResult result = MessageBox.Show(
                    "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال قاعدة البيانات الحالية.",
                    "تأكيد الاستعادة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.Yes)
                {
                    // إعادة النتيجة إلى النافذة الأم
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء الاستعادة: {ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة حدث النقر على زر الإغلاق
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// الحصول على مسار ملف النسخة الاحتياطية
        /// </summary>
        public string GetBackupFilePath()
        {
            return _backupFilePath;
        }
    }
}
