<Window x:Class="GoPOS.UI.Views.ReturnInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إرجاع فاتورة" 
        Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinWidth="800" MinHeight="600">
    
    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#2196F3"/>
        </Style>
        
        <Style x:Key="FieldLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5,0,5"/>
        </Style>
        
        <Style x:Key="FieldValueStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5,5,0,5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
            <Style.Resources>
                <Style TargetType="Border">
                    <Setter Property="CornerRadius" Value="4"/>
                </Style>
            </Style.Resources>
        </Style>
    </Window.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- رأس الفاتورة المرتجعة -->
        <Border Grid.Row="0" Background="#F44336" Padding="15" CornerRadius="4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="OriginalInvoiceNumberText" Text="إرجاع الفاتورة رقم: INV-001" FontSize="20" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="↩️" FontSize="20" Margin="10,0,0,0" Foreground="White"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="تاريخ الإرجاع: " FontSize="16" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock x:Name="ReturnDateText" Text="2023/05/15" FontSize="16" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- محتوى الفاتورة المرتجعة -->
        <Grid Grid.Row="1" Margin="0,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- معلومات الفاتورة الأصلية -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- معلومات الفاتورة الأصلية -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="معلومات الفاتورة الأصلية" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الفاتورة:" Style="{StaticResource FieldLabelStyle}"/>
                        <TextBlock x:Name="InvoiceNumberValueText" Grid.Row="0" Grid.Column="1" Text="INV-001" Style="{StaticResource FieldValueStyle}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الفاتورة:" Style="{StaticResource FieldLabelStyle}"/>
                        <TextBlock x:Name="InvoiceDateValueText" Grid.Row="1" Grid.Column="1" Text="2023/05/15" Style="{StaticResource FieldValueStyle}"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="طريقة الدفع:" Style="{StaticResource FieldLabelStyle}"/>
                        <TextBlock x:Name="PaymentMethodValueText" Grid.Row="2" Grid.Column="1" Text="نقداً" Style="{StaticResource FieldValueStyle}"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="المجموع:" Style="{StaticResource FieldLabelStyle}"/>
                        <TextBlock x:Name="TotalValueText" Grid.Row="3" Grid.Column="1" Text="172.50" Style="{StaticResource FieldValueStyle}"/>
                    </Grid>
                </StackPanel>
                
                <!-- معلومات العميل -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="معلومات العميل" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم العميل:" Style="{StaticResource FieldLabelStyle}"/>
                        <TextBlock x:Name="CustomerNameValueText" Grid.Row="0" Grid.Column="1" Text="عميل نقدي" Style="{StaticResource FieldValueStyle}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم العميل:" Style="{StaticResource FieldLabelStyle}"/>
                        <TextBlock x:Name="CustomerIdValueText" Grid.Row="1" Grid.Column="1" Text="1" Style="{StaticResource FieldValueStyle}"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="سبب الإرجاع:" Style="{StaticResource FieldLabelStyle}"/>
                        <ComboBox x:Name="ReturnReasonComboBox" Grid.Row="2" Grid.Column="1" Margin="5,5,0,5" SelectedIndex="0">
                            <ComboBoxItem Content="منتج معيب"/>
                            <ComboBoxItem Content="منتج غير مطابق للمواصفات"/>
                            <ComboBoxItem Content="خطأ في الطلب"/>
                            <ComboBoxItem Content="تغيير رأي العميل"/>
                            <ComboBoxItem Content="سبب آخر"/>
                        </ComboBox>
                    </Grid>
                </StackPanel>
            </Grid>
            
            <!-- عناصر الفاتورة للإرجاع -->
            <StackPanel Grid.Row="1" Margin="0,10,0,0">
                <TextBlock Text="عناصر الفاتورة للإرجاع" Style="{StaticResource SectionHeaderStyle}"/>
                
                <DataGrid x:Name="ReturnItemsDataGrid" AutoGenerateColumns="False" 
                          BorderThickness="1" GridLinesVisibility="All" AlternatingRowBackground="#F5F5F5"
                          CanUserSortColumns="True" HeadersVisibility="All" Margin="0,5,0,0">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="#" Binding="{Binding Index}" Width="50" IsReadOnly="True"/>
                        <DataGridTextColumn Header="المنتج" Binding="{Binding ProductName}" Width="*" IsReadOnly="True"/>
                        <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, StringFormat={}{0:N2}}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="الكمية الأصلية" Binding="{Binding OriginalQuantity}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="الكمية المرتجعة" Binding="{Binding ReturnQuantity}" Width="100"/>
                        <DataGridTextColumn Header="المجموع" Binding="{Binding Total, StringFormat={}{0:N2}}" Width="100" IsReadOnly="True"/>
                        <DataGridTemplateColumn Header="سبب الإرجاع" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox ItemsSource="{Binding ReturnReasons}" SelectedItem="{Binding SelectedReason, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Width="140"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>
            
            <!-- ملخص الإرجاع -->
            <Grid Grid.Row="2" Margin="0,10,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>
                
                <!-- ملاحظات الإرجاع -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="ملاحظات الإرجاع" Style="{StaticResource FieldLabelStyle}"/>
                    <TextBox x:Name="ReturnNotesTextBox" Height="80" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
                
                <!-- ملخص المبالغ -->
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="ملخص المبالغ" Style="{StaticResource FieldLabelStyle}"/>
                    <Border Background="#F5F5F5" Padding="10" CornerRadius="4">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي المرتجع:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="TotalReturnValueText" Grid.Row="0" Grid.Column="1" Text="0.00" Style="{StaticResource FieldValueStyle}" FontWeight="Bold"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="الضريبة المرتجعة:" Style="{StaticResource FieldLabelStyle}"/>
                            <TextBlock x:Name="TaxReturnValueText" Grid.Row="1" Grid.Column="1" Text="0.00" Style="{StaticResource FieldValueStyle}"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="طريقة رد المبلغ:" Style="{StaticResource FieldLabelStyle}"/>
                            <ComboBox x:Name="RefundMethodComboBox" Grid.Row="2" Grid.Column="1" Margin="5,5,0,5" SelectedIndex="0">
                                <ComboBoxItem Content="نقداً"/>
                                <ComboBoxItem Content="إعادة للبطاقة الائتمانية"/>
                                <ComboBoxItem Content="رصيد للعميل"/>
                                <ComboBoxItem Content="استبدال بمنتج آخر"/>
                            </ComboBox>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ المسترد:" Style="{StaticResource FieldLabelStyle}" FontWeight="Bold"/>
                            <TextBlock x:Name="RefundAmountValueText" Grid.Row="3" Grid.Column="1" Text="0.00" Style="{StaticResource FieldValueStyle}" FontWeight="Bold" Foreground="#F44336"/>
                        </Grid>
                    </Border>
                </StackPanel>
            </Grid>
        </Grid>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button x:Name="ProcessReturnButton" Content="تنفيذ الإرجاع" Style="{StaticResource ActionButtonStyle}" Background="#F44336" Foreground="White" Click="ProcessReturnButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="↩️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="تنفيذ الإرجاع" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
            
            <Button x:Name="CancelButton" Content="إلغاء" Style="{StaticResource ActionButtonStyle}" Background="#607D8B" Foreground="White" Click="CancelButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
        </StackPanel>
    </Grid>
</Window>
