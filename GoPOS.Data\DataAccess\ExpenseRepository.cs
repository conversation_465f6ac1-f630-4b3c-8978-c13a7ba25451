using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع المنصرفات
    /// </summary>
    public class ExpenseRepository : IRepository<Expense>
    {
        private readonly DatabaseContext _dbContext;

        /// <summary>
        /// إنشاء مستودع المنصرفات
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public ExpenseRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        /// <summary>
        /// الحصول على جميع المنصرفات
        /// </summary>
        /// <returns>قائمة بجميع المنصرفات</returns>
        public async Task<IEnumerable<Expense>> GetAllAsync()
        {
            string query = @"
                SELECT e.*, u.UserName
                FROM Expenses e
                LEFT JOIN Users u ON e.UserId = u.Id
                ORDER BY e.Date DESC";

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query);
            return ConvertDataTableToExpenses(dataTable);
        }

        /// <summary>
        /// الحصول على منصرف بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المنصرف</param>
        /// <returns>المنصرف إذا تم العثور عليه، وإلا فإنه يرجع null</returns>
        public async Task<Expense> GetByIdAsync(int id)
        {
            string query = @"
                SELECT e.*, u.UserName
                FROM Expenses e
                LEFT JOIN Users u ON e.UserId = u.Id
                WHERE e.Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToExpenses(dataTable).FirstOrDefault();
        }

        /// <summary>
        /// إضافة منصرف جديد
        /// </summary>
        /// <param name="entity">المنصرف المراد إضافته</param>
        /// <returns>معرف المنصرف المضاف</returns>
        public async Task<int> AddAsync(Expense entity)
        {
            string query = @"
                INSERT INTO Expenses (Date, Amount, Description, Type, UserId, IsConfirmed, Beneficiary, DocumentNumber)
                VALUES (@Date, @Amount, @Description, @Type, @UserId, @IsConfirmed, @Beneficiary, @DocumentNumber);
                SELECT SCOPE_IDENTITY();";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Date", entity.Date),
                new SqlParameter("@Amount", entity.Amount),
                new SqlParameter("@Description", entity.Description),
                new SqlParameter("@Type", (int)entity.Type),
                new SqlParameter("@UserId", entity.UserId),
                new SqlParameter("@IsConfirmed", entity.IsConfirmed),
                new SqlParameter("@Beneficiary", (object)entity.Beneficiary ?? DBNull.Value),
                new SqlParameter("@DocumentNumber", (object)entity.DocumentNumber ?? DBNull.Value)
            };

            object result = await _dbContext.ExecuteScalarAsync(query, parameters);
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// تحديث منصرف موجود
        /// </summary>
        /// <param name="entity">المنصرف المراد تحديثه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> UpdateAsync(Expense entity)
        {
            string query = @"
                UPDATE Expenses
                SET Date = @Date,
                    Amount = @Amount,
                    Description = @Description,
                    Type = @Type,
                    UserId = @UserId,
                    IsConfirmed = @IsConfirmed,
                    Beneficiary = @Beneficiary,
                    DocumentNumber = @DocumentNumber
                WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", entity.Id),
                new SqlParameter("@Date", entity.Date),
                new SqlParameter("@Amount", entity.Amount),
                new SqlParameter("@Description", entity.Description),
                new SqlParameter("@Type", (int)entity.Type),
                new SqlParameter("@UserId", entity.UserId),
                new SqlParameter("@IsConfirmed", entity.IsConfirmed),
                new SqlParameter("@Beneficiary", (object)entity.Beneficiary ?? DBNull.Value),
                new SqlParameter("@DocumentNumber", (object)entity.DocumentNumber ?? DBNull.Value)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// حذف منصرف بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المنصرف المراد حذفه</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> DeleteAsync(int id)
        {
            string query = "DELETE FROM Expenses WHERE Id = @Id";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Id", id)
            };

            return await _dbContext.ExecuteNonQueryAsync(query, parameters);
        }

        /// <summary>
        /// البحث عن منصرفات بناءً على معايير معينة
        /// </summary>
        /// <param name="predicate">دالة التصفية</param>
        /// <returns>قائمة بالمنصرفات التي تطابق معايير البحث</returns>
        public async Task<IEnumerable<Expense>> FindAsync(Func<Expense, bool> predicate)
        {
            var expenses = await GetAllAsync();
            return expenses.Where(predicate);
        }

        /// <summary>
        /// البحث عن منصرفات بواسطة النص
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة بالمنصرفات التي تطابق نص البحث</returns>
        public async Task<IEnumerable<Expense>> SearchAsync(string searchText)
        {
            string query = @"
                SELECT e.*, u.UserName
                FROM Expenses e
                LEFT JOIN Users u ON e.UserId = u.Id
                WHERE e.Description LIKE @SearchText
                OR e.Beneficiary LIKE @SearchText
                OR e.DocumentNumber LIKE @SearchText
                OR CAST(e.Amount AS NVARCHAR) LIKE @SearchText
                ORDER BY e.Date DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@SearchText", $"%{searchText}%")
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToExpenses(dataTable);
        }

        /// <summary>
        /// البحث عن منصرفات بواسطة النوع
        /// </summary>
        /// <param name="type">نوع المنصرف</param>
        /// <returns>قائمة بالمنصرفات من النوع المحدد</returns>
        public async Task<IEnumerable<Expense>> GetByTypeAsync(ExpenseType type)
        {
            string query = @"
                SELECT e.*, u.UserName
                FROM Expenses e
                LEFT JOIN Users u ON e.UserId = u.Id
                WHERE e.Type = @Type
                ORDER BY e.Date DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Type", (int)type)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToExpenses(dataTable);
        }

        /// <summary>
        /// البحث عن منصرفات بواسطة التاريخ
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>قائمة بالمنصرفات في التاريخ المحدد</returns>
        public async Task<IEnumerable<Expense>> GetByDateAsync(DateTime date)
        {
            string query = @"
                SELECT e.*, u.UserName
                FROM Expenses e
                LEFT JOIN Users u ON e.UserId = u.Id
                WHERE CONVERT(DATE, e.Date) = CONVERT(DATE, @Date)
                ORDER BY e.Date DESC";

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Date", date)
            };

            DataTable dataTable = await _dbContext.ExecuteQueryAsync(query, parameters);
            return ConvertDataTableToExpenses(dataTable);
        }

        /// <summary>
        /// تحويل DataTable إلى قائمة منصرفات
        /// </summary>
        /// <param name="dataTable">جدول البيانات</param>
        /// <returns>قائمة المنصرفات</returns>
        private IEnumerable<Expense> ConvertDataTableToExpenses(DataTable dataTable)
        {
            List<Expense> expenses = new List<Expense>();

            foreach (DataRow row in dataTable.Rows)
            {
                Expense expense = new Expense
                {
                    Id = Convert.ToInt32(row["Id"]),
                    Date = Convert.ToDateTime(row["Date"]),
                    Amount = Convert.ToDecimal(row["Amount"]),
                    Description = row["Description"].ToString(),
                    Type = (ExpenseType)Convert.ToInt32(row["Type"]),
                    UserId = Convert.ToInt32(row["UserId"]),
                    IsConfirmed = Convert.ToBoolean(row["IsConfirmed"]),
                    UserName = row["UserName"]?.ToString()
                };

                if (row["Beneficiary"] != DBNull.Value)
                {
                    expense.Beneficiary = row["Beneficiary"].ToString();
                }

                if (row["DocumentNumber"] != DBNull.Value)
                {
                    expense.DocumentNumber = row["DocumentNumber"].ToString();
                }

                expenses.Add(expense);
            }

            return expenses;
        }
    }
}
