using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using System.Windows.Media.Imaging;
using System.IO;
using System.Printing;
using System.Windows.Input;
using System.Diagnostics;
using GoPOS.UI.Services;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for SettingsView.xaml
    /// </summary>
    public partial class SettingsView : UserControl
    {
        private string _logoPath;
        private readonly BackupService _backupService;

        // إعدادات النسخ الاحتياطي التلقائي
        private bool _enableAutoBackup = true;
        private string _backupFrequency = "Weekly";
        private string _weekDay = "Sunday";
        private int _monthDay = 1;
        private int _backupHour = 22; // 10 PM
        private int _backupMinute = 0;
        private string _backupPath;

        public SettingsView()
        {
            InitializeComponent();

            // تهيئة خدمة النسخ الاحتياطي
            string serverName = "localhost";
            string databaseName = "GoPOS";
            string username = "sa";
            string password = "";
            bool useWindowsAuth = true;

            // قراءة إعدادات قاعدة البيانات من ملف الإعدادات إذا كان موجوداً
            string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GoPOS");
            string settingsPath = Path.Combine(appDataPath, "settings.json");
            if (File.Exists(settingsPath))
            {
                try
                {
                    string settingsJson = File.ReadAllText(settingsPath);
                    // يمكن استخدام مكتبة JSON لقراءة الإعدادات
                    // هنا نستخدم طريقة بسيطة للتوضيح
                    if (settingsJson.Contains("\"ServerName\":"))
                    {
                        int start = settingsJson.IndexOf("\"ServerName\":") + 14;
                        int end = settingsJson.IndexOf("\"", start);
                        serverName = settingsJson.Substring(start, end - start);
                    }

                    if (settingsJson.Contains("\"DatabaseName\":"))
                    {
                        int start = settingsJson.IndexOf("\"DatabaseName\":") + 16;
                        int end = settingsJson.IndexOf("\"", start);
                        databaseName = settingsJson.Substring(start, end - start);
                    }

                    if (settingsJson.Contains("\"UseWindowsAuth\":"))
                    {
                        int start = settingsJson.IndexOf("\"UseWindowsAuth\":") + 17;
                        useWindowsAuth = settingsJson.Substring(start, 5).Contains("true");
                    }

                    if (!useWindowsAuth)
                    {
                        if (settingsJson.Contains("\"Username\":"))
                        {
                            int start = settingsJson.IndexOf("\"Username\":") + 12;
                            int end = settingsJson.IndexOf("\"", start);
                            username = settingsJson.Substring(start, end - start);
                        }

                        if (settingsJson.Contains("\"Password\":"))
                        {
                            int start = settingsJson.IndexOf("\"Password\":") + 12;
                            int end = settingsJson.IndexOf("\"", start);
                            password = settingsJson.Substring(start, end - start);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"حدث خطأ أثناء قراءة ملف الإعدادات: {ex.Message}");
                }
            }

            _backupService = new BackupService(null, serverName, databaseName, username, password, useWindowsAuth);

            // تحميل الإعدادات
            LoadSettings();
            LoadPrinters();
        }

        private void LoadSettings()
        {
            // تحميل الإعدادات المحفوظة (سيتم استبدالها بقراءة من قاعدة البيانات أو ملف الإعدادات)

            // إعدادات عامة
            StoreNameTextBox.Text = "متجر GoPOS";
            StoreAddressTextBox.Text = "الرياض، المملكة العربية السعودية";
            StorePhoneTextBox.Text = "0501234567";
            StoreEmailTextBox.Text = "<EMAIL>";
            StoreTaxNumberTextBox.Text = "*********";

            // إعدادات قاعدة البيانات
            ServerNameTextBox.Text = "localhost";
            DatabaseNameTextBox.Text = "GoPOS";
            DatabaseUserTextBox.Text = "sa";

            // تعيين مسار النسخ الاحتياطي
            _backupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "GoPOS", "Backup");
            BackupPathTextBox.Text = _backupPath;

            // تعيين إعدادات النسخ الاحتياطي التلقائي
            AutoBackupCheckBox.IsChecked = _enableAutoBackup;
            BackupFrequencyComboBox.SelectedIndex = _backupFrequency == "Daily" ? 0 : _backupFrequency == "Weekly" ? 1 : 2;
        }

        private void LoadPrinters()
        {
            try
            {
                // الحصول على قائمة الطابعات المتاحة
                LocalPrintServer printServer = new LocalPrintServer();
                PrintQueueCollection printQueues = printServer.GetPrintQueues();

                List<string> printers = new List<string>();
                foreach (PrintQueue printer in printQueues)
                {
                    printers.Add(printer.Name);
                }

                // إضافة الطابعات إلى القوائم المنسدلة
                ReceiptPrinterComboBox.ItemsSource = printers;
                ReportPrinterComboBox.ItemsSource = printers;

                // اختيار الطابعة الافتراضية
                if (printers.Count > 0)
                {
                    ReceiptPrinterComboBox.SelectedIndex = 0;
                    ReportPrinterComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الطابعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveGeneralSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة الإدخال
            if (string.IsNullOrWhiteSpace(StoreNameTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم المتجر", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                StoreNameTextBox.Focus();
                return;
            }

            // حفظ الإعدادات العامة (سيتم استبدالها بالحفظ في قاعدة البيانات أو ملف الإعدادات)
            MessageBox.Show("تم حفظ الإعدادات العامة بنجاح", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetGeneralSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إعادة تعيين الإعدادات العامة إلى القيم الافتراضية؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // إعادة تعيين الإعدادات العامة إلى القيم الافتراضية
                StoreNameTextBox.Text = "متجر GoPOS";
                StoreAddressTextBox.Text = "الرياض، المملكة العربية السعودية";
                StorePhoneTextBox.Text = "0501234567";
                StoreEmailTextBox.Text = "<EMAIL>";
                StoreTaxNumberTextBox.Text = "*********";
                LanguageComboBox.SelectedIndex = 0;
                ThemeComboBox.SelectedIndex = 0;
                FontSizeComboBox.SelectedIndex = 1;
                CurrencyComboBox.SelectedIndex = 0;
                TaxRateTextBox.Text = "15";
                ShowTaxCheckBox.IsChecked = true;
                ReceiptMessageTextBox.Text = "شكراً لتسوقكم معنا";
            }
        }

        private void SaveDatabaseSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة الإدخال
                if (string.IsNullOrWhiteSpace(ServerNameTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم الخادم", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ServerNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(DatabaseNameTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم قاعدة البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    DatabaseNameTextBox.Focus();
                    return;
                }

                // حفظ إعدادات قاعدة البيانات

                // حفظ إعدادات النسخ الاحتياطي
                _enableAutoBackup = AutoBackupCheckBox.IsChecked == true;
                _backupPath = BackupPathTextBox.Text;

                switch (BackupFrequencyComboBox.SelectedIndex)
                {
                    case 0:
                        _backupFrequency = "Daily";
                        break;
                    case 1:
                        _backupFrequency = "Weekly";
                        break;
                    case 2:
                        _backupFrequency = "Monthly";
                        break;
                }

                // فتح نافذة جدولة النسخ الاحتياطي التلقائي إذا كان مفعلاً
                if (_enableAutoBackup)
                {
                    BackupSchedulerWindow schedulerWindow = new BackupSchedulerWindow(
                        _enableAutoBackup,
                        _backupFrequency,
                        _weekDay,
                        _monthDay,
                        _backupHour,
                        _backupMinute,
                        _backupPath);

                    schedulerWindow.Owner = Window.GetWindow(this);

                    if (schedulerWindow.ShowDialog() == true)
                    {
                        // تحديث إعدادات النسخ الاحتياطي التلقائي
                        _enableAutoBackup = schedulerWindow.EnableAutoBackup;
                        _backupFrequency = schedulerWindow.BackupFrequency;
                        _weekDay = schedulerWindow.WeekDay;
                        _monthDay = schedulerWindow.MonthDay;
                        _backupHour = schedulerWindow.BackupHour;
                        _backupMinute = schedulerWindow.BackupMinute;
                        _backupPath = schedulerWindow.BackupPath;

                        // تحديث واجهة المستخدم
                        AutoBackupCheckBox.IsChecked = _enableAutoBackup;
                        BackupPathTextBox.Text = _backupPath;

                        switch (_backupFrequency)
                        {
                            case "Daily":
                                BackupFrequencyComboBox.SelectedIndex = 0;
                                break;
                            case "Weekly":
                                BackupFrequencyComboBox.SelectedIndex = 1;
                                break;
                            case "Monthly":
                                BackupFrequencyComboBox.SelectedIndex = 2;
                                break;
                        }
                    }
                }

                MessageBox.Show("تم حفظ إعدادات قاعدة البيانات بنجاح", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetDatabaseSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إعادة تعيين إعدادات قاعدة البيانات إلى القيم الافتراضية؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // إعادة تعيين إعدادات قاعدة البيانات إلى القيم الافتراضية
                DatabaseTypeComboBox.SelectedIndex = 0;
                ServerNameTextBox.Text = "localhost";
                DatabaseNameTextBox.Text = "GoPOS";
                DatabaseUserTextBox.Text = "sa";
                DatabasePasswordBox.Password = "";

                // إعادة تعيين إعدادات النسخ الاحتياطي
                _enableAutoBackup = true;
                _backupFrequency = "Weekly";
                _weekDay = "Sunday";
                _monthDay = 1;
                _backupHour = 22;
                _backupMinute = 0;
                _backupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "GoPOS", "Backup");

                // تحديث واجهة المستخدم
                AutoBackupCheckBox.IsChecked = _enableAutoBackup;
                BackupFrequencyComboBox.SelectedIndex = 1; // أسبوعي
                BackupPathTextBox.Text = _backupPath;
            }
        }

        private void SavePrintSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة الإدخال
            if (ReceiptPrinterComboBox.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار طابعة الإيصالات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                ReceiptPrinterComboBox.Focus();
                return;
            }

            // حفظ إعدادات الطباعة (سيتم استبدالها بالحفظ في ملف الإعدادات)
            MessageBox.Show("تم حفظ إعدادات الطباعة بنجاح", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetPrintSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إعادة تعيين إعدادات الطباعة إلى القيم الافتراضية؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // إعادة تعيين إعدادات الطباعة إلى القيم الافتراضية
                if (ReceiptPrinterComboBox.Items.Count > 0)
                    ReceiptPrinterComboBox.SelectedIndex = 0;

                if (ReportPrinterComboBox.Items.Count > 0)
                    ReportPrinterComboBox.SelectedIndex = 0;

                PaperSizeComboBox.SelectedIndex = 0;
                AutoPrintCheckBox.IsChecked = true;
                CopiesComboBox.SelectedIndex = 0;
                PrintLogoCheckBox.IsChecked = true;
                LogoImage.Source = null;
                _logoPath = null;
            }
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة الإدخال
                if (string.IsNullOrWhiteSpace(ServerNameTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم الخادم", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ServerNameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(DatabaseNameTextBox.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم قاعدة البيانات", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    DatabaseNameTextBox.Focus();
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                // تعطيل زر الاختبار
                TestConnectionButton.IsEnabled = false;

                // تحديث معلومات الاتصال
                string serverName = ServerNameTextBox.Text;
                string databaseName = DatabaseNameTextBox.Text;
                string username = DatabaseUserTextBox.Text;
                string password = DatabasePasswordBox.Password;
                bool useWindowsAuth = DatabaseTypeComboBox.SelectedIndex == 0; // الخيار الأول هو Windows Authentication

                // إنشاء خدمة النسخ الاحتياطي بمعلومات الاتصال الجديدة
                BackupService testService = new BackupService(null, serverName, databaseName, username, password, useWindowsAuth);

                // اختبار الاتصال
                bool connectionSuccess = await testService.TestConnectionAsync();

                if (connectionSuccess)
                {
                    // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
                    try
                    {
                        await testService.CreateDatabaseIfNotExistsAsync();
                        MessageBox.Show("تم الاتصال بقاعدة البيانات بنجاح", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"تم الاتصال بالخادم بنجاح، ولكن حدث خطأ أثناء إنشاء قاعدة البيانات: {ex.Message}", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("فشل الاتصال بقاعدة البيانات. الرجاء التحقق من إعدادات الاتصال.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اختبار الاتصال: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Debug.WriteLine($"خطأ في اختبار الاتصال: {ex}");
            }
            finally
            {
                // إعادة تمكين زر الاختبار
                TestConnectionButton.IsEnabled = true;

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private async void BackupNowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود مسار النسخ الاحتياطي
                if (string.IsNullOrWhiteSpace(BackupPathTextBox.Text))
                {
                    MessageBox.Show("الرجاء تحديد مسار النسخ الاحتياطي", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    BackupPathTextBox.Focus();
                    return;
                }

                // التحقق من وجود المسار
                if (!Directory.Exists(BackupPathTextBox.Text))
                {
                    MessageBoxResult result = MessageBox.Show("المسار المحدد غير موجود. هل ترغب في إنشائه؟", "تنبيه", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        Directory.CreateDirectory(BackupPathTextBox.Text);
                    }
                    else
                    {
                        return;
                    }
                }

                // إظهار مؤشر الانتظار
                Cursor = Cursors.Wait;

                // تعطيل الأزرار أثناء العملية
                BackupNowButton.IsEnabled = false;
                RestoreBackupButton.IsEnabled = false;

                // تحديث مسار النسخ الاحتياطي
                _backupPath = BackupPathTextBox.Text;

                // إنشاء نسخة احتياطية باستخدام خدمة النسخ الاحتياطي
                string backupFilePath = await _backupService.CreateBackupAsync(_backupPath);

                MessageBox.Show($"تم إنشاء نسخة احتياطية بنجاح في:\n{backupFilePath}", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (UnauthorizedAccessException)
            {
                MessageBox.Show("ليس لديك صلاحية الوصول إلى المسار المحدد. الرجاء تحديد مسار آخر أو تشغيل البرنامج كمسؤول.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (IOException ex)
            {
                MessageBox.Show($"حدث خطأ في الوصول إلى الملف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                // يمكن إضافة تسجيل الخطأ في ملف السجل هنا
            }
            finally
            {
                // إعادة تمكين الأزرار
                BackupNowButton.IsEnabled = true;
                RestoreBackupButton.IsEnabled = true;

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = Cursors.Arrow;
            }
        }

        private void RestoreBackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إدارة النسخ الاحتياطية
                BackupManagerWindow backupManagerWindow = new BackupManagerWindow();
                backupManagerWindow.Owner = Window.GetWindow(this);
                backupManagerWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة إدارة النسخ الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BrowseBackupPathButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح مربع حوار لاختيار مجلد النسخ الاحتياطي
                var dialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "اختيار مجلد النسخ الاحتياطي",
                    ShowNewFolderButton = true
                };

                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    // تحديث مسار النسخ الاحتياطي
                    BackupPathTextBox.Text = dialog.SelectedPath;
                    _backupPath = dialog.SelectedPath;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اختيار مجلد النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SelectLogoButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح مربع حوار لاختيار صورة الشعار
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "ملفات الصور (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif|جميع الملفات (*.*)|*.*",
                Title = "اختيار شعار المتجر"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _logoPath = openFileDialog.FileName;

                    // عرض الصورة المختارة
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(_logoPath);
                    bitmap.EndInit();
                    LogoImage.Source = bitmap;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تحميل الصورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void TestPrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من اختيار طابعة
                if (ReceiptPrinterComboBox.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار طابعة الإيصالات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ReceiptPrinterComboBox.Focus();
                    return;
                }

                // محاكاة عملية الطباعة
                MessageBox.Show("تم إرسال صفحة اختبار إلى الطابعة", "نجاح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
