﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Windows.Media.Animation;
using System.Windows.Input;
using System.Windows.Media;
using GoPOS.UI.Views;

namespace GoPOS.UI;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer _timer = null!;
    private UserControl _currentView = null!;
    private string _currentUsername = "مدير النظام";
    private int _notificationCount = 0;
    private const string APP_VERSION = "1.0.0";

    public MainWindow()
    {
        try
        {
            InitializeComponent();
            InitializeTimer();
            InitializeEvents();
            InitializeUI();
            LoadDefaultView();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تهيئة النافذة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void InitializeTimer()
    {
        try
        {
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            UpdateDateTime();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تهيئة المؤقت: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void InitializeEvents()
    {
        try
        {
            // لا نحتاج لإضافة معالجات الأحداث لأزرار القائمة الجانبية
            // لأننا أضفناها مباشرة في ملف XAML

            // إضافة معالج حدث إغلاق النافذة
            this.Closing += MainWindow_Closing;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تهيئة الأحداث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void InitializeUI()
    {
        try
        {
            // تعيين معلومات المستخدم
            UserNameText.Text = $"المستخدم: {_currentUsername}";

            // تعيين عدد الإشعارات
            UpdateNotificationCount(_notificationCount);

            // تعيين رقم الإصدار
            VersionText.Text = $"الإصدار: {APP_VERSION}";

            // إظهار رسالة ترحيبية متحركة
            ShowWelcomeMessage();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تهيئة واجهة المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadDefaultView()
    {
        try
        {
            // تحميل لوحة المعلومات كشاشة افتراضية
            LoadView(new DashboardView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل الشاشة الافتراضية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void MenuButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            Button button = sender as Button;
            if (button == null) return;

            string content = button.Content.ToString() ?? string.Empty;

            // تعطيل جميع أزرار القائمة لمنع النقر المتكرر
            foreach (var child in MenuPanel.Children)
            {
                if (child is Button menuButton)
                {
                    menuButton.IsEnabled = false;
                }
            }

            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            try
            {
                switch (content)
                {
                    case "لوحة المعلومات":
                        LoadView(new DashboardView());
                        break;
                    case "نقطة البيع":
                        LoadView(new POSTerminalView());
                        break;
                    case "المنتجات":
                        LoadView(new ProductsManagementView());
                        break;
                    case "المخزون":
                        LoadView(new InventoryManagementView());
                        break;
                    case "الجرد الدوري":
                        LoadView(new InventoryCheckView());
                        break;
                    case "العملاء":
                        LoadView(new CustomersManagementView());
                        break;
                    case "الأقساط":
                        LoadView(new InstallmentsManagementView());
                        break;
                    case "الفواتير":
                        LoadView(new InvoicesView());
                        break;
                    case "المنصرفات":
                        LoadView(new ExpensesView());
                        break;
                    case "التقارير":
                        LoadView(new ReportsView());
                        break;
                    case "الإعدادات":
                        LoadView(new SettingsView());
                        break;
                    default:
                        MessageBox.Show($"الشاشة '{content}' غير معروفة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الشاشة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                // محاولة تحميل لوحة المعلومات كشاشة بديلة
                try
                {
                    LoadView(new DashboardView());
                }
                catch
                {
                    // تجاهل الخطأ إذا فشل تحميل لوحة المعلومات أيضًا
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تبديل الشاشات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة تمكين أزرار القائمة
            Dispatcher.BeginInvoke(new Action(() =>
            {
                foreach (var child in MenuPanel.Children)
                {
                    if (child is Button menuButton)
                    {
                        menuButton.IsEnabled = true;
                    }
                }

                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }), System.Windows.Threading.DispatcherPriority.Background);
        }
    }

    private void LoadView(UserControl view)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            if (view == null)
            {
                throw new ArgumentNullException(nameof(view), "العرض المطلوب تحميله غير موجود");
            }

            // تنظيف الذاكرة إذا كان هناك عرض سابق
            if (_currentView != null)
            {
                // إزالة العرض الحالي
                ContentArea.Children.Clear();

                // محاولة تنظيف الموارد
                try
                {
                    // إذا كان العرض يدعم IDisposable
                    if (_currentView is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                }
                catch
                {
                    // تجاهل أي أخطاء أثناء التنظيف
                }
            }

            // تأخير تحميل العرض الجديد لإعطاء وقت للتنظيف
            Dispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    // تحميل العرض الجديد
                    _currentView = view;
                    ContentArea.Children.Add(view);

                    // تطبيق جامع القمامة
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تحميل العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

                    // محاولة تحميل لوحة المعلومات كعرض بديل
                    try
                    {
                        _currentView = new DashboardView();
                        ContentArea.Children.Add(_currentView);
                    }
                    catch
                    {
                        // إذا فشل كل شيء، عرض رسالة خطأ فقط
                        TextBlock errorMessage = new TextBlock
                        {
                            Text = "حدث خطأ أثناء تحميل الشاشة. الرجاء المحاولة مرة أخرى.",
                            FontSize = 18,
                            HorizontalAlignment = HorizontalAlignment.Center,
                            VerticalAlignment = VerticalAlignment.Center,
                            TextWrapping = TextWrapping.Wrap
                        };
                        ContentArea.Children.Add(errorMessage);
                    }
                }
                finally
                {
                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل العرض: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void Timer_Tick(object sender, EventArgs e)
    {
        UpdateDateTime();
    }

    private void UpdateDateTime()
    {
        DateTimeText.Text = $"التاريخ والوقت: {DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}";
    }

    private void ShowWelcomeMessage()
    {
        try
        {
            // إظهار رسالة الترحيب مع تأثير حركي
            WelcomeMessage.Opacity = 0;
            WelcomeMessage.Visibility = Visibility.Visible;

            DoubleAnimation fadeIn = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = TimeSpan.FromSeconds(1.5)
            };

            WelcomeMessage.BeginAnimation(OpacityProperty, fadeIn);

            // إخفاء رسالة الترحيب بعد 3 ثوانٍ
            DispatcherTimer timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(3)
            };

            timer.Tick += (s, e) =>
            {
                DoubleAnimation fadeOut = new DoubleAnimation
                {
                    From = 1.0,
                    To = 0.0,
                    Duration = TimeSpan.FromSeconds(1)
                };

                fadeOut.Completed += (s2, e2) =>
                {
                    WelcomeMessage.Visibility = Visibility.Collapsed;
                };

                WelcomeMessage.BeginAnimation(OpacityProperty, fadeOut);
                timer.Stop();
            };

            timer.Start();
        }
        catch (Exception ex)
        {
            // تجاهل الأخطاء في إظهار رسالة الترحيب
            Console.WriteLine($"خطأ في إظهار رسالة الترحيب: {ex.Message}");
        }
    }

    private void UpdateNotificationCount(int count)
    {
        _notificationCount = count;
        NotificationsText.Text = $"الإشعارات: {_notificationCount}";

        // تغيير لون النص إذا كان هناك إشعارات
        if (_notificationCount > 0)
        {
            NotificationsText.Foreground = new SolidColorBrush(Colors.Red);
            NotificationsText.FontWeight = FontWeights.Bold;
        }
        else
        {
            NotificationsText.Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)); // #2196F3
            NotificationsText.FontWeight = FontWeights.Normal;
        }
    }

    private void Notifications_MouseDown(object sender, MouseButtonEventArgs e)
    {
        if (_notificationCount > 0)
        {
            MessageBox.Show($"لديك {_notificationCount} إشعارات جديدة", "الإشعارات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            MessageBox.Show("لا توجد إشعارات جديدة", "الإشعارات", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        try
        {
            MessageBoxResult result = MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.No)
            {
                e.Cancel = true;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء إغلاق النافذة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    #region معالجات أحداث القائمة العلوية

    private void LogoutMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBoxResult result = MessageBox.Show("هل تريد تسجيل الخروج من النظام؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                // هنا يمكن إضافة كود تسجيل الخروج وإعادة تشغيل شاشة تسجيل الدخول
                MessageBox.Show("تم تسجيل الخروج بنجاح", "تسجيل الخروج", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تسجيل الخروج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ExitMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBoxResult result = MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء الخروج من النظام: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ManageProductsMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new ProductsManagementView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة إدارة المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void ManageCategoriesMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new CategoriesManagementView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة إدارة الفئات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void POSMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new POSTerminalView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة نقطة البيع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void InvoicesMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new InvoicesView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة الفواتير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void ExpensesMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new ExpensesView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة المنصرفات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void ManageCustomersMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new CustomersManagementView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة إدارة العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void SalesReportMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            // تحميل شاشة التقارير مع تحديد تقرير المبيعات
            ReportsView reportsView = new ReportsView();
            LoadView(reportsView);

            // تحديد تقرير المبيعات في القائمة
            if (reportsView.ReportsListBox.Items.Count > 0)
            {
                reportsView.ReportsListBox.SelectedIndex = 0; // تقرير المبيعات هو العنصر الأول
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح تقرير المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void InventoryReportMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            // تحميل شاشة التقارير مع تحديد تقرير المخزون
            ReportsView reportsView = new ReportsView();
            LoadView(reportsView);

            // تحديد تقرير المخزون في القائمة
            if (reportsView.ReportsListBox.Items.Count > 1)
            {
                reportsView.ReportsListBox.SelectedIndex = 1; // تقرير المخزون هو العنصر الثاني
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void ManageUsersMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new UsersManagementView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة إدارة المستخدمين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void SystemSettingsMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            LoadView(new SettingsView());
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح شاشة إعدادات النظام: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void ExportImportMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إظهار مؤشر الانتظار
            Cursor = System.Windows.Input.Cursors.Wait;

            // فتح نافذة تصدير واستيراد البيانات
            DataExportImportWindow exportImportWindow = new DataExportImportWindow();
            exportImportWindow.Owner = this;
            exportImportWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح نافذة تصدير واستيراد البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // إعادة المؤشر إلى الوضع الطبيعي
            Cursor = System.Windows.Input.Cursors.Arrow;
        }
    }

    private void AboutMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            AboutWindow aboutWindow = new AboutWindow();
            aboutWindow.Owner = this;
            aboutWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء عرض معلومات البرنامج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void HelpMenuItem_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("مرحباً بك في نظام نقاط البيع GoPOS\nتصميم وتطوير: GoLx\n\nللمساعدة والدعم الفني، يرجى التواصل مع فريق الدعم", "المساعدة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء عرض المساعدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    #endregion
}