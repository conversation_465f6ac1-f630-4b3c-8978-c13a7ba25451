﻿#pragma checksum "..\..\..\..\Views\POSTerminalView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "981B31D80BCCB3144385E1F99B4CBB0C63FA1F8A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// POSTerminalView
    /// </summary>
    public partial class POSTerminalView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 35 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BarcodeButton;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuickBarcodeButton;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CategoriesPanel;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel ProductsPanel;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoProductsMessage;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadingIndicator;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerComboBox;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView CartListView;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmptyCartMessage;
        
        #line default
        #line hidden
        
        
        #line 385 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtotalTextBlock;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountTextBox;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DiscountTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxTextBlock;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTextBlock;
        
        #line default
        #line hidden
        
        
        #line 450 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PayCashButton;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PayCardButton;
        
        #line default
        #line hidden
        
        
        #line 488 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InstallmentButton;
        
        #line default
        #line hidden
        
        
        #line 514 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveDraftButton;
        
        #line default
        #line hidden
        
        
        #line 531 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WalletPayButton;
        
        #line default
        #line hidden
        
        
        #line 548 "..\..\..\..\Views\POSTerminalView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/posterminalview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\POSTerminalView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 40 "..\..\..\..\Views\POSTerminalView.xaml"
            this.SearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.SearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\Views\POSTerminalView.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BarcodeButton = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\..\Views\POSTerminalView.xaml"
            this.BarcodeButton.Click += new System.Windows.RoutedEventHandler(this.BarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.QuickBarcodeButton = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\..\Views\POSTerminalView.xaml"
            this.QuickBarcodeButton.Click += new System.Windows.RoutedEventHandler(this.QuickBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CategoriesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            
            #line 142 "..\..\..\..\Views\POSTerminalView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CategoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ProductsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 8:
            this.NoProductsMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LoadingIndicator = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.CustomerComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            
            #line 233 "..\..\..\..\Views\POSTerminalView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCustomerButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CartListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 16:
            this.EmptyCartMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SubtotalTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.DiscountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 402 "..\..\..\..\Views\POSTerminalView.xaml"
            this.DiscountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.DiscountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.DiscountTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 410 "..\..\..\..\Views\POSTerminalView.xaml"
            this.DiscountTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DiscountTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TaxTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.TotalTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.PayCashButton = ((System.Windows.Controls.Button)(target));
            
            #line 458 "..\..\..\..\Views\POSTerminalView.xaml"
            this.PayCashButton.Click += new System.Windows.RoutedEventHandler(this.PayCashButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.PayCardButton = ((System.Windows.Controls.Button)(target));
            
            #line 477 "..\..\..\..\Views\POSTerminalView.xaml"
            this.PayCardButton.Click += new System.Windows.RoutedEventHandler(this.PayCardButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.InstallmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 496 "..\..\..\..\Views\POSTerminalView.xaml"
            this.InstallmentButton.Click += new System.Windows.RoutedEventHandler(this.InstallmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.SaveDraftButton = ((System.Windows.Controls.Button)(target));
            
            #line 520 "..\..\..\..\Views\POSTerminalView.xaml"
            this.SaveDraftButton.Click += new System.Windows.RoutedEventHandler(this.SaveDraftButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.WalletPayButton = ((System.Windows.Controls.Button)(target));
            
            #line 537 "..\..\..\..\Views\POSTerminalView.xaml"
            this.WalletPayButton.Click += new System.Windows.RoutedEventHandler(this.WalletPayButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 554 "..\..\..\..\Views\POSTerminalView.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 571 "..\..\..\..\Views\POSTerminalView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveDraftButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 293 "..\..\..\..\Views\POSTerminalView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DecreaseQuantityButton_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 315 "..\..\..\..\Views\POSTerminalView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.IncreaseQuantityButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 344 "..\..\..\..\Views\POSTerminalView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveItemButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

