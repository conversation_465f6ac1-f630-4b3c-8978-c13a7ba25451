using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;
using System.Data.SqlClient;
using System.Data;
using System.Text;
using System.Diagnostics;
using GoPOS.UI.Models;

namespace GoPOS.UI.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي واستعادة البيانات
    /// </summary>
    public class BackupService
    {
        private readonly string _defaultBackupPath;
        private readonly string _databaseName;
        private readonly string _appDataPath;
        private readonly string _connectionString;
        private readonly string _serverName;
        private readonly string _username;
        private readonly string _password;
        private readonly bool _useWindowsAuth;

        public BackupService(string connectionString = null, string serverName = null, string databaseName = null,
                            string username = null, string password = null, bool useWindowsAuth = true)
        {
            // تهيئة المسارات الافتراضية
            _defaultBackupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "GoPOS", "Backup");
            _appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GoPOS");

            // تعيين معلومات قاعدة البيانات
            _databaseName = databaseName ?? "GoPOS";
            _serverName = serverName ?? "localhost";
            _username = username ?? "sa";
            _password = password ?? "";
            _useWindowsAuth = useWindowsAuth;

            // إنشاء سلسلة الاتصال إذا لم يتم توفيرها
            if (string.IsNullOrEmpty(connectionString))
            {
                if (_useWindowsAuth)
                {
                    _connectionString = $"Server={_serverName};Database={_databaseName};Integrated Security=True;";
                }
                else
                {
                    _connectionString = $"Server={_serverName};Database={_databaseName};User Id={_username};Password={_password};";
                }
            }
            else
            {
                _connectionString = connectionString;
            }

            // التأكد من وجود المسارات
            EnsureDirectoriesExist();
        }

        /// <summary>
        /// التأكد من وجود المسارات اللازمة
        /// </summary>
        private void EnsureDirectoriesExist()
        {
            try
            {
                if (!Directory.Exists(_defaultBackupPath))
                {
                    Directory.CreateDirectory(_defaultBackupPath);
                }

                if (!Directory.Exists(_appDataPath))
                {
                    Directory.CreateDirectory(_appDataPath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء المسارات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <param name="includeSettings">تضمين إعدادات النظام</param>
        /// <returns>مسار ملف النسخة الاحتياطية</returns>
        public async Task<string> CreateBackupAsync(string backupPath = null, bool includeSettings = true)
        {
            try
            {
                // استخدام المسار الافتراضي إذا لم يتم تحديد مسار
                string targetPath = string.IsNullOrWhiteSpace(backupPath) ? _defaultBackupPath : backupPath;

                // التأكد من وجود المسار
                if (!Directory.Exists(targetPath))
                {
                    Directory.CreateDirectory(targetPath);
                }

                // إنشاء اسم ملف النسخة الاحتياطية
                string backupFileName = $"GoPOS_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.gopos";
                string backupFilePath = Path.Combine(targetPath, backupFileName);

                // إنشاء مجلد مؤقت للنسخة الاحتياطية
                string tempBackupDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                Directory.CreateDirectory(tempBackupDir);

                try
                {
                    // إنشاء نسخة احتياطية من قاعدة البيانات
                    string dbBackupPath = await CreateSqlBackupAsync(tempBackupDir);

                    // نسخ ملفات الإعدادات إذا تم تحديد ذلك
                    if (includeSettings)
                    {
                        await Task.Run(() => CopySettingsFiles(tempBackupDir));
                    }

                    // إنشاء ملف معلومات النسخة الاحتياطية
                    await CreateBackupInfoFileAsync(tempBackupDir, includeSettings);

                    // ضغط الملفات في ملف واحد
                    await Task.Run(() => ZipDirectory(tempBackupDir, backupFilePath));

                    return backupFilePath;
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempBackupDir))
                    {
                        Directory.Delete(tempBackupDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة بيانات SQL Server
        /// </summary>
        /// <param name="tempDir">المجلد المؤقت</param>
        /// <returns>مسار ملف النسخة الاحتياطية</returns>
        private async Task<string> CreateSqlBackupAsync(string tempDir)
        {
            // إنشاء مسار ملف النسخة الاحتياطية
            string dbBackupPath = Path.Combine(tempDir, $"{_databaseName}.bak");

            // إنشاء أمر SQL لإنشاء نسخة احتياطية
            string backupQuery = $@"
                BACKUP DATABASE [{_databaseName}]
                TO DISK = N'{dbBackupPath}'
                WITH NOFORMAT, NOINIT, NAME = N'{_databaseName} Backup',
                SKIP, NOREWIND, NOUNLOAD, STATS = 10";

            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand command = new SqlCommand(backupQuery, connection))
                {
                    command.CommandTimeout = 300; // 5 دقائق
                    await command.ExecuteNonQueryAsync();
                }
            }

            return dbBackupPath;
        }

        /// <summary>
        /// إنشاء ملف معلومات النسخة الاحتياطية
        /// </summary>
        /// <param name="tempDir">المجلد المؤقت</param>
        /// <param name="includeSettings">تضمين إعدادات النظام</param>
        private async Task CreateBackupInfoFileAsync(string tempDir, bool includeSettings)
        {
            // إنشاء ملف معلومات النسخة الاحتياطية
            string infoPath = Path.Combine(tempDir, "backup_info.json");
            string infoContent = $@"{{
  ""DatabaseName"": ""{_databaseName}"",
  ""ServerName"": ""{_serverName}"",
  ""BackupDate"": ""{DateTime.Now:yyyy-MM-dd HH:mm:ss}"",
  ""Version"": ""1.0.0"",
  ""IncludesSettings"": {includeSettings.ToString().ToLower()},
  ""UseWindowsAuth"": {_useWindowsAuth.ToString().ToLower()}
}}";
            await File.WriteAllTextAsync(infoPath, infoContent);
        }

        /// <summary>
        /// استعادة النسخة الاحتياطية
        /// </summary>
        /// <param name="backupFilePath">مسار ملف النسخة الاحتياطية</param>
        /// <param name="restoreSettings">استعادة إعدادات النظام</param>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> RestoreBackupAsync(string backupFilePath, bool restoreSettings = true)
        {
            try
            {
                // التحقق من وجود ملف النسخة الاحتياطية
                if (!File.Exists(backupFilePath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود", backupFilePath);
                }

                // إنشاء مجلد مؤقت لاستخراج النسخة الاحتياطية
                string tempRestoreDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                Directory.CreateDirectory(tempRestoreDir);

                try
                {
                    // استخراج ملفات النسخة الاحتياطية
                    await Task.Run(() => ExtractZipFile(backupFilePath, tempRestoreDir));

                    // التحقق من صحة النسخة الاحتياطية
                    if (!ValidateBackup(tempRestoreDir))
                    {
                        throw new Exception("النسخة الاحتياطية غير صالحة أو تالفة");
                    }

                    // استعادة قاعدة البيانات
                    await RestoreSqlDatabaseAsync(tempRestoreDir);

                    // استعادة ملفات الإعدادات إذا تم تحديد ذلك
                    if (restoreSettings)
                    {
                        await Task.Run(() => RestoreSettingsFiles(tempRestoreDir));
                    }

                    return true;
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempRestoreDir))
                    {
                        Directory.Delete(tempRestoreDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// استعادة قاعدة بيانات SQL Server
        /// </summary>
        /// <param name="tempDir">المجلد المؤقت</param>
        private async Task RestoreSqlDatabaseAsync(string tempDir)
        {
            // التحقق من وجود ملف قاعدة البيانات
            string dbBackupPath = Path.Combine(tempDir, $"{_databaseName}.bak");
            if (!File.Exists(dbBackupPath))
            {
                throw new FileNotFoundException("ملف قاعدة البيانات غير موجود في النسخة الاحتياطية", dbBackupPath);
            }

            // الحصول على مسارات ملفات قاعدة البيانات
            string dataFilePath = string.Empty;
            string logFilePath = string.Empty;

            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                await connection.OpenAsync();

                // الحصول على مسارات ملفات قاعدة البيانات الحالية
                string filePathsQuery = $@"
                    SELECT physical_name FROM sys.master_files
                    WHERE database_id = DB_ID('{_databaseName}')
                    ORDER BY file_id";

                using (SqlCommand command = new SqlCommand(filePathsQuery, connection))
                {
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            dataFilePath = reader.GetString(0);
                        }

                        if (await reader.ReadAsync())
                        {
                            logFilePath = reader.GetString(0);
                        }
                    }
                }

                // إغلاق الاتصالات الحالية بقاعدة البيانات
                string setOfflineQuery = $@"
                    USE [master];
                    ALTER DATABASE [{_databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;";

                using (SqlCommand command = new SqlCommand(setOfflineQuery, connection))
                {
                    await command.ExecuteNonQueryAsync();
                }

                // استعادة قاعدة البيانات
                string restoreQuery = $@"
                    RESTORE DATABASE [{_databaseName}]
                    FROM DISK = N'{dbBackupPath}'
                    WITH FILE = 1,
                    MOVE N'{_databaseName}' TO N'{dataFilePath}',
                    MOVE N'{_databaseName}_log' TO N'{logFilePath}',
                    NOUNLOAD, REPLACE, STATS = 10;

                    ALTER DATABASE [{_databaseName}] SET MULTI_USER;";

                using (SqlCommand command = new SqlCommand(restoreQuery, connection))
                {
                    command.CommandTimeout = 600; // 10 دقائق
                    await command.ExecuteNonQueryAsync();
                }
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية المتاحة
        /// </summary>
        /// <param name="backupPath">مسار النسخ الاحتياطية</param>
        /// <returns>قائمة بملفات النسخ الاحتياطية</returns>
        public List<BackupInfo> GetAvailableBackups(string backupPath = null)
        {
            try
            {
                // استخدام المسار الافتراضي إذا لم يتم تحديد مسار
                string targetPath = string.IsNullOrWhiteSpace(backupPath) ? _defaultBackupPath : backupPath;

                // التأكد من وجود المسار
                if (!Directory.Exists(targetPath))
                {
                    return new List<BackupInfo>();
                }

                // البحث عن ملفات النسخ الاحتياطية
                string[] backupFiles = Directory.GetFiles(targetPath, "*.gopos");

                // إنشاء قائمة بمعلومات النسخ الاحتياطية
                List<BackupInfo> backupInfos = new List<BackupInfo>();
                foreach (string file in backupFiles)
                {
                    backupInfos.Add(BackupInfo.FromFile(file));
                }

                // ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء (من الأحدث إلى الأقدم)
                return backupInfos.OrderByDescending(b => b.CreationDate).ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء قراءة النسخ الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return new List<BackupInfo>();
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>نجاح الاتصال</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء قاعدة بيانات جديدة إذا لم تكن موجودة
        /// </summary>
        /// <returns>نجاح العملية</returns>
        public async Task<bool> CreateDatabaseIfNotExistsAsync()
        {
            try
            {
                // إنشاء سلسلة اتصال بقاعدة بيانات master
                string masterConnectionString;
                if (_useWindowsAuth)
                {
                    masterConnectionString = $"Server={_serverName};Database=master;Integrated Security=True;";
                }
                else
                {
                    masterConnectionString = $"Server={_serverName};Database=master;User Id={_username};Password={_password};";
                }

                using (SqlConnection connection = new SqlConnection(masterConnectionString))
                {
                    await connection.OpenAsync();

                    // التحقق من وجود قاعدة البيانات
                    string checkDbQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{_databaseName}'";
                    using (SqlCommand command = new SqlCommand(checkDbQuery, connection))
                    {
                        int dbCount = (int)await command.ExecuteScalarAsync();
                        if (dbCount > 0)
                        {
                            // قاعدة البيانات موجودة بالفعل
                            return true;
                        }
                    }

                    // إنشاء قاعدة البيانات
                    string createDbQuery = $"CREATE DATABASE [{_databaseName}]";
                    using (SqlCommand command = new SqlCommand(createDbQuery, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء إنشاء قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// نسخ ملفات الإعدادات
        /// </summary>
        /// <param name="tempDir">المجلد المؤقت</param>
        private void CopySettingsFiles(string tempDir)
        {
            try
            {
                // إنشاء مجلد للإعدادات
                string settingsDir = Path.Combine(tempDir, "Settings");
                Directory.CreateDirectory(settingsDir);

                // نسخ ملفات الإعدادات من مجلد التطبيق
                string appSettingsPath = Path.Combine(_appDataPath, "settings.json");
                if (File.Exists(appSettingsPath))
                {
                    File.Copy(appSettingsPath, Path.Combine(settingsDir, "settings.json"), true);
                }
                else
                {
                    // إنشاء ملف إعدادات افتراضي إذا لم يكن موجوداً
                    string settingsContent = $@"{{
  ""StoreName"": ""متجر GoPOS"",
  ""StoreAddress"": ""الرياض، المملكة العربية السعودية"",
  ""StorePhone"": ""0501234567"",
  ""StoreEmail"": ""<EMAIL>"",
  ""StoreTaxNumber"": ""*********"",
  ""Language"": ""ar"",
  ""Theme"": ""Light"",
  ""FontSize"": ""Medium"",
  ""Currency"": ""SAR"",
  ""TaxRate"": 15,
  ""ShowTax"": true,
  ""ReceiptMessage"": ""شكراً لتسوقكم معنا""
}}";
                    File.WriteAllText(Path.Combine(settingsDir, "settings.json"), settingsContent);
                }

                // نسخ ملفات أخرى مثل الشعار وإعدادات الطباعة
                string logoPath = Path.Combine(_appDataPath, "logo.png");
                if (File.Exists(logoPath))
                {
                    File.Copy(logoPath, Path.Combine(settingsDir, "logo.png"), true);
                }

                string printSettingsPath = Path.Combine(_appDataPath, "print_settings.json");
                if (File.Exists(printSettingsPath))
                {
                    File.Copy(printSettingsPath, Path.Combine(settingsDir, "print_settings.json"), true);
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ ولكن عدم إيقاف العملية
                Debug.WriteLine($"حدث خطأ أثناء نسخ ملفات الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// استعادة ملفات الإعدادات
        /// </summary>
        /// <param name="tempDir">المجلد المؤقت</param>
        private void RestoreSettingsFiles(string tempDir)
        {
            try
            {
                // التحقق من وجود مجلد الإعدادات
                string settingsDir = Path.Combine(tempDir, "Settings");
                if (!Directory.Exists(settingsDir))
                {
                    return;
                }

                // التأكد من وجود مجلد التطبيق
                if (!Directory.Exists(_appDataPath))
                {
                    Directory.CreateDirectory(_appDataPath);
                }

                // استعادة ملف الإعدادات
                string settingsPath = Path.Combine(settingsDir, "settings.json");
                if (File.Exists(settingsPath))
                {
                    File.Copy(settingsPath, Path.Combine(_appDataPath, "settings.json"), true);
                }

                // استعادة ملفات أخرى
                string logoPath = Path.Combine(settingsDir, "logo.png");
                if (File.Exists(logoPath))
                {
                    File.Copy(logoPath, Path.Combine(_appDataPath, "logo.png"), true);
                }

                string printSettingsPath = Path.Combine(settingsDir, "print_settings.json");
                if (File.Exists(printSettingsPath))
                {
                    File.Copy(printSettingsPath, Path.Combine(_appDataPath, "print_settings.json"), true);
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ ولكن عدم إيقاف العملية
                Debug.WriteLine($"حدث خطأ أثناء استعادة ملفات الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة النسخة الاحتياطية
        /// </summary>
        /// <param name="tempDir">المجلد المؤقت</param>
        /// <returns>صحة النسخة الاحتياطية</returns>
        private bool ValidateBackup(string tempDir)
        {
            // التحقق من وجود ملف معلومات النسخة الاحتياطية
            string infoPath = Path.Combine(tempDir, "backup_info.json");
            if (!File.Exists(infoPath))
            {
                return false;
            }

            // التحقق من وجود ملف قاعدة البيانات
            string dbBackupPath = Path.Combine(tempDir, $"{_databaseName}.bak");
            if (!File.Exists(dbBackupPath))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// ضغط مجلد في ملف ZIP
        /// </summary>
        /// <param name="sourceDir">المجلد المصدر</param>
        /// <param name="destinationFile">ملف الوجهة</param>
        private void ZipDirectory(string sourceDir, string destinationFile)
        {
            ZipFile.CreateFromDirectory(sourceDir, destinationFile);
        }

        /// <summary>
        /// استخراج ملف ZIP إلى مجلد
        /// </summary>
        /// <param name="sourceFile">ملف المصدر</param>
        /// <param name="destinationDir">مجلد الوجهة</param>
        private void ExtractZipFile(string sourceFile, string destinationDir)
        {
            ZipFile.ExtractToDirectory(sourceFile, destinationDir);
        }
    }

    // تم نقل فئة BackupInfo إلى GoPOS.UI.Models.BackupInfo
}
