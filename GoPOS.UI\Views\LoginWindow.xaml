<Window x:Class="GoPOS.UI.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تسجيل الدخول - GoPOS | تصميم GoLx" Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- الشعار والعنوان -->
        <StackPanel Grid.Row="0" Margin="20">
            <TextBlock Text="نظام نقاط البيع GoPOS"
                       FontSize="24" FontWeight="Bold"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="تصميم وتطوير: GoLx"
                       FontSize="14" FontWeight="SemiBold"
                       HorizontalAlignment="Center"
                       Foreground="#2196F3"
                       Margin="0,2,0,0"/>
            <TextBlock Text="تسجيل الدخول"
                       FontSize="18"
                       HorizontalAlignment="Center"
                       Margin="0,8,0,0"/>
        </StackPanel>

        <!-- نموذج تسجيل الدخول -->
        <Border Grid.Row="1"
                Background="#F5F5F5"
                CornerRadius="8"
                Margin="40,0,40,20"
                Padding="20">
            <StackPanel VerticalAlignment="Center">
                <TextBlock Text="اسم المستخدم:" Margin="0,0,0,5" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4">
                    <TextBox x:Name="UsernameTextBox"
                             Height="30"
                             Padding="8"
                             Margin="0"
                             BorderThickness="0"
                             FontSize="14"/>
                </Border>

                <TextBlock Text="كلمة المرور:" Margin="0,15,0,5" FontWeight="SemiBold"/>
                <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="4">
                    <PasswordBox x:Name="PasswordBox"
                                 Height="30"
                                 Padding="8"
                                 Margin="0"
                                 BorderThickness="0"
                                 FontSize="14"/>
                </Border>

                <Grid Margin="0,10,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <CheckBox x:Name="RememberMeCheckBox"
                              Content="تذكرني"
                              Grid.Column="0"
                              VerticalAlignment="Center"/>

                    <TextBlock Text="نسيت كلمة المرور؟"
                               Grid.Column="1"
                               Foreground="#2196F3"
                               Cursor="Hand"
                               TextDecorations="Underline"
                               VerticalAlignment="Center"
                               MouseDown="ForgotPassword_MouseDown"/>
                </Grid>

                <Button x:Name="LoginButton"
                        Content="تسجيل الدخول"
                        Height="40"
                        Background="#2196F3"
                        Foreground="White"
                        FontSize="14"
                        FontWeight="Bold"
                        Click="LoginButton_Click">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </Button.Resources>
                </Button>

                <TextBlock x:Name="ErrorMessageText"
                           Foreground="Red"
                           TextWrapping="Wrap"
                           Margin="0,10,0,0"
                           TextAlignment="Center"
                           Visibility="Collapsed"
                           FontWeight="SemiBold"/>
            </StackPanel>
        </Border>

        <!-- الشريط السفلي -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- زر وضع الصيانة -->
            <Button x:Name="MaintenanceModeButton"
                    Grid.Column="0"
                    Content="وضع الصيانة"
                    Margin="10,0,0,0"
                    Padding="8,3"
                    Background="Transparent"
                    BorderBrush="Transparent"
                    Foreground="#666666"
                    FontSize="11"
                    Click="MaintenanceModeButton_Click"/>

            <!-- حقوق النشر -->
            <TextBlock Grid.Column="1"
                       Text="© 2024 GoPOS - تصميم GoLx - جميع الحقوق محفوظة"
                       HorizontalAlignment="Center"
                       FontSize="11"
                       Foreground="#666666"/>

            <!-- رقم الإصدار -->
            <TextBlock Grid.Column="2"
                       Text="الإصدار 1.0.0"
                       Margin="0,0,10,0"
                       Foreground="#666666"
                       FontSize="11"
                       HorizontalAlignment="Right"/>
        </Grid>
    </Grid>
</Window>
