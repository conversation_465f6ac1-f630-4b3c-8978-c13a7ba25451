using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using GoPOS.Core.Models;
using GoPOS.UI.Services;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for ReturnInvoiceWindow.xaml
    /// </summary>
    public partial class ReturnInvoiceWindow : Window
    {
        private InvoiceViewModel _originalInvoice;
        private ObservableCollection<ReturnItemViewModel> _returnItems;
        private decimal _totalReturnAmount = 0;
        private decimal _taxReturnAmount = 0;
        private decimal _refundAmount = 0;

        public InvoiceViewModel ReturnInvoice { get; private set; }

        public ReturnInvoiceWindow(InvoiceViewModel originalInvoice)
        {
            InitializeComponent();
            _originalInvoice = originalInvoice;
            LoadOriginalInvoiceData();
            InitializeReturnItems();
            UpdateTotals();
        }

        private void LoadOriginalInvoiceData()
        {
            try
            {
                // عرض معلومات الفاتورة الأصلية
                OriginalInvoiceNumberText.Text = $"إرجاع الفاتورة رقم: {_originalInvoice.InvoiceNumber}";
                ReturnDateText.Text = DateTime.Now.ToString("yyyy/MM/dd");

                InvoiceNumberValueText.Text = _originalInvoice.InvoiceNumber;
                InvoiceDateValueText.Text = _originalInvoice.Date.ToString("yyyy/MM/dd");
                PaymentMethodValueText.Text = _originalInvoice.PaymentMethodText;
                TotalValueText.Text = _originalInvoice.Total.ToString("N2");

                CustomerNameValueText.Text = _originalInvoice.CustomerName;
                CustomerIdValueText.Text = _originalInvoice.CustomerId.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات الفاتورة الأصلية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeReturnItems()
        {
            try
            {
                // تهيئة قائمة العناصر المرتجعة
                _returnItems = new ObservableCollection<ReturnItemViewModel>();

                // الحصول على عناصر الفاتورة الأصلية (محاكاة)
                List<InvoiceItemViewModel> originalItems = GetOriginalInvoiceItems();

                // إضافة العناصر إلى قائمة العناصر المرتجعة
                int index = 1;
                foreach (var item in originalItems)
                {
                    _returnItems.Add(new ReturnItemViewModel
                    {
                        Index = index++,
                        ProductId = item.ProductId,
                        ProductName = item.ProductName,
                        Barcode = item.Barcode,
                        UnitPrice = item.UnitPrice,
                        OriginalQuantity = item.Quantity,
                        ReturnQuantity = 0, // البداية بصفر
                        Total = 0, // سيتم حسابه عند تغيير الكمية المرتجعة
                        ReturnReasons = new List<string>
                        {
                            "منتج معيب",
                            "منتج غير مطابق للمواصفات",
                            "خطأ في الطلب",
                            "تغيير رأي العميل",
                            "سبب آخر"
                        },
                        SelectedReason = "منتج معيب" // القيمة الافتراضية
                    });
                }

                // ربط القائمة بعنصر DataGrid
                ReturnItemsDataGrid.ItemsSource = _returnItems;

                // إضافة معالج حدث لتغيير الكمية المرتجعة
                ReturnItemsDataGrid.CellEditEnding += ReturnItemsDataGrid_CellEditEnding;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة عناصر الإرجاع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<InvoiceItemViewModel> GetOriginalInvoiceItems()
        {
            // محاكاة عناصر الفاتورة الأصلية
            List<InvoiceItemViewModel> items = new List<InvoiceItemViewModel>();

            // إضافة عناصر محاكاة بناءً على إجمالي الفاتورة
            if (_originalInvoice.Total > 0)
            {
                // العنصر الأول
                items.Add(new InvoiceItemViewModel
                {
                    Index = 1,
                    ProductId = 1,
                    ProductName = "منتج 1",
                    Barcode = "1234567890",
                    UnitPrice = 50.00m,
                    Quantity = 2,
                    DiscountAmount = 0,
                    TaxAmount = 15.00m,
                    Total = 115.00m
                });

                // العنصر الثاني (إذا كان المجموع أكبر من 150)
                if (_originalInvoice.Total > 150)
                {
                    items.Add(new InvoiceItemViewModel
                    {
                        Index = 2,
                        ProductId = 2,
                        ProductName = "منتج 2",
                        Barcode = "0987654321",
                        UnitPrice = 25.00m,
                        Quantity = 1,
                        DiscountAmount = 0,
                        TaxAmount = 3.75m,
                        Total = 28.75m
                    });
                }

                // العنصر الثالث (إذا كان المجموع أكبر من 300)
                if (_originalInvoice.Total > 300)
                {
                    items.Add(new InvoiceItemViewModel
                    {
                        Index = 3,
                        ProductId = 3,
                        ProductName = "منتج 3",
                        Barcode = "5678901234",
                        UnitPrice = 100.00m,
                        Quantity = 1,
                        DiscountAmount = 10.00m,
                        TaxAmount = 13.50m,
                        Total = 103.50m
                    });
                }
            }

            return items;
        }

        private void ReturnItemsDataGrid_CellEditEnding(object sender, System.Windows.Controls.DataGridCellEditEndingEventArgs e)
        {
            if (e.Column.Header.ToString() == "الكمية المرتجعة")
            {
                // تحديث المجموع بعد تغيير الكمية المرتجعة
                System.Windows.Threading.Dispatcher.BeginInvoke(
                    new Action(() =>
                    {
                        UpdateReturnItemTotals();
                        UpdateTotals();
                    }),
                    System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private void UpdateReturnItemTotals()
        {
            try
            {
                foreach (var item in _returnItems)
                {
                    // التحقق من صحة الكمية المرتجعة
                    if (item.ReturnQuantity < 0)
                    {
                        item.ReturnQuantity = 0;
                    }
                    else if (item.ReturnQuantity > item.OriginalQuantity)
                    {
                        item.ReturnQuantity = item.OriginalQuantity;
                    }

                    // حساب المجموع
                    item.Total = item.UnitPrice * item.ReturnQuantity;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث مجاميع العناصر المرتجعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTotals()
        {
            try
            {
                // حساب إجمالي المرتجع
                _totalReturnAmount = _returnItems.Sum(item => item.Total);

                // حساب الضريبة المرتجعة (15%)
                _taxReturnAmount = _totalReturnAmount * 0.15m;

                // حساب المبلغ المسترد
                _refundAmount = _totalReturnAmount + _taxReturnAmount;

                // عرض القيم
                TotalReturnValueText.Text = _totalReturnAmount.ToString("N2");
                TaxReturnValueText.Text = _taxReturnAmount.ToString("N2");
                RefundAmountValueText.Text = _refundAmount.ToString("N2");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث المجاميع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ProcessReturnButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود عناصر مرتجعة
                if (_returnItems.All(item => item.ReturnQuantity == 0))
                {
                    MessageBox.Show("الرجاء تحديد الكمية المرتجعة لعنصر واحد على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تأكيد الإرجاع
                MessageBoxResult result = MessageBox.Show(
                    $"هل أنت متأكد من إرجاع العناصر المحددة بقيمة {_refundAmount:N2}؟",
                    "تأكيد الإرجاع",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إنشاء فاتورة مرتجعة جديدة
                    ReturnInvoice = CreateReturnInvoice();

                    // عرض رسالة نجاح
                    MessageBox.Show($"تم إنشاء فاتورة مرتجعة بنجاح\nرقم الفاتورة المرتجعة: {ReturnInvoice.InvoiceNumber}\nالمبلغ المسترد: {_refundAmount:N2}", "تم الإرجاع", MessageBoxButton.OK, MessageBoxImage.Information);

                    // إغلاق النافذة مع إرجاع نتيجة نجاح
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تنفيذ الإرجاع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private InvoiceViewModel CreateReturnInvoice()
        {
            // إنشاء فاتورة مرتجعة جديدة
            string returnInvoiceNumber = $"RET-{_originalInvoice.InvoiceNumber}";
            
            // الحصول على طريقة رد المبلغ
            string refundMethod = ((System.Windows.Controls.ComboBoxItem)RefundMethodComboBox.SelectedItem).Content.ToString();
            
            // الحصول على سبب الإرجاع
            string returnReason = ((System.Windows.Controls.ComboBoxItem)ReturnReasonComboBox.SelectedItem).Content.ToString();
            
            // إنشاء ملاحظات الإرجاع
            string notes = $"إرجاع للفاتورة {_originalInvoice.InvoiceNumber}";
            if (!string.IsNullOrWhiteSpace(ReturnNotesTextBox.Text))
            {
                notes += $"\nسبب الإرجاع: {returnReason}";
                notes += $"\nملاحظات: {ReturnNotesTextBox.Text}";
            }

            // إنشاء الفاتورة المرتجعة
            return new InvoiceViewModel
            {
                Id = new Random().Next(1000, 9999), // توليد معرف عشوائي
                InvoiceNumber = returnInvoiceNumber,
                Date = DateTime.Now,
                CustomerId = _originalInvoice.CustomerId,
                CustomerName = _originalInvoice.CustomerName,
                UserId = 1, // المستخدم الحالي
                UserName = "المدير", // اسم المستخدم الحالي
                Subtotal = _totalReturnAmount,
                DiscountAmount = 0,
                DiscountPercentage = 0,
                IsDiscountPercentage = false,
                TaxAmount = _taxReturnAmount,
                TaxPercentage = 15,
                Total = _refundAmount,
                Status = InvoiceStatus.Returned,
                StatusText = "مرتجعة",
                PaymentMethod = GetRefundPaymentMethod(),
                PaymentMethodText = refundMethod,
                IsPaid = true,
                PaidAmount = _refundAmount,
                RemainingAmount = 0,
                IsInstallment = false,
                InstallmentPlanId = null,
                Notes = notes,
                CanEdit = Visibility.Collapsed,
                CanDelete = Visibility.Collapsed
            };
        }

        private InvoicePaymentMethod GetRefundPaymentMethod()
        {
            // تحويل طريقة رد المبلغ إلى تعداد InvoicePaymentMethod
            switch (RefundMethodComboBox.SelectedIndex)
            {
                case 0: return InvoicePaymentMethod.Cash; // نقداً
                case 1: return InvoicePaymentMethod.CreditCard; // إعادة للبطاقة الائتمانية
                case 2: return InvoicePaymentMethod.Other; // رصيد للعميل
                case 3: return InvoicePaymentMethod.Other; // استبدال بمنتج آخر
                default: return InvoicePaymentMethod.Cash;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة بدون إرجاع نتيجة نجاح
            DialogResult = false;
            Close();
        }
    }

    /// <summary>
    /// نموذج عرض عنصر الفاتورة المرتجعة
    /// </summary>
    public class ReturnItemViewModel
    {
        public int Index { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string Barcode { get; set; }
        public decimal UnitPrice { get; set; }
        public int OriginalQuantity { get; set; }
        public int ReturnQuantity { get; set; }
        public decimal Total { get; set; }
        public List<string> ReturnReasons { get; set; }
        public string SelectedReason { get; set; }
    }
}
