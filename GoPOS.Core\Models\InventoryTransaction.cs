using System;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج معاملة المخزون
    /// </summary>
    public class InventoryTransaction
    {
        public int Id { get; set; }

        [Required]
        public DateTime TransactionDate { get; set; }

        [Required]
        public TransactionType Type { get; set; }

        [Required]
        public int ProductId { get; set; }
        public Product Product { get; set; }

        [Required]
        public int QuantityBefore { get; set; }

        [Required]
        public int QuantityAfter { get; set; }

        [Required]
        public int QuantityChange { get; set; }

        public int? SourceWarehouseId { get; set; }
        public Warehouse SourceWarehouse { get; set; }

        public int? DestinationWarehouseId { get; set; }
        public Warehouse DestinationWarehouse { get; set; }

        public int? OrderId { get; set; }
        public Order Order { get; set; }

        public int? PurchaseOrderId { get; set; }
        public PurchaseOrder PurchaseOrder { get; set; }

        public int? InventoryCheckId { get; set; }
        public InventoryCheck InventoryCheck { get; set; }

        public int UserId { get; set; }
        public User User { get; set; }

        [MaxLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
    }

    public enum TransactionType
    {
        Purchase = 0,        // شراء
        Sale = 1,            // بيع
        Return = 2,          // مرتجع
        Adjustment = 3,      // تعديل
        Transfer = 4,        // نقل
        Inventory = 5,       // جرد
        Expiry = 6,          // انتهاء صلاحية
        Damage = 7,          // تلف
        Initial = 8          // رصيد افتتاحي
    }
}
