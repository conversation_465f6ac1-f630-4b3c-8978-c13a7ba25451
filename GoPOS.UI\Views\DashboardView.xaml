<UserControl x:Class="GoPOS.UI.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="8" Margin="0,0,0,20">
            <Grid Margin="15,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="لوحة المعلومات" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock x:Name="DateRangeTextBlock" Text="البيانات لليوم الحالي" FontSize="14" Foreground="White" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border BorderBrush="White" BorderThickness="1" CornerRadius="4" Margin="0,0,10,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="الفترة:" Foreground="White" VerticalAlignment="Center" Margin="10,0,5,0"/>
                            <ComboBox x:Name="PeriodComboBox"
                                      Grid.Column="1"
                                      Width="150"
                                      Height="35"
                                      BorderThickness="0"
                                      Background="Transparent"
                                      Foreground="White"
                                      SelectionChanged="PeriodComboBox_SelectionChanged">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.WindowBrushKey}" Color="Transparent"/>
                                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="#1565C0"/>
                                </ComboBox.Resources>
                                <ComboBoxItem Content="اليوم" IsSelected="True"/>
                                <ComboBoxItem Content="هذا الأسبوع"/>
                                <ComboBoxItem Content="هذا الشهر"/>
                                <ComboBoxItem Content="هذا العام"/>
                                <ComboBoxItem Content="مخصص"/>
                            </ComboBox>
                        </Grid>
                    </Border>

                    <StackPanel x:Name="DateRangePanel" Orientation="Horizontal" Visibility="Collapsed" Margin="0,0,10,0">
                        <Border BorderBrush="White" BorderThickness="1" CornerRadius="4" Margin="0,0,5,0">
                            <DatePicker x:Name="StartDatePicker" Width="120" BorderThickness="0" SelectedDateChanged="DateRange_SelectedDateChanged"/>
                        </Border>
                        <TextBlock Text="إلى" Foreground="White" VerticalAlignment="Center" Margin="5,0"/>
                        <Border BorderBrush="White" BorderThickness="1" CornerRadius="4">
                            <DatePicker x:Name="EndDatePicker" Width="120" BorderThickness="0" SelectedDateChanged="DateRange_SelectedDateChanged"/>
                        </Border>
                    </StackPanel>

                    <Button x:Name="RefreshButton"
                            Width="80"
                            Height="35"
                            Click="RefreshButton_Click"
                            Background="White"
                            Foreground="#2196F3"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- مؤشرات الأداء الرئيسية -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إجمالي المبيعات -->
            <Border Grid.Column="0" Background="#4CAF50" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="إجمالي المبيعات" Foreground="White" FontSize="16" FontWeight="SemiBold"/>
                    <TextBlock x:Name="TotalSalesTextBlock" Grid.Row="1" Text="0.00 ر.س" Foreground="White" FontSize="24" FontWeight="Bold" Margin="0,10,0,5"/>
                    <StackPanel Grid.Row="2" Orientation="Horizontal">
                        <TextBlock x:Name="SalesChangeTextBlock" Text="+0.0%" Foreground="White" FontSize="12"/>
                        <TextBlock Text=" مقارنة بالفترة السابقة" Foreground="White" FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- عدد الطلبات -->
            <Border Grid.Column="1" Background="#2196F3" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="عدد الطلبات" Foreground="White" FontSize="16" FontWeight="SemiBold"/>
                    <TextBlock x:Name="OrderCountTextBlock" Grid.Row="1" Text="0" Foreground="White" FontSize="24" FontWeight="Bold" Margin="0,10,0,5"/>
                    <StackPanel Grid.Row="2" Orientation="Horizontal">
                        <TextBlock x:Name="OrdersChangeTextBlock" Text="+0.0%" Foreground="White" FontSize="12"/>
                        <TextBlock Text=" مقارنة بالفترة السابقة" Foreground="White" FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- متوسط قيمة الطلب -->
            <Border Grid.Column="2" Background="#FF9800" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="متوسط قيمة الطلب" Foreground="White" FontSize="16" FontWeight="SemiBold"/>
                    <TextBlock x:Name="AverageOrderTextBlock" Grid.Row="1" Text="0.00 ر.س" Foreground="White" FontSize="24" FontWeight="Bold" Margin="0,10,0,5"/>
                    <StackPanel Grid.Row="2" Orientation="Horizontal">
                        <TextBlock x:Name="AverageChangeTextBlock" Text="+0.0%" Foreground="White" FontSize="12"/>
                        <TextBlock Text=" مقارنة بالفترة السابقة" Foreground="White" FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- عدد العملاء الجدد -->
            <Border Grid.Column="3" Background="#9C27B0" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="العملاء الجدد" Foreground="White" FontSize="16" FontWeight="SemiBold"/>
                    <TextBlock x:Name="NewCustomersTextBlock" Grid.Row="1" Text="0" Foreground="White" FontSize="24" FontWeight="Bold" Margin="0,10,0,5"/>
                    <StackPanel Grid.Row="2" Orientation="Horizontal">
                        <TextBlock x:Name="CustomersChangeTextBlock" Text="+0.0%" Foreground="White" FontSize="12"/>
                        <TextBlock Text=" مقارنة بالفترة السابقة" Foreground="White" FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- الرسوم البيانية - الصف الأول -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- رسم بياني للمبيعات -->
            <Border Grid.Column="0" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="المبيعات حسب الفترة" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

                    <!-- هنا سيتم إضافة الرسم البياني للمبيعات -->
                    <Border Grid.Row="1" Background="#F5F5F5" CornerRadius="5">
                        <Canvas x:Name="SalesChartCanvas" Margin="10">
                            <!-- سيتم رسم الرسم البياني برمجياً -->
                        </Canvas>
                    </Border>
                </Grid>
            </Border>

            <!-- توزيع المبيعات حسب الفئة -->
            <Border Grid.Column="1" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="المبيعات حسب الفئة" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

                    <!-- هنا سيتم إضافة الرسم البياني الدائري -->
                    <Border Grid.Row="1" Background="#F5F5F5" CornerRadius="5">
                        <Canvas x:Name="CategoryChartCanvas" Margin="10">
                            <!-- سيتم رسم الرسم البياني برمجياً -->
                        </Canvas>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- الرسوم البيانية - الصف الثاني -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- أفضل المنتجات مبيعاً -->
            <Border Grid.Column="0" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="أفضل المنتجات مبيعاً" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

                    <ListView x:Name="TopProductsListView" Grid.Row="1" BorderThickness="0" Background="Transparent">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="Margin" Value="0,2"/>
                                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                <Setter Property="BorderBrush" Value="#EEEEEE"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="{Binding Rank}" FontWeight="Bold" Foreground="#2196F3" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Column="1" Text="{Binding Name}" FontWeight="SemiBold"/>
                                    <TextBlock Grid.Column="2" Text="{Binding Sales, StringFormat={}{0:N0}}" HorizontalAlignment="Left" Width="80"/>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </Border>

            <!-- أفضل العملاء -->
            <Border Grid.Column="1" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="أفضل العملاء" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

                    <ListView x:Name="TopCustomersListView" Grid.Row="1" BorderThickness="0" Background="Transparent">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="Margin" Value="0,2"/>
                                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                <Setter Property="BorderBrush" Value="#EEEEEE"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="{Binding Rank}" FontWeight="Bold" Foreground="#2196F3" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Column="1" Text="{Binding Name}" FontWeight="SemiBold"/>
                                    <TextBlock Grid.Column="2" Text="{Binding Total, StringFormat={}{0:N2} ر.س}" HorizontalAlignment="Left" Width="100" Foreground="#4CAF50"/>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </Border>

            <!-- الأقساط المستحقة قريباً -->
            <Border Grid.Column="2" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="5">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="الأقساط المستحقة قريباً" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,10"/>

                    <ListView x:Name="UpcomingInstallmentsListView" Grid.Row="1" BorderThickness="0" Background="Transparent">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="Margin" Value="0,2"/>
                                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                                <Setter Property="BorderBrush" Value="#EEEEEE"/>
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="0,5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="{Binding CustomerName}" FontWeight="SemiBold"/>
                                    <TextBlock Grid.Column="1" Text="{Binding Amount, StringFormat={}{0:N2} ر.س}" Foreground="#FF9800" Margin="0,0,10,0"/>
                                    <TextBlock Grid.Column="2" Text="{Binding DueDate, StringFormat={}{0:yyyy/MM/dd}}" Foreground="#F44336"/>
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>

                    <Button x:Name="ViewAllInstallmentsButton"
                            Grid.Row="2"
                            Content="عرض كل الأقساط"
                            Margin="0,10,0,0"
                            HorizontalAlignment="Center"
                            Click="ViewAllInstallmentsButton_Click"
                            Background="#FF9800"
                            Foreground="White"
                            Padding="10,5">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
