using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GoPOS.UI.ViewModels
{
    /// <summary>
    /// نموذج عرض العميل
    /// </summary>
    public class CustomerViewModel : INotifyPropertyChanged
    {
        private int _id;
        private string _name;
        private string _phone;
        private string _email;
        private string _address;
        private string _notes;
        private bool _isActive = true;
        private int _invoiceCount;
        private decimal _totalPurchases;

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone
        {
            get => _phone;
            set
            {
                if (_phone != value)
                {
                    _phone = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string Email
        {
            get => _email;
            set
            {
                if (_email != value)
                {
                    _email = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// العنوان
        /// </summary>
        public string Address
        {
            get => _address;
            set
            {
                if (_address != value)
                {
                    _address = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// هل العميل نشط
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// عدد الفواتير
        /// </summary>
        public int InvoiceCount
        {
            get => _invoiceCount;
            set
            {
                if (_invoiceCount != value)
                {
                    _invoiceCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// إجمالي المشتريات
        /// </summary>
        public decimal TotalPurchases
        {
            get => _totalPurchases;
            set
            {
                if (_totalPurchases != value)
                {
                    _totalPurchases = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حدث تغيير الخاصية
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// إشعار بتغيير الخاصية
        /// </summary>
        /// <param name="propertyName">اسم الخاصية</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
