﻿#pragma checksum "..\..\..\..\Views\CustomersManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F2CA66324586B4E97B5BBA078B0CF3ABCAA27665"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// CustomersManagementView
    /// </summary>
    public partial class CustomersManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\..\Views\CustomersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCustomerButton;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\..\Views\CustomersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditCustomerButton;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\..\Views\CustomersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteCustomerButton;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\Views\CustomersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\Views\CustomersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\Views\CustomersManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CustomersDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/customersmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CustomersManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddCustomerButton = ((System.Windows.Controls.Button)(target));
            
            #line 25 "..\..\..\..\Views\CustomersManagementView.xaml"
            this.AddCustomerButton.Click += new System.Windows.RoutedEventHandler(this.AddCustomerButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.EditCustomerButton = ((System.Windows.Controls.Button)(target));
            
            #line 26 "..\..\..\..\Views\CustomersManagementView.xaml"
            this.EditCustomerButton.Click += new System.Windows.RoutedEventHandler(this.EditCustomerButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DeleteCustomerButton = ((System.Windows.Controls.Button)(target));
            
            #line 27 "..\..\..\..\Views\CustomersManagementView.xaml"
            this.DeleteCustomerButton.Click += new System.Windows.RoutedEventHandler(this.DeleteCustomerButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 31 "..\..\..\..\Views\CustomersManagementView.xaml"
            this.SearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.SearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\..\Views\CustomersManagementView.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CustomersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 37 "..\..\..\..\Views\CustomersManagementView.xaml"
            this.CustomersDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CustomersDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

