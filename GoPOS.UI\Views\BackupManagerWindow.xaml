<Window x:Class="GoPOS.UI.Views.BackupManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="إدارة النسخ الاحتياطية"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="8" Margin="0,0,0,20">
            <Grid Margin="15,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="إدارة النسخ الاحتياطية" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="إنشاء واستعادة النسخ الاحتياطية لقاعدة البيانات" FontSize="14" Foreground="White" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="CreateBackupButton"
                            Width="150"
                            Height="40"
                            Margin="0,0,10,0"
                            Click="CreateBackupButton_Click"
                            Background="White"
                            Foreground="#2196F3"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="💾" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إنشاء نسخة احتياطية" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshButton"
                            Width="100"
                            Height="40"
                            Click="RefreshButton_Click"
                            Background="White"
                            Foreground="#2196F3"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- قائمة النسخ الاحتياطية -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- معلومات المسار -->
            <Grid Grid.Row="0" Margin="0,0,0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="مسار النسخ الاحتياطية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox x:Name="BackupPathTextBox" Grid.Column="1" Height="30" IsReadOnly="True"/>
                <Button x:Name="BrowsePathButton" Grid.Column="2" Content="استعراض" Width="80" Height="30" Margin="10,0,0,0" Click="BrowsePathButton_Click"/>
            </Grid>

            <!-- جدول النسخ الاحتياطية -->
            <DataGrid x:Name="BackupsDataGrid"
                      Grid.Row="1"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      SelectionChanged="BackupsDataGrid_SelectionChanged"
                      AlternatingRowBackground="#F5F5F5"
                      BorderBrush="#DDDDDD"
                      BorderThickness="1"
                      RowHeight="35"
                      HeadersVisibility="Column"
                      GridLinesVisibility="Horizontal"
                      HorizontalGridLinesBrush="#EEEEEE">
                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#2196F3"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Padding" Value="10,0"/>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="اسم الملف" Binding="{Binding FileName}" Width="*">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreationDate, StringFormat={}{0:yyyy/MM/dd HH:mm:ss}}" Width="150">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="الحجم" Binding="{Binding FormattedSize}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                                <Setter Property="HorizontalAlignment" Value="Right"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="النوع" Binding="{Binding BackupType}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
                <DataGrid.ContextMenu>
                    <ContextMenu>
                        <MenuItem Header="استعادة النسخة المحددة" Click="RestoreBackupMenuItem_Click"/>
                        <MenuItem Header="حذف النسخة المحددة" Click="DeleteBackupMenuItem_Click"/>
                        <MenuItem Header="عرض معلومات النسخة" Click="ViewBackupInfoMenuItem_Click"/>
                        <MenuItem Header="نسخ إلى مجلد آخر" Click="CopyBackupMenuItem_Click"/>
                    </ContextMenu>
                </DataGrid.ContextMenu>
            </DataGrid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock x:Name="StatusTextBlock" Grid.Column="0" Text="" VerticalAlignment="Center"/>

            <Button x:Name="RestoreBackupButton"
                    Grid.Column="1"
                    Content="استعادة النسخة المحددة"
                    Width="180"
                    Height="40"
                    Margin="0,0,10,0"
                    Click="RestoreBackupButton_Click"
                    Background="#FF9800"
                    Foreground="White"
                    IsEnabled="False">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>

            <Button x:Name="DeleteBackupButton"
                    Grid.Column="2"
                    Content="حذف النسخة المحددة"
                    Width="150"
                    Height="40"
                    Margin="0,0,10,0"
                    Click="DeleteBackupButton_Click"
                    Background="#F44336"
                    Foreground="White"
                    IsEnabled="False">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>

            <Button x:Name="ViewBackupInfoButton"
                    Grid.Column="3"
                    Content="عرض المعلومات"
                    Width="120"
                    Height="40"
                    Margin="0,0,10,0"
                    Click="ViewBackupInfoButton_Click"
                    Background="#009688"
                    Foreground="White"
                    IsEnabled="False">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>

            <Button x:Name="CloseButton"
                    Grid.Column="4"
                    Content="إغلاق"
                    Width="100"
                    Height="40"
                    Click="CloseButton_Click"
                    Background="#607D8B"
                    Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </Grid>
    </Grid>
</Window>
