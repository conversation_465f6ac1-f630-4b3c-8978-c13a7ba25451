using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for POSTerminalView.xaml
    /// </summary>
    public partial class POSTerminalView : UserControl
    {
        private ObservableCollection<CartItem> _cartItems;
        private decimal _subtotal = 0;
        private decimal _discount = 0;
        private decimal _total = 0;
        private bool _isPercentDiscount = true;

        private ProductRepository _productRepository;
        private CategoryRepository _categoryRepository;
        private CustomerRepository _customerRepository;
        private DatabaseContext _dbContext;

        public POSTerminalView()
        {
            InitializeComponent();

            // تهيئة قاعدة البيانات والمستودعات
            _dbContext = new DatabaseContext();
            _productRepository = new ProductRepository(_dbContext);
            _categoryRepository = new CategoryRepository(_dbContext);
            _customerRepository = new CustomerRepository(_dbContext);

            InitializeDataAsync();
        }

        private async void InitializeDataAsync()
        {
            try
            {
                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // تعطيل واجهة المستخدم أثناء التحميل
                IsEnabled = false;

                // تهيئة سلة المشتريات
                _cartItems = new ObservableCollection<CartItem>();
                CartListView.ItemsSource = _cartItems;

                // تحديث حالة السلة
                UpdateCartStatus();

                // تهيئة قائمة العملاء من قاعدة البيانات
                List<Customer> customers = new List<Customer>();

                // إضافة عميل نقدي كأول عميل
                customers.Add(new Customer { Id = 1, Name = "عميل نقدي" });

                // جلب العملاء النشطين من قاعدة البيانات
                var dbCustomers = await _customerRepository.FindAsync(c => c.IsActive);
                foreach (var customer in dbCustomers)
                {
                    customers.Add(customer);
                }

                CustomerComboBox.ItemsSource = customers;
                CustomerComboBox.SelectedIndex = 0;

                // تهيئة الفئات من قاعدة البيانات
                // إضافة زر "الكل" أولاً
                CategoriesPanel.Children.Clear();
                AddCategoryButton("الكل", 0);

                // جلب الفئات النشطة من قاعدة البيانات
                var categories = await _categoryRepository.FindAsync(c => c.IsActive);
                foreach (var category in categories)
                {
                    AddCategoryButton(category.Name, category.Id);
                }

                // تهيئة نوع الخصم
                DiscountTypeComboBox.SelectedIndex = 0; // نسبة مئوية

                // تهيئة قيمة الضريبة
                TaxTextBlock.Text = "0.00";

                // تحميل المنتجات
                await LoadProductsAsync(0); // تحميل كل المنتجات

                // إخفاء مؤشر التحميل وإعادة تمكين واجهة المستخدم
                LoadingIndicator.Visibility = Visibility.Collapsed;
                IsEnabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
                IsEnabled = true;
            }
        }

        private void AddCategoryButton(string name, int id)
        {
            Button button = new Button
            {
                Content = name,
                Width = 100,
                Height = 40,
                Margin = new Thickness(5),
                Tag = id,
                Background = id == 0 ? new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(33, 150, 243)) : new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                Foreground = id == 0 ? new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White) : new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black),
                FontWeight = id == 0 ? FontWeights.SemiBold : FontWeights.Normal
            };

            // إضافة تأثير تدوير الزوايا
            button.Resources = new ResourceDictionary();
            Style style = new Style(typeof(Border));
            style.Setters.Add(new Setter(Border.CornerRadiusProperty, new CornerRadius(20)));
            button.Resources.Add(typeof(Border), style);

            button.Click += CategoryButton_Click;
            CategoriesPanel.Children.Add(button);
        }

        private async Task LoadProductsAsync(int categoryId)
        {
            try
            {
                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // تعطيل واجهة المستخدم أثناء التحميل
                IsEnabled = false;

                // إخفاء رسالة عدم وجود منتجات
                NoProductsMessage.Visibility = Visibility.Collapsed;

                // مسح منطقة المنتجات
                ProductsPanel.Children.Clear();

                // تحديث تنسيق أزرار الفئات
                foreach (var child in CategoriesPanel.Children)
                {
                    if (child is Button categoryButton)
                    {
                        int buttonCategoryId = Convert.ToInt32(categoryButton.Tag);
                        if (buttonCategoryId == categoryId)
                        {
                            categoryButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(33, 150, 243));
                            categoryButton.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White);
                            categoryButton.FontWeight = FontWeights.SemiBold;
                        }
                        else
                        {
                            categoryButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White);
                            categoryButton.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black);
                            categoryButton.FontWeight = FontWeights.Normal;
                        }
                    }
                }

                // تحميل المنتجات من قاعدة البيانات
                IEnumerable<Product> products;

                if (categoryId == 0)
                {
                    // إذا كانت الفئة "الكل"، نعرض جميع المنتجات النشطة
                    products = await _productRepository.FindAsync(p => p.IsActive);
                }
                else
                {
                    // تصفية المنتجات حسب الفئة المحددة
                    products = await _productRepository.GetByCategoryAsync(categoryId);
                    products = products.Where(p => p.IsActive);
                }

                // إضافة المنتجات إلى الواجهة
                foreach (var product in products)
                {
                    AddProductCard(product);
                }

                // إظهار رسالة إذا لم يتم العثور على منتجات
                if (!products.Any())
                {
                    NoProductsMessage.Visibility = Visibility.Visible;
                }

                // إخفاء مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Collapsed;

                // إعادة تمكين واجهة المستخدم
                IsEnabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
                IsEnabled = true;
            }
        }

        private List<Product> GetAllProducts()
        {
            // هذه بيانات تجريبية، سيتم استبدالها بقراءة من قاعدة البيانات
            List<Product> products = new List<Product>();

            // مشروبات
            products.Add(new Product { Id = 1, Name = "قهوة", Price = 5.00m, CategoryId = 1, Barcode = "1001" });
            products.Add(new Product { Id = 2, Name = "شاي", Price = 3.00m, CategoryId = 1, Barcode = "1002" });
            products.Add(new Product { Id = 3, Name = "عصير برتقال", Price = 7.00m, CategoryId = 1, Barcode = "1003" });
            products.Add(new Product { Id = 10, Name = "عصير تفاح", Price = 6.50m, CategoryId = 1, Barcode = "1010" });
            products.Add(new Product { Id = 11, Name = "عصير مانجو", Price = 8.00m, CategoryId = 1, Barcode = "1011" });

            // وجبات سريعة
            products.Add(new Product { Id = 4, Name = "ساندويتش", Price = 12.00m, CategoryId = 2, Barcode = "2001" });
            products.Add(new Product { Id = 5, Name = "بيتزا", Price = 25.00m, CategoryId = 2, Barcode = "2002" });
            products.Add(new Product { Id = 6, Name = "برجر", Price = 18.00m, CategoryId = 2, Barcode = "2003" });
            products.Add(new Product { Id = 12, Name = "شاورما", Price = 15.00m, CategoryId = 2, Barcode = "2004" });
            products.Add(new Product { Id = 13, Name = "فاهيتا", Price = 22.00m, CategoryId = 2, Barcode = "2005" });

            // حلويات
            products.Add(new Product { Id = 7, Name = "كيك", Price = 15.00m, CategoryId = 3, Barcode = "3001" });
            products.Add(new Product { Id = 8, Name = "آيس كريم", Price = 8.00m, CategoryId = 3, Barcode = "3002" });
            products.Add(new Product { Id = 9, Name = "كوكيز", Price = 6.00m, CategoryId = 3, Barcode = "3003" });
            products.Add(new Product { Id = 14, Name = "تشيز كيك", Price = 18.00m, CategoryId = 3, Barcode = "3004" });
            products.Add(new Product { Id = 15, Name = "كنافة", Price = 20.00m, CategoryId = 3, Barcode = "3005" });

            // مستلزمات
            products.Add(new Product { Id = 16, Name = "أكواب ورقية", Price = 10.00m, CategoryId = 4, Barcode = "4001" });
            products.Add(new Product { Id = 17, Name = "مناديل", Price = 5.00m, CategoryId = 4, Barcode = "4002" });
            products.Add(new Product { Id = 18, Name = "أكياس", Price = 3.00m, CategoryId = 4, Barcode = "4003" });

            // إلكترونيات
            products.Add(new Product { Id = 19, Name = "شاحن هاتف", Price = 35.00m, CategoryId = 5, Barcode = "5001" });
            products.Add(new Product { Id = 20, Name = "سماعات", Price = 50.00m, CategoryId = 5, Barcode = "5002" });
            products.Add(new Product { Id = 21, Name = "بطارية", Price = 15.00m, CategoryId = 5, Barcode = "5003" });

            return products;
        }

        private void AddProductCard(Product product)
        {
            // إنشاء حدود البطاقة
            Border border = new Border
            {
                Width = 160,
                Height = 180,
                Margin = new Thickness(8),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(221, 221, 221)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    ShadowDepth = 2,
                    BlurRadius = 5,
                    Opacity = 0.2,
                    Color = System.Windows.Media.Colors.Gray
                }
            };

            // إنشاء محتوى البطاقة
            Grid mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // الجزء العلوي (معلومات المنتج)
            StackPanel infoPanel = new StackPanel
            {
                Margin = new Thickness(10)
            };

            // صورة المنتج (استخدام أيقونة بسيطة)
            TextBlock iconText = new TextBlock
            {
                Text = GetProductIcon(product.CategoryId),
                FontSize = 32,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            // اسم المنتج
            TextBlock nameText = new TextBlock
            {
                Text = product.Name,
                FontWeight = FontWeights.Bold,
                FontSize = 16,
                TextWrapping = TextWrapping.Wrap,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5),
                TextTrimming = TextTrimming.CharacterEllipsis,
                MaxHeight = 40
            };

            // الباركود
            TextBlock barcodeText = new TextBlock
            {
                Text = $"#{product.Barcode}",
                FontSize = 12,
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5)
            };

            // السعر
            TextBlock priceText = new TextBlock
            {
                Text = $"{product.Price:N2} ر.س",
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(33, 150, 243)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5)
            };

            // إضافة العناصر إلى لوحة المعلومات
            infoPanel.Children.Add(iconText);
            infoPanel.Children.Add(nameText);
            infoPanel.Children.Add(barcodeText);
            infoPanel.Children.Add(priceText);

            // الجزء السفلي (زر الإضافة)
            Button addButton = new Button
            {
                Content = "إضافة إلى السلة",
                Tag = product,
                Height = 35,
                Margin = new Thickness(10, 0, 10, 10),
                Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(76, 175, 80)),
                Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                FontWeight = FontWeights.SemiBold
            };

            // إضافة تأثير تدوير الزوايا للزر
            addButton.Resources = new ResourceDictionary();
            Style style = new Style(typeof(Border));
            style.Setters.Add(new Setter(Border.CornerRadiusProperty, new CornerRadius(4)));
            addButton.Resources.Add(typeof(Border), style);

            // إضافة معالج حدث النقر
            addButton.Click += AddToCartButton_Click;

            // إضافة العناصر إلى الشبكة الرئيسية
            Grid.SetRow(infoPanel, 0);
            Grid.SetRow(addButton, 1);
            mainGrid.Children.Add(infoPanel);
            mainGrid.Children.Add(addButton);

            // إضافة الشبكة إلى البطاقة
            border.Child = mainGrid;

            // إضافة البطاقة إلى لوحة المنتجات
            ProductsPanel.Children.Add(border);
        }

        private string GetProductIcon(int categoryId)
        {
            switch (categoryId)
            {
                case 1: return "☕"; // مشروبات
                case 2: return "🍔"; // وجبات سريعة
                case 3: return "🍰"; // حلويات
                case 4: return "🧴"; // مستلزمات
                case 5: return "📱"; // إلكترونيات
                default: return "📦"; // افتراضي
            }
        }

        private async void CategoryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                Button button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    int categoryId = Convert.ToInt32(button.Tag);
                    await LoadProductsAsync(categoryId);

                    // تحديث تنسيق الأزرار لإظهار الفئة المحددة
                    foreach (var child in CategoriesPanel.Children)
                    {
                        if (child is Button categoryButton)
                        {
                            if (categoryButton == button)
                            {
                                categoryButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.LightBlue);
                                categoryButton.FontWeight = FontWeights.Bold;
                            }
                            else
                            {
                                categoryButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White);
                                categoryButton.FontWeight = FontWeights.Normal;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل منتجات الفئة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة المؤشر إلى الوضع الطبيعي
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchProducts();
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchProducts();
            }
        }

        private async void SearchProducts()
        {
            try
            {
                string searchText = SearchTextBox.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    MessageBox.Show("الرجاء إدخال نص للبحث", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // تعطيل واجهة المستخدم أثناء البحث
                IsEnabled = false;

                // إخفاء رسالة عدم وجود منتجات
                NoProductsMessage.Visibility = Visibility.Collapsed;

                // مسح منطقة المنتجات
                ProductsPanel.Children.Clear();

                // إعادة تنسيق أزرار الفئات (إلغاء تحديد جميع الفئات)
                foreach (var child in CategoriesPanel.Children)
                {
                    if (child is Button categoryButton)
                    {
                        categoryButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White);
                        categoryButton.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black);
                        categoryButton.FontWeight = FontWeights.Normal;
                    }
                }

                // البحث عن المنتجات في قاعدة البيانات
                var searchResults = await _productRepository.SearchAsync(searchText);

                // تصفية المنتجات النشطة فقط
                searchResults = searchResults.Where(p => p.IsActive);

                // عرض نتائج البحث
                foreach (var product in searchResults)
                {
                    AddProductCard(product);
                }

                // إظهار رسالة إذا لم يتم العثور على منتجات
                if (!searchResults.Any())
                {
                    NoProductsMessage.Visibility = Visibility.Visible;
                }

                // إخفاء مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Collapsed;

                // إعادة تمكين واجهة المستخدم
                IsEnabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث عن المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
                IsEnabled = true;
            }
        }

        private void AddToCartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    Product product = button.Tag as Product;
                    if (product != null)
                    {
                        // تأثير بصري للنقر على الزر
                        button.IsEnabled = false;

                        // إضافة المنتج إلى السلة
                        AddProductToCart(product);

                        // إعادة تمكين الزر بعد فترة قصيرة
                        System.Threading.Tasks.Task.Delay(200).ContinueWith(_ =>
                        {
                            Dispatcher.Invoke(() => button.IsEnabled = true);
                        });
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن إضافة المنتج إلى السلة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المنتج إلى السلة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddProductToCart(Product product)
        {
            try
            {
                if (product == null)
                {
                    throw new ArgumentNullException(nameof(product), "المنتج غير موجود");
                }

                // البحث عن المنتج في السلة
                CartItem existingItem = null;
                foreach (var item in _cartItems)
                {
                    if (item.ProductId == product.Id)
                    {
                        existingItem = item;
                        break;
                    }
                }

                if (existingItem != null)
                {
                    // زيادة الكمية إذا كان المنتج موجود بالفعل
                    existingItem.Quantity++;
                    existingItem.TotalPrice = existingItem.UnitPrice * existingItem.Quantity;

                    // تحديث العرض
                    CartListView.Items.Refresh();
                }
                else
                {
                    // إضافة منتج جديد إلى السلة
                    CartItem newItem = new CartItem
                    {
                        ProductId = product.Id,
                        ProductName = product.Name,
                        UnitPrice = product.Price,
                        Quantity = 1,
                        TotalPrice = product.Price
                    };
                    _cartItems.Add(newItem);
                }

                // تحديث المجاميع
                UpdateTotals();

                // تحديث حالة السلة
                UpdateCartStatus();

                // التمرير إلى آخر عنصر في السلة
                if (CartListView.Items.Count > 0)
                {
                    CartListView.ScrollIntoView(CartListView.Items[CartListView.Items.Count - 1]);
                }

                // إضافة تأثير حركي للإشارة إلى إضافة المنتج
                AnimateCartIcon();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المنتج إلى السلة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void IncreaseQuantityButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    CartItem item = button.Tag as CartItem;
                    if (item != null)
                    {
                        // تأثير بصري للنقر على الزر
                        button.IsEnabled = false;

                        // زيادة الكمية
                        item.Quantity++;
                        item.TotalPrice = item.UnitPrice * item.Quantity;

                        // تحديث المجاميع
                        UpdateTotals();

                        // تحديث العرض
                        CartListView.Items.Refresh();

                        // إعادة تمكين الزر بعد فترة قصيرة
                        System.Threading.Tasks.Task.Delay(200).ContinueWith(_ =>
                        {
                            Dispatcher.Invoke(() => button.IsEnabled = true);
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء زيادة الكمية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DecreaseQuantityButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    CartItem item = button.Tag as CartItem;
                    if (item != null)
                    {
                        // تأثير بصري للنقر على الزر
                        button.IsEnabled = false;

                        if (item.Quantity > 1)
                        {
                            // تقليل الكمية
                            item.Quantity--;
                            item.TotalPrice = item.UnitPrice * item.Quantity;

                            // تحديث العرض
                            CartListView.Items.Refresh();
                        }
                        else
                        {
                            // إزالة المنتج من السلة
                            _cartItems.Remove(item);
                        }

                        // تحديث المجاميع
                        UpdateTotals();

                        // إعادة تمكين الزر بعد فترة قصيرة
                        System.Threading.Tasks.Task.Delay(200).ContinueWith(_ =>
                        {
                            Dispatcher.Invoke(() => button.IsEnabled = true);
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تقليل الكمية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTotals()
        {
            try
            {
                // حساب المجموع الفرعي
                _subtotal = 0;
                foreach (var item in _cartItems)
                {
                    _subtotal += item.TotalPrice;
                }

                // عرض المجموع الفرعي
                SubtotalTextBlock.Text = $"{_subtotal:N2}";

                // حساب الخصم
                CalculateDiscount();

                // حساب الضريبة (15% ضريبة القيمة المضافة)
                decimal taxRate = 0.15m; // 15%
                decimal taxAmount = _total * taxRate;

                // عرض قيمة الضريبة
                TaxTextBlock.Text = $"{taxAmount:N2}";

                // إضافة الضريبة إلى الإجمالي
                _total += taxAmount;

                // عرض الإجمالي النهائي
                TotalTextBlock.Text = $"{_total:N2}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حساب المجاميع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CalculateDiscount()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(DiscountTextBox.Text) && decimal.TryParse(DiscountTextBox.Text, out decimal discountValue))
                {
                    if (_isPercentDiscount)
                    {
                        // التحقق من أن نسبة الخصم لا تتجاوز 100%
                        if (discountValue > 100)
                        {
                            discountValue = 100;
                            DiscountTextBox.Text = "100";
                        }

                        // حساب الخصم كنسبة مئوية
                        _discount = (_subtotal * discountValue) / 100;
                    }
                    else
                    {
                        // التحقق من أن قيمة الخصم لا تتجاوز المجموع الفرعي
                        if (discountValue > _subtotal)
                        {
                            discountValue = _subtotal;
                            DiscountTextBox.Text = _subtotal.ToString("N2");
                        }

                        // حساب الخصم كقيمة ثابتة
                        _discount = discountValue;
                    }
                }
                else
                {
                    _discount = 0;
                }

                // حساب الإجمالي بعد الخصم (قبل الضريبة)
                _total = _subtotal - _discount;
                if (_total < 0) _total = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حساب الخصم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                _discount = 0;
                _total = _subtotal;
            }
        }

        private void DiscountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateDiscount();
        }

        private void DiscountTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _isPercentDiscount = DiscountTypeComboBox.SelectedIndex == 0;
            CalculateDiscount();
        }

        private async void BarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // محاكاة مسح الباركود باستخدام مربع حوار بسيط
                string barcode = ShowInputDialog("أدخل الباركود", "مسح الباركود");

                if (!string.IsNullOrWhiteSpace(barcode))
                {
                    // إظهار مؤشر التحميل
                    LoadingIndicator.Visibility = Visibility.Visible;

                    // تعطيل واجهة المستخدم أثناء البحث
                    IsEnabled = false;

                    // البحث عن المنتج بالباركود في قاعدة البيانات
                    var foundProduct = await _productRepository.GetByBarcodeAsync(barcode);

                    if (foundProduct != null && foundProduct.IsActive)
                    {
                        // إضافة المنتج إلى السلة
                        AddProductToCart(foundProduct);

                        // عرض رسالة نجاح
                        MessageBox.Show($"تم إضافة {foundProduct.Name} إلى السلة", "تم المسح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("لم يتم العثور على منتج نشط بهذا الباركود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    // إخفاء مؤشر التحميل
                    LoadingIndicator.Visibility = Visibility.Collapsed;

                    // إعادة تمكين واجهة المستخدم
                    IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء مسح الباركود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
                IsEnabled = true;
            }
        }

        private void BarcodeSearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchByBarcode();
        }

        private void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchByBarcode();
            }
        }

        private async void SearchByBarcode()
        {
            try
            {
                string barcode = BarcodeTextBox.Text.Trim();
                if (string.IsNullOrEmpty(barcode))
                {
                    MessageBox.Show("الرجاء إدخال الباركود", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إظهار مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Visible;

                // تعطيل زر البحث لمنع النقر المتكرر
                BarcodeSearchButton.IsEnabled = false;

                // البحث عن المنتج بالباركود في قاعدة البيانات
                var foundProduct = await _productRepository.GetByBarcodeAsync(barcode);

                if (foundProduct != null && foundProduct.IsActive)
                {
                    // إضافة المنتج إلى السلة
                    AddProductToCart(foundProduct);

                    // مسح حقل الباركود
                    BarcodeTextBox.Clear();

                    // تركيز حقل الباركود للإدخال التالي
                    BarcodeTextBox.Focus();
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على منتج نشط بهذا الباركود", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);

                    // تحديد النص في حقل الباركود للتسهيل على المستخدم
                    BarcodeTextBox.SelectAll();
                    BarcodeTextBox.Focus();
                }

                // إخفاء مؤشر التحميل
                LoadingIndicator.Visibility = Visibility.Collapsed;

                // إعادة تمكين زر البحث
                BarcodeSearchButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث عن المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                LoadingIndicator.Visibility = Visibility.Collapsed;
                BarcodeSearchButton.IsEnabled = true;
            }
        }

        private string ShowInputDialog(string prompt, string title)
        {
            // إنشاء نافذة حوار بسيطة
            Window inputDialog = new Window
            {
                Title = title,
                Width = 300,
                Height = 150,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                ResizeMode = ResizeMode.NoResize,
                WindowStyle = WindowStyle.ToolWindow
            };

            // إنشاء تخطيط النافذة
            Grid grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // إضافة نص التوجيه
            TextBlock promptText = new TextBlock
            {
                Text = prompt,
                Margin = new Thickness(10),
                TextWrapping = TextWrapping.Wrap
            };
            Grid.SetRow(promptText, 0);

            // إضافة مربع النص
            TextBox inputTextBox = new TextBox
            {
                Margin = new Thickness(10),
                Padding = new Thickness(5),
                Height = 30
            };
            Grid.SetRow(inputTextBox, 1);

            // إضافة أزرار الإجراءات
            StackPanel buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(10)
            };

            Button okButton = new Button
            {
                Content = "موافق",
                Width = 80,
                Height = 30,
                Margin = new Thickness(5, 0, 0, 0),
                IsDefault = true
            };

            Button cancelButton = new Button
            {
                Content = "إلغاء",
                Width = 80,
                Height = 30,
                IsCancel = true
            };

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(okButton);
            Grid.SetRow(buttonPanel, 2);

            // إضافة العناصر إلى الشبكة
            grid.Children.Add(promptText);
            grid.Children.Add(inputTextBox);
            grid.Children.Add(buttonPanel);

            // تعيين محتوى النافذة
            inputDialog.Content = grid;

            // تعيين التركيز على مربع النص
            inputDialog.Loaded += (s, e) => inputTextBox.Focus();

            // تعيين نتيجة النافذة
            string result = null;
            okButton.Click += (s, e) =>
            {
                result = inputTextBox.Text;
                inputDialog.DialogResult = true;
            };

            // عرض النافذة وانتظار النتيجة
            bool? dialogResult = inputDialog.ShowDialog();

            // إرجاع النتيجة
            return dialogResult == true ? result : null;
        }

        private void RemoveItemButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    CartItem item = button.Tag as CartItem;
                    if (item != null)
                    {
                        // تأكيد الحذف
                        MessageBoxResult result = MessageBox.Show($"هل تريد إزالة {item.ProductName} من السلة؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            // إزالة المنتج من السلة
                            _cartItems.Remove(item);

                            // تحديث المجاميع
                            UpdateTotals();

                            // تحديث حالة السلة
                            UpdateCartStatus();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إزالة المنتج من السلة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateCartStatus()
        {
            // تحديث حالة السلة (إظهار/إخفاء رسالة السلة الفارغة)
            if (_cartItems.Count == 0)
            {
                EmptyCartMessage.Visibility = Visibility.Visible;
            }
            else
            {
                EmptyCartMessage.Visibility = Visibility.Collapsed;
            }
        }

        private void AnimateCartIcon()
        {
            try
            {
                // إنشاء أيقونة متحركة للسلة
                Border cartIconBorder = new Border
                {
                    Width = 40,
                    Height = 40,
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(76, 175, 80)),
                    CornerRadius = new CornerRadius(20),
                    HorizontalAlignment = HorizontalAlignment.Right,
                    VerticalAlignment = VerticalAlignment.Top,
                    Margin = new Thickness(0, 20, 20, 0),
                    Opacity = 0
                };

                TextBlock cartIcon = new TextBlock
                {
                    Text = "🛒",
                    FontSize = 20,
                    Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.White),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                cartIconBorder.Child = cartIcon;

                // إضافة الأيقونة إلى الشاشة
                Grid parentGrid = this.Parent as Grid;
                if (parentGrid != null)
                {
                    parentGrid.Children.Add(cartIconBorder);

                    // إنشاء الرسوم المتحركة
                    System.Windows.Media.Animation.Storyboard storyboard = new System.Windows.Media.Animation.Storyboard();

                    // تحريك الأيقونة من اليمين إلى اليسار
                    System.Windows.Media.Animation.DoubleAnimation moveAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        From = 0,
                        To = -300,
                        Duration = TimeSpan.FromSeconds(1)
                    };
                    System.Windows.Media.Animation.Storyboard.SetTarget(moveAnimation, cartIconBorder);
                    System.Windows.Media.Animation.Storyboard.SetTargetProperty(moveAnimation, new System.Windows.PropertyPath("(UIElement.RenderTransform).(TranslateTransform.X)"));

                    // تغيير شفافية الأيقونة
                    System.Windows.Media.Animation.DoubleAnimation opacityAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        From = 0,
                        To = 1,
                        Duration = TimeSpan.FromSeconds(0.3)
                    };
                    System.Windows.Media.Animation.Storyboard.SetTarget(opacityAnimation, cartIconBorder);
                    System.Windows.Media.Animation.Storyboard.SetTargetProperty(opacityAnimation, new System.Windows.PropertyPath("Opacity"));

                    // تغيير شفافية الأيقونة للاختفاء
                    System.Windows.Media.Animation.DoubleAnimation fadeOutAnimation = new System.Windows.Media.Animation.DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        BeginTime = TimeSpan.FromSeconds(0.7),
                        Duration = TimeSpan.FromSeconds(0.3)
                    };
                    System.Windows.Media.Animation.Storyboard.SetTarget(fadeOutAnimation, cartIconBorder);
                    System.Windows.Media.Animation.Storyboard.SetTargetProperty(fadeOutAnimation, new System.Windows.PropertyPath("Opacity"));

                    // إضافة الرسوم المتحركة إلى الـ Storyboard
                    storyboard.Children.Add(moveAnimation);
                    storyboard.Children.Add(opacityAnimation);
                    storyboard.Children.Add(fadeOutAnimation);

                    // تعيين معالج حدث الانتهاء
                    storyboard.Completed += (s, e) =>
                    {
                        parentGrid.Children.Remove(cartIconBorder);
                    };

                    // تعيين تحويل الإزاحة للأيقونة
                    cartIconBorder.RenderTransform = new System.Windows.Media.TranslateTransform();

                    // بدء الرسوم المتحركة
                    storyboard.Begin();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أي أخطاء في الرسوم المتحركة
                Console.WriteLine($"خطأ في الرسوم المتحركة: {ex.Message}");
            }
        }

        private void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إضافة عميل جديد
            MessageBox.Show("سيتم فتح نافذة إضافة عميل جديد", "إضافة عميل", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PayButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر لمنع النقر المتكرر
                PayButton.IsEnabled = false;

                if (_cartItems.Count == 0)
                {
                    MessageBox.Show("لا توجد منتجات في السلة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // التحقق من اختيار عميل
                Customer selectedCustomer = CustomerComboBox.SelectedItem as Customer;
                if (selectedCustomer == null)
                {
                    MessageBox.Show("الرجاء اختيار عميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إتمام عملية الدفع النقدي
                MessageBoxResult result = MessageBox.Show($"هل تريد إتمام عملية الدفع النقدي بمبلغ {_total:N2} ر.س؟", "تأكيد الدفع", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إظهار مؤشر الانتظار
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // محاكاة تأخير لعملية الدفع
                    System.Threading.Thread.Sleep(1000);

                    // إنشاء طلب جديد (سيتم استبداله بالحفظ في قاعدة البيانات)
                    int orderId = new Random().Next(1000, 9999);

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;

                    MessageBox.Show($"تم إتمام عملية الدفع بنجاح\nرقم الطلب: {orderId}", "تم الدفع", MessageBoxButton.OK, MessageBoxImage.Information);

                    // مسح السلة
                    ClearCart();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عملية الدفع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
            finally
            {
                // إعادة تمكين الزر
                PayButton.IsEnabled = true;
            }
        }

        private void InstallmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر لمنع النقر المتكرر
                InstallmentButton.IsEnabled = false;

                if (_cartItems.Count == 0)
                {
                    MessageBox.Show("لا توجد منتجات في السلة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // التحقق من اختيار عميل
                Customer selectedCustomer = CustomerComboBox.SelectedItem as Customer;
                if (selectedCustomer == null || selectedCustomer.Id == 1) // عميل نقدي
                {
                    MessageBox.Show("الرجاء اختيار عميل مسجل للدفع بالأقساط", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // إنشاء طلب جديد (سيتم استبداله بالحفظ في قاعدة البيانات)
                int orderId = new Random().Next(1000, 9999);

                // إعادة المؤشر إلى الوضع الطبيعي قبل فتح النافذة
                Cursor = System.Windows.Input.Cursors.Arrow;

                // فتح نافذة إعداد خطة الأقساط
                InstallmentPlanWindow installmentWindow = new InstallmentPlanWindow(_total, orderId);
                installmentWindow.Owner = Window.GetWindow(this);

                bool? result = installmentWindow.ShowDialog();

                if (result == true)
                {
                    // الحصول على خطة الأقساط
                    var installmentPlan = installmentWindow.InstallmentPlan;

                    // إظهار مؤشر الانتظار
                    Cursor = System.Windows.Input.Cursors.Wait;

                    // محاكاة تأخير لعملية حفظ الأقساط
                    System.Threading.Thread.Sleep(1000);

                    // إعادة المؤشر إلى الوضع الطبيعي
                    Cursor = System.Windows.Input.Cursors.Arrow;

                    // إتمام عملية البيع بالأقساط (سيتم استبداله بالحفظ في قاعدة البيانات)
                    MessageBox.Show($"تم إنشاء خطة الأقساط بنجاح\nرقم الطلب: {orderId}\nالدفعة المقدمة: {installmentPlan.DownPayment:N2} ر.س\nعدد الأقساط: {installmentPlan.NumberOfInstallments}", "تم الدفع", MessageBoxButton.OK, MessageBoxImage.Information);

                    // مسح السلة
                    ClearCart();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء خطة الأقساط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
            finally
            {
                // إعادة تمكين الزر
                InstallmentButton.IsEnabled = true;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartItems.Count > 0)
            {
                MessageBoxResult result = MessageBox.Show("هل أنت متأكد من إلغاء الفاتورة الحالية؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    ClearCart();
                }
            }
        }

        private void SaveDraftButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات في السلة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // حفظ الفاتورة كمسودة
            MessageBox.Show("تم حفظ الفاتورة كمسودة", "حفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            ClearCart();
        }

        private void ClearCart()
        {
            _cartItems.Clear();
            UpdateTotals();
            DiscountTextBox.Text = string.Empty;
        }
    }

    // فئات مساعدة
    public class CartItem
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
        public decimal TotalPrice { get; set; }
    }

    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal Price { get; set; }
        public int CategoryId { get; set; }
        public string Barcode { get; set; }
        public string ImagePath { get; set; }
        public bool IsAvailable { get; set; } = true;
    }

    public class Customer
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}
