using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using GoPOS.Data.DataAccess;
using GoPOS.Data.Models;

namespace GoPOS.Data.Services
{
    /// <summary>
    /// خدمة مراقبة أداء النظام
    /// </summary>
    public class PerformanceMonitorService
    {
        private readonly DatabaseContext _dbContext;
        private readonly MaintenanceRepository _maintenanceRepository;
        private readonly string _logFilePath;
        private readonly int _developerId;
        private Timer _monitorTimer;
        private readonly PerformanceSettings _settings;
        private readonly List<PerformanceCounter> _counters;
        private readonly List<PerformanceAlert> _alerts;

        /// <summary>
        /// إنشاء خدمة مراقبة الأداء
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        /// <param name="developerId">معرف المستخدم المطور</param>
        /// <param name="settings">إعدادات المراقبة</param>
        public PerformanceMonitorService(DatabaseContext dbContext, int developerId, PerformanceSettings settings = null)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _maintenanceRepository = new MaintenanceRepository(dbContext);
            _developerId = developerId;
            _settings = settings ?? new PerformanceSettings();
            _counters = new List<PerformanceCounter>();
            _alerts = new List<PerformanceAlert>();

            // إنشاء مجلد السجلات إذا لم يكن موجودًا
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // تحديد مسار ملف السجل
            _logFilePath = Path.Combine(logDirectory, "PerformanceMonitor.log");

            // تهيئة عدادات الأداء
            InitializePerformanceCounters();
        }

        /// <summary>
        /// تهيئة عدادات الأداء
        /// </summary>
        private void InitializePerformanceCounters()
        {
            try
            {
                // عداد استخدام وحدة المعالجة المركزية
                _counters.Add(new PerformanceCounter("Processor", "% Processor Time", "_Total"));

                // عداد استخدام الذاكرة المتاحة
                _counters.Add(new PerformanceCounter("Memory", "Available MBytes"));

                // عداد استخدام القرص
                _counters.Add(new PerformanceCounter("PhysicalDisk", "% Disk Time", "_Total"));

                // عداد طلبات SQL Server
                try
                {
                    _counters.Add(new PerformanceCounter("SQLServer:General Statistics", "User Connections"));
                }
                catch
                {
                    // تجاهل الخطأ إذا لم يكن SQL Server مثبتًا
                    LogMessage("تعذر تهيئة عداد SQL Server");
                }

                LogMessage("تم تهيئة عدادات الأداء بنجاح");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء تهيئة عدادات الأداء: {ex.Message}");
            }
        }

        /// <summary>
        /// بدء خدمة مراقبة الأداء
        /// </summary>
        public void Start()
        {
            try
            {
                // حساب الفترة الزمنية بالمللي ثانية
                int intervalMilliseconds = _settings.IntervalMinutes * 60 * 1000;

                // إنشاء مؤقت لمراقبة الأداء
                _monitorTimer = new Timer(async (state) => await MonitorPerformanceAsync(), null,
                    0, // التأخير الأولي
                    intervalMilliseconds); // الفترة الزمنية بين عمليات المراقبة

                LogMessage($"تم بدء خدمة مراقبة الأداء. الفترة الزمنية: {_settings.IntervalMinutes} دقيقة");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء بدء خدمة مراقبة الأداء: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إيقاف خدمة مراقبة الأداء
        /// </summary>
        public void Stop()
        {
            try
            {
                // إيقاف المؤقت
                _monitorTimer?.Dispose();
                _monitorTimer = null;

                // التخلص من عدادات الأداء
                foreach (var counter in _counters)
                {
                    counter.Dispose();
                }
                _counters.Clear();

                LogMessage("تم إيقاف خدمة مراقبة الأداء");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء إيقاف خدمة مراقبة الأداء: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// مراقبة أداء النظام
        /// </summary>
        private async Task MonitorPerformanceAsync()
        {
            try
            {
                LogMessage("بدء مراقبة أداء النظام...");

                // جمع بيانات الأداء
                PerformanceData data = CollectPerformanceData();

                // تحليل البيانات والتحقق من وجود مشاكل
                List<PerformanceAlert> newAlerts = AnalyzePerformanceData(data);

                // تسجيل التنبيهات الجديدة
                foreach (var alert in newAlerts)
                {
                    _alerts.Add(alert);
                    LogMessage($"تنبيه: {alert.AlertType} - {alert.Description}");

                    // تسجيل التنبيه في قاعدة البيانات
                    await _maintenanceRepository.LogMaintenanceOperationAsync(
                        _developerId,
                        $"تنبيه أداء: {alert.AlertType}",
                        alert.Description);

                    // إرسال إشعار بالتنبيه إذا تم تمكين هذه الميزة
                    if (_settings.SendAlertNotifications)
                    {
                        await SendAlertNotificationAsync(alert);
                    }
                }

                // تسجيل بيانات الأداء في قاعدة البيانات إذا تم تمكين هذه الميزة
                if (_settings.LogPerformanceData)
                {
                    await LogPerformanceDataAsync(data);
                }

                LogMessage("اكتملت مراقبة أداء النظام");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء مراقبة أداء النظام: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع بيانات الأداء
        /// </summary>
        private PerformanceData CollectPerformanceData()
        {
            PerformanceData data = new PerformanceData
            {
                Timestamp = DateTime.Now,
                Metrics = new Dictionary<string, float>()
            };

            try
            {
                // جمع بيانات من عدادات الأداء
                foreach (var counter in _counters)
                {
                    try
                    {
                        float value = counter.NextValue();
                        data.Metrics.Add(counter.CounterName, value);
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"حدث خطأ أثناء قراءة عداد {counter.CounterName}: {ex.Message}");
                    }
                }

                // جمع معلومات إضافية
                data.Metrics.Add("ProcessMemoryMB", Process.GetCurrentProcess().WorkingSet64 / 1024f / 1024f);
                data.Metrics.Add("ThreadCount", Process.GetCurrentProcess().Threads.Count);

                // جمع معلومات عن مساحة القرص
                DriveInfo drive = new DriveInfo(Path.GetPathRoot(AppDomain.CurrentDomain.BaseDirectory));
                data.Metrics.Add("DiskFreeSpaceGB", drive.AvailableFreeSpace / 1024f / 1024f / 1024f);
                data.Metrics.Add("DiskTotalSpaceGB", drive.TotalSize / 1024f / 1024f / 1024f);
                data.Metrics.Add("DiskUsagePercent", 100f - (drive.AvailableFreeSpace * 100f / drive.TotalSize));
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء جمع بيانات الأداء: {ex.Message}");
            }

            return data;
        }

        /// <summary>
        /// تحليل بيانات الأداء والتحقق من وجود مشاكل
        /// </summary>
        private List<PerformanceAlert> AnalyzePerformanceData(PerformanceData data)
        {
            List<PerformanceAlert> alerts = new List<PerformanceAlert>();

            try
            {
                // التحقق من استخدام وحدة المعالجة المركزية
                if (data.Metrics.TryGetValue("% Processor Time", out float cpuUsage) && cpuUsage > _settings.CpuThresholdPercent)
                {
                    alerts.Add(new PerformanceAlert
                    {
                        Timestamp = data.Timestamp,
                        AlertType = "استخدام وحدة المعالجة المركزية",
                        Description = $"استخدام وحدة المعالجة المركزية مرتفع: {cpuUsage:F2}%",
                        Severity = AlertSeverity.Warning
                    });
                }

                // التحقق من الذاكرة المتاحة
                if (data.Metrics.TryGetValue("Available MBytes", out float availableMemory) && availableMemory < _settings.MemoryThresholdMB)
                {
                    alerts.Add(new PerformanceAlert
                    {
                        Timestamp = data.Timestamp,
                        AlertType = "الذاكرة المتاحة",
                        Description = $"الذاكرة المتاحة منخفضة: {availableMemory:F2} ميجابايت",
                        Severity = AlertSeverity.Warning
                    });
                }

                // التحقق من استخدام القرص
                if (data.Metrics.TryGetValue("DiskUsagePercent", out float diskUsage) && diskUsage > _settings.DiskThresholdPercent)
                {
                    alerts.Add(new PerformanceAlert
                    {
                        Timestamp = data.Timestamp,
                        AlertType = "استخدام القرص",
                        Description = $"استخدام القرص مرتفع: {diskUsage:F2}%",
                        Severity = diskUsage > 95 ? AlertSeverity.Critical : AlertSeverity.Warning
                    });
                }

                // التحقق من مساحة القرص المتاحة
                if (data.Metrics.TryGetValue("DiskFreeSpaceGB", out float diskFreeSpace) && diskFreeSpace < _settings.DiskFreeSpaceThresholdGB)
                {
                    alerts.Add(new PerformanceAlert
                    {
                        Timestamp = data.Timestamp,
                        AlertType = "مساحة القرص المتاحة",
                        Description = $"مساحة القرص المتاحة منخفضة: {diskFreeSpace:F2} جيجابايت",
                        Severity = diskFreeSpace < 1 ? AlertSeverity.Critical : AlertSeverity.Warning
                    });
                }
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء تحليل بيانات الأداء: {ex.Message}");
            }

            return alerts;
        }

        /// <summary>
        /// تسجيل بيانات الأداء في قاعدة البيانات
        /// </summary>
        private async Task LogPerformanceDataAsync(PerformanceData data)
        {
            try
            {
                // إنشاء نص JSON لبيانات الأداء
                string metricsJson = Newtonsoft.Json.JsonConvert.SerializeObject(data.Metrics);

                // تسجيل البيانات في قاعدة البيانات
                string query = @"
                    INSERT INTO PerformanceLogs (Timestamp, MetricsJson)
                    VALUES (@Timestamp, @MetricsJson)";

                System.Data.SqlClient.SqlParameter[] parameters = new System.Data.SqlClient.SqlParameter[]
                {
                    new System.Data.SqlClient.SqlParameter("@Timestamp", data.Timestamp),
                    new System.Data.SqlClient.SqlParameter("@MetricsJson", metricsJson)
                };

                await _dbContext.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء تسجيل بيانات الأداء: {ex.Message}");
            }
        }

        /// <summary>
        /// إرسال إشعار بالتنبيه
        /// </summary>
        private async Task SendAlertNotificationAsync(PerformanceAlert alert)
        {
            try
            {
                // هذه مجرد محاكاة لإرسال الإشعار
                // في التطبيق الفعلي، يمكن إرسال بريد إلكتروني أو إشعار عبر تطبيق جوال
                LogMessage($"تم إرسال إشعار بالتنبيه: {alert.AlertType} - {alert.Description}");

                // تسجيل العملية
                await _maintenanceRepository.LogMaintenanceOperationAsync(
                    _developerId,
                    "إرسال إشعار تنبيه",
                    $"تم إرسال إشعار بالتنبيه: {alert.AlertType} - {alert.Description}");
            }
            catch (Exception ex)
            {
                LogMessage($"حدث خطأ أثناء إرسال إشعار التنبيه: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل رسالة في ملف السجل
        /// </summary>
        private void LogMessage(string message)
        {
            try
            {
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
                File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
            }
            catch
            {
                // تجاهل أي خطأ أثناء تسجيل الرسالة
            }
        }
    }

    /// <summary>
    /// بيانات أداء النظام
    /// </summary>
    public class PerformanceData
    {
        /// <summary>
        /// وقت جمع البيانات
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// مقاييس الأداء
        /// </summary>
        public Dictionary<string, float> Metrics { get; set; }
    }

    /// <summary>
    /// تنبيه أداء النظام
    /// </summary>
    public class PerformanceAlert
    {
        /// <summary>
        /// وقت التنبيه
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// نوع التنبيه
        /// </summary>
        public string AlertType { get; set; }

        /// <summary>
        /// وصف التنبيه
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// خطورة التنبيه
        /// </summary>
        public AlertSeverity Severity { get; set; }
    }

    /// <summary>
    /// خطورة التنبيه
    /// </summary>
    public enum AlertSeverity
    {
        /// <summary>
        /// معلومات
        /// </summary>
        Info,

        /// <summary>
        /// تحذير
        /// </summary>
        Warning,

        /// <summary>
        /// خطأ
        /// </summary>
        Error,

        /// <summary>
        /// حرج
        /// </summary>
        Critical
    }

    /// <summary>
    /// إعدادات مراقبة الأداء
    /// </summary>
    public class PerformanceSettings
    {
        /// <summary>
        /// الفترة الزمنية بين عمليات المراقبة (بالدقائق)
        /// </summary>
        public int IntervalMinutes { get; set; } = 5;

        /// <summary>
        /// حد استخدام وحدة المعالجة المركزية (بالنسبة المئوية)
        /// </summary>
        public float CpuThresholdPercent { get; set; } = 80;

        /// <summary>
        /// حد الذاكرة المتاحة (بالميجابايت)
        /// </summary>
        public float MemoryThresholdMB { get; set; } = 500;

        /// <summary>
        /// حد استخدام القرص (بالنسبة المئوية)
        /// </summary>
        public float DiskThresholdPercent { get; set; } = 90;

        /// <summary>
        /// حد مساحة القرص المتاحة (بالجيجابايت)
        /// </summary>
        public float DiskFreeSpaceThresholdGB { get; set; } = 5;

        /// <summary>
        /// تسجيل بيانات الأداء في قاعدة البيانات
        /// </summary>
        public bool LogPerformanceData { get; set; } = true;

        /// <summary>
        /// إرسال إشعارات بالتنبيهات
        /// </summary>
        public bool SendAlertNotifications { get; set; } = true;

        /// <summary>
        /// إنشاء تقارير أداء دورية
        /// </summary>
        public bool GeneratePerformanceReports { get; set; } = false;

        /// <summary>
        /// الفترة الزمنية للتقارير
        /// </summary>
        public ReportInterval ReportInterval { get; set; } = ReportInterval.Weekly;

        /// <summary>
        /// تنسيق التقرير
        /// </summary>
        public ReportFormat ReportFormat { get; set; } = ReportFormat.PDF;

        /// <summary>
        /// مجلد التقارير
        /// </summary>
        public string ReportsDirectory { get; set; } = "";
    }

    /// <summary>
    /// الفترة الزمنية للتقارير
    /// </summary>
    public enum ReportInterval
    {
        /// <summary>
        /// يومي
        /// </summary>
        Daily,

        /// <summary>
        /// أسبوعي
        /// </summary>
        Weekly,

        /// <summary>
        /// شهري
        /// </summary>
        Monthly
    }

    /// <summary>
    /// تنسيق التقرير
    /// </summary>
    public enum ReportFormat
    {
        /// <summary>
        /// PDF
        /// </summary>
        PDF,

        /// <summary>
        /// Excel
        /// </summary>
        Excel,

        /// <summary>
        /// HTML
        /// </summary>
        HTML
    }
}
