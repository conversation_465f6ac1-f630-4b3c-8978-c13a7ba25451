using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج الطلب
    /// </summary>
    public class Order
    {
        public int Id { get; set; }

        [Required]
        public DateTime OrderDate { get; set; }

        [Required]
        public decimal TotalAmount { get; set; }

        public decimal? Discount { get; set; }

        [Required]
        public decimal FinalAmount { get; set; }

        [Required]
        public decimal PaidAmount { get; set; }

        [Required]
        public decimal RemainingAmount { get; set; }

        [Required]
        public OrderStatus Status { get; set; }

        [Required]
        public PaymentType PaymentType { get; set; }

        [MaxLength(500)]
        public string Notes { get; set; }

        public int? CustomerId { get; set; }
        public Customer Customer { get; set; }

        public int UserId { get; set; }
        public User User { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // العلاقات
        public ICollection<OrderItem> OrderItems { get; set; }
        public ICollection<Payment> Payments { get; set; }
        public InstallmentPlan InstallmentPlan { get; set; }
    }

    public enum OrderStatus
    {
        Pending = 0,
        Completed = 1,
        PartiallyPaid = 2,
        Cancelled = 3
    }

    public enum PaymentType
    {
        Cash = 0,
        Card = 1,
        BankTransfer = 2,
        Installment = 3,
        Mixed = 4
    }
}
