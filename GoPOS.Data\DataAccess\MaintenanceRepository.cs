using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using System.Linq;
using GoPOS.Data.Models;

namespace GoPOS.Data.DataAccess
{
    /// <summary>
    /// مستودع الصيانة
    /// </summary>
    public class MaintenanceRepository
    {
        private readonly DatabaseContext _dbContext;
        private readonly string _connectionString;
        private readonly string _backupDirectory;

        /// <summary>
        /// إنشاء مستودع الصيانة
        /// </summary>
        /// <param name="dbContext">سياق قاعدة البيانات</param>
        public MaintenanceRepository(DatabaseContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _connectionString = _dbContext.ConnectionString;
            
            // تحديد مسار النسخ الاحتياطي
            string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _backupDirectory = Path.Combine(appDirectory, "Backups");
            
            // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية لقاعدة البيانات
        /// </summary>
        /// <returns>مسار ملف النسخة الاحتياطية</returns>
        public async Task<string> CreateBackupAsync()
        {
            try
            {
                // استخراج اسم قاعدة البيانات من سلسلة الاتصال
                string databaseName = GetDatabaseNameFromConnectionString(_connectionString);
                if (string.IsNullOrEmpty(databaseName))
                {
                    throw new InvalidOperationException("لم يتم العثور على اسم قاعدة البيانات في سلسلة الاتصال");
                }

                // إنشاء اسم ملف النسخة الاحتياطية
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string backupFileName = $"{databaseName}_{timestamp}.bak";
                string backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                // استعلام إنشاء النسخة الاحتياطية
                string query = $@"
                    BACKUP DATABASE [{databaseName}] 
                    TO DISK = @BackupPath 
                    WITH FORMAT, MEDIANAME = 'GoPOS_Backup', 
                    NAME = 'GoPOS Full Backup'";

                SqlParameter[] parameters = new SqlParameter[]
                {
                    new SqlParameter("@BackupPath", backupFilePath)
                };

                // تنفيذ الاستعلام
                await _dbContext.ExecuteNonQueryAsync(query, parameters);

                return backupFilePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// </summary>
        /// <param name="backupFilePath">مسار ملف النسخة الاحتياطية</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> RestoreBackupAsync(string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود", backupFilePath);
                }

                // استخراج اسم قاعدة البيانات من سلسلة الاتصال
                string databaseName = GetDatabaseNameFromConnectionString(_connectionString);
                if (string.IsNullOrEmpty(databaseName))
                {
                    throw new InvalidOperationException("لم يتم العثور على اسم قاعدة البيانات في سلسلة الاتصال");
                }

                // استعلام استعادة النسخة الاحتياطية
                string query = $@"
                    USE [master];
                    ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
                    RESTORE DATABASE [{databaseName}] 
                    FROM DISK = @BackupPath 
                    WITH REPLACE, RECOVERY;
                    ALTER DATABASE [{databaseName}] SET MULTI_USER;";

                SqlParameter[] parameters = new SqlParameter[]
                {
                    new SqlParameter("@BackupPath", backupFilePath)
                };

                // تنفيذ الاستعلام
                return await _dbContext.ExecuteNonQueryAsync(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية المتاحة
        /// </summary>
        /// <returns>قائمة بمسارات ملفات النسخ الاحتياطية</returns>
        public List<string> GetAvailableBackups()
        {
            try
            {
                if (!Directory.Exists(_backupDirectory))
                {
                    return new List<string>();
                }

                // البحث عن ملفات النسخ الاحتياطية
                string[] backupFiles = Directory.GetFiles(_backupDirectory, "*.bak");
                return backupFiles.OrderByDescending(f => new FileInfo(f).CreationTime).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء الحصول على قائمة النسخ الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إصلاح قاعدة البيانات
        /// </summary>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> RepairDatabaseAsync()
        {
            try
            {
                // استخراج اسم قاعدة البيانات من سلسلة الاتصال
                string databaseName = GetDatabaseNameFromConnectionString(_connectionString);
                if (string.IsNullOrEmpty(databaseName))
                {
                    throw new InvalidOperationException("لم يتم العثور على اسم قاعدة البيانات في سلسلة الاتصال");
                }

                // استعلام إصلاح قاعدة البيانات
                string query = $@"
                    USE [master];
                    ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
                    DBCC CHECKDB ([{databaseName}], REPAIR_ALLOW_DATA_LOSS) WITH NO_INFOMSGS;
                    ALTER DATABASE [{databaseName}] SET MULTI_USER;";

                // تنفيذ الاستعلام
                return await _dbContext.ExecuteNonQueryAsync(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء إصلاح قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ استعلام SQL مباشر (للمطورين فقط)
        /// </summary>
        /// <param name="query">استعلام SQL</param>
        /// <returns>نتيجة الاستعلام</returns>
        public async Task<DataTable> ExecuteDirectQueryAsync(string query)
        {
            try
            {
                if (string.IsNullOrEmpty(query))
                {
                    throw new ArgumentException("الاستعلام لا يمكن أن يكون فارغًا");
                }

                // تنفيذ الاستعلام
                return await _dbContext.ExecuteQueryAsync(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تسجيل عملية صيانة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="operationType">نوع العملية</param>
        /// <param name="details">تفاصيل العملية</param>
        /// <returns>معرف السجل</returns>
        public async Task<int> LogMaintenanceOperationAsync(int userId, string operationType, string details)
        {
            try
            {
                string query = @"
                    INSERT INTO MaintenanceLogs (UserId, OperationType, Details, OperationDate)
                    VALUES (@UserId, @OperationType, @Details, @OperationDate);
                    SELECT SCOPE_IDENTITY();";

                SqlParameter[] parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OperationType", operationType),
                    new SqlParameter("@Details", details),
                    new SqlParameter("@OperationDate", DateTime.Now)
                };

                object result = await _dbContext.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء تسجيل عملية الصيانة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// استخراج اسم قاعدة البيانات من سلسلة الاتصال
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <returns>اسم قاعدة البيانات</returns>
        private string GetDatabaseNameFromConnectionString(string connectionString)
        {
            try
            {
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(connectionString);
                return builder.InitialCatalog;
            }
            catch
            {
                return null;
            }
        }
    }
}
