﻿#pragma checksum "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7A14D705647E023E2963EA2CB7C3D6A964EA6A3A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// InventoryCheckDetailsWindow
    /// </summary>
    public partial class InventoryCheckDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 39 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CheckNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker CheckDatePicker;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WarehouseComboBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddProductButton;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveProductButton;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportProductsButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ProductsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/inventorycheckdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CheckNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CheckDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.WarehouseComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.AddProductButton = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
            this.AddProductButton.Click += new System.Windows.RoutedEventHandler(this.AddProductButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.RemoveProductButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
            this.RemoveProductButton.Click += new System.Windows.RoutedEventHandler(this.RemoveProductButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ImportProductsButton = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
            this.ImportProductsButton.Click += new System.Windows.RoutedEventHandler(this.ImportProductsButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ProductsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 83 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
            this.ProductsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProductsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\..\Views\InventoryCheckDetailsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

