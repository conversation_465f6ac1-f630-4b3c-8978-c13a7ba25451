﻿#pragma checksum "..\..\..\..\Views\ExpensesView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "91D48AA5F25BC08D4652CFB82D025EF38B775DAB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// ExpensesView
    /// </summary>
    public partial class ExpensesView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 31 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddExpenseButton;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FilterTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FilterDatePicker;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ExpensesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoExpensesMessage;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadingIndicator;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CashRegisterNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentBalanceTextBlock;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalExpensesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResponsibleUserTextBlock;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewCashRegisterButton;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDepositButton;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddWithdrawalButton;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\ExpensesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintReportButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/expensesview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ExpensesView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddExpenseButton = ((System.Windows.Controls.Button)(target));
            
            #line 31 "..\..\..\..\Views\ExpensesView.xaml"
            this.AddExpenseButton.Click += new System.Windows.RoutedEventHandler(this.AddExpenseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\Views\ExpensesView.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 95 "..\..\..\..\Views\ExpensesView.xaml"
            this.SearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.SearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\..\Views\ExpensesView.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.FilterTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 100 "..\..\..\..\Views\ExpensesView.xaml"
            this.FilterTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FilterTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FilterDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 110 "..\..\..\..\Views\ExpensesView.xaml"
            this.FilterDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.FilterDatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ExpensesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 116 "..\..\..\..\Views\ExpensesView.xaml"
            this.ExpensesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ExpensesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.NoExpensesMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LoadingIndicator = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.CashRegisterNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.CurrentBalanceTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TotalExpensesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.LastUpdateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ResponsibleUserTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ViewCashRegisterButton = ((System.Windows.Controls.Button)(target));
            
            #line 201 "..\..\..\..\Views\ExpensesView.xaml"
            this.ViewCashRegisterButton.Click += new System.Windows.RoutedEventHandler(this.ViewCashRegisterButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.AddDepositButton = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\Views\ExpensesView.xaml"
            this.AddDepositButton.Click += new System.Windows.RoutedEventHandler(this.AddDepositButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.AddWithdrawalButton = ((System.Windows.Controls.Button)(target));
            
            #line 233 "..\..\..\..\Views\ExpensesView.xaml"
            this.AddWithdrawalButton.Click += new System.Windows.RoutedEventHandler(this.AddWithdrawalButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.PrintReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 249 "..\..\..\..\Views\ExpensesView.xaml"
            this.PrintReportButton.Click += new System.Windows.RoutedEventHandler(this.PrintReportButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 8:
            
            #line 129 "..\..\..\..\Views\ExpensesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditExpenseButton_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 130 "..\..\..\..\Views\ExpensesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteExpenseButton_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 131 "..\..\..\..\Views\ExpensesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintExpenseButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

