﻿#pragma checksum "..\..\..\..\Views\CategoriesManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7DD861B84C5BA934749637E47ABAF7BAECCB9137"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// CategoriesManagementView
    /// </summary>
    public partial class CategoriesManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 31 "..\..\..\..\Views\CategoriesManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCategoryButton;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\CategoriesManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\Views\CategoriesManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CategoriesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Views\CategoriesManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditCategoryButton;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Views\CategoriesManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteCategoryButton;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\CategoriesManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewProductsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/categoriesmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CategoriesManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddCategoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\..\Views\CategoriesManagementView.xaml"
            this.AddCategoryButton.Click += new System.Windows.RoutedEventHandler(this.AddCategoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\Views\CategoriesManagementView.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CategoriesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 84 "..\..\..\..\Views\CategoriesManagementView.xaml"
            this.CategoriesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CategoriesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.EditCategoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\..\Views\CategoriesManagementView.xaml"
            this.EditCategoryButton.Click += new System.Windows.RoutedEventHandler(this.EditCategoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DeleteCategoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 203 "..\..\..\..\Views\CategoriesManagementView.xaml"
            this.DeleteCategoryButton.Click += new System.Windows.RoutedEventHandler(this.DeleteCategoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ViewProductsButton = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\..\Views\CategoriesManagementView.xaml"
            this.ViewProductsButton.Click += new System.Windows.RoutedEventHandler(this.ViewProductsButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

