using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GoPOS.UI.ViewModels
{
    /// <summary>
    /// نموذج عرض خطة الأقساط
    /// </summary>
    public class InstallmentPlanViewModel : INotifyPropertyChanged
    {
        private int _id;
        private int _orderId;
        private int _customerId;
        private string _customerName = string.Empty;
        private decimal _totalAmount;
        private decimal _downPayment;
        private decimal _remainingAmount;
        private int _numberOfInstallments;
        private int _frequency;
        private DateTime _startDate;
        private int _status;
        private string _statusText = string.Empty;
        private string _canCancel = "Visible";
        private List<InstallmentViewModel> _installments;

        /// <summary>
        /// معرف خطة الأقساط
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// معرف الطلب
        /// </summary>
        public int OrderId
        {
            get => _orderId;
            set
            {
                if (_orderId != value)
                {
                    _orderId = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName
        {
            get => _customerName;
            set
            {
                if (_customerName != value)
                {
                    _customerName = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// المبلغ الإجمالي
        /// </summary>
        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                if (_totalAmount != value)
                {
                    _totalAmount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// الدفعة المقدمة
        /// </summary>
        public decimal DownPayment
        {
            get => _downPayment;
            set
            {
                if (_downPayment != value)
                {
                    _downPayment = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount
        {
            get => _remainingAmount;
            set
            {
                if (_remainingAmount != value)
                {
                    _remainingAmount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// عدد الأقساط
        /// </summary>
        public int NumberOfInstallments
        {
            get => _numberOfInstallments;
            set
            {
                if (_numberOfInstallments != value)
                {
                    _numberOfInstallments = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// تكرار الأقساط
        /// </summary>
        public int Frequency
        {
            get => _frequency;
            set
            {
                if (_frequency != value)
                {
                    _frequency = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// تاريخ البدء
        /// </summary>
        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (_startDate != value)
                {
                    _startDate = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حالة خطة الأقساط
        /// </summary>
        public int Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// نص حالة خطة الأقساط
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set
            {
                if (_statusText != value)
                {
                    _statusText = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// إمكانية إلغاء خطة الأقساط
        /// </summary>
        public string CanCancel
        {
            get => _canCancel;
            set
            {
                if (_canCancel != value)
                {
                    _canCancel = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// قائمة الأقساط
        /// </summary>
        public List<InstallmentViewModel> Installments
        {
            get => _installments;
            set
            {
                if (_installments != value)
                {
                    _installments = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// حدث تغيير الخاصية
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// إشعار بتغيير الخاصية
        /// </summary>
        /// <param name="propertyName">اسم الخاصية</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
