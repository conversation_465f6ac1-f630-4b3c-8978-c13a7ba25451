﻿#pragma checksum "..\..\..\..\Views\InstallmentPlanWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "852484565E2288E9F11A053B235AC774B2B52E45"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// InstallmentPlanWindow
    /// </summary>
    public partial class InstallmentPlanWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 48 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OrderIdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerComboBox;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DownPaymentTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NumberOfInstallmentsComboBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FrequencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstallmentAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InterestRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DueDayComboBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InstallmentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintPreviewButton;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AdjustDatesButton;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/installmentplanwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.OrderIdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CustomerComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 51 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.CustomerComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CustomerComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TotalAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.DownPaymentTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 58 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.DownPaymentTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.DownPaymentTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.NumberOfInstallmentsComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 83 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.NumberOfInstallmentsComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.NumberOfInstallmentsComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FrequencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 93 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.FrequencyComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FrequencyComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.StartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 101 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.StartDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.StartDatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.InstallmentAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.InterestRateTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 108 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.InterestRateTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.InterestRateTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DueDayComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 112 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.DueDayComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DueDayComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.InstallmentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.PrintPreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.PrintPreviewButton.Click += new System.Windows.RoutedEventHandler(this.PrintPreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.AdjustDatesButton = ((System.Windows.Controls.Button)(target));
            
            #line 244 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.AdjustDatesButton.Click += new System.Windows.RoutedEventHandler(this.AdjustDatesButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 264 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 281 "..\..\..\..\Views\InstallmentPlanWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

