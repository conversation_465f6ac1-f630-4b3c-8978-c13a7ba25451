<Window x:Class="GoPOS.UI.Views.ProductSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="اختيار منتج" Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="اختيار منتج" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- شريط البحث -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="بحث:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox x:Name="SearchTextBox" Grid.Column="1" Height="30" Margin="0,0,10,0" KeyDown="SearchTextBox_KeyDown"/>
            
            <TextBlock Grid.Column="2" Text="الفئة:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <ComboBox x:Name="CategoryComboBox" Grid.Column="3" Width="150" Height="30" SelectionChanged="CategoryComboBox_SelectionChanged">
                <ComboBoxItem Content="الكل" IsSelected="True"/>
                <ComboBoxItem Content="مشروبات"/>
                <ComboBoxItem Content="وجبات سريعة"/>
                <ComboBoxItem Content="حلويات"/>
            </ComboBox>
        </Grid>

        <!-- قائمة المنتجات -->
        <DataGrid x:Name="ProductsDataGrid" Grid.Row="2" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectionChanged="ProductsDataGrid_SelectionChanged" MouseDoubleClick="ProductsDataGrid_MouseDoubleClick">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*"/>
                <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="120"/>
                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat={}{0:N2}}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button x:Name="SelectButton" Content="اختيار" Width="100" Height="40" Margin="0,0,10,0" Click="SelectButton_Click" IsEnabled="False"/>
            <Button x:Name="CancelButton" Content="إلغاء" Width="100" Height="40" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
