using System;
using System.ComponentModel.DataAnnotations;

namespace GoPOS.Core.Models
{
    /// <summary>
    /// نموذج عنصر الجرد
    /// </summary>
    public class InventoryCheckItem
    {
        public int Id { get; set; }

        [Required]
        public int InventoryCheckId { get; set; }
        public InventoryCheck InventoryCheck { get; set; }

        [Required]
        public int ProductId { get; set; }
        public Product Product { get; set; }

        [Required]
        public int SystemQuantity { get; set; }

        public int? ActualQuantity { get; set; }

        public int? Difference { get; set; }

        public bool IsChecked { get; set; }

        [MaxLength(200)]
        public string Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
