<UserControl x:Class="GoPOS.UI.Views.InventoryManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GoPOS.UI.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان وشريط الأدوات -->
        <Grid Grid.Row="0" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="إدارة المخزون" FontSize="24" FontWeight="Bold"/>

            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <ComboBox x:Name="WarehouseComboBox" Width="150" Height="30" Margin="0,0,10,0" SelectionChanged="WarehouseComboBox_SelectionChanged">
                    <ComboBoxItem Content="جميع المخازن" IsSelected="True"/>
                    <ComboBoxItem Content="المخزن الرئيسي"/>
                    <ComboBoxItem Content="مخزن الفرع 1"/>
                    <ComboBoxItem Content="مخزن الفرع 2"/>
                </ComboBox>
                <ComboBox x:Name="CategoryFilterComboBox" Width="150" Height="30" Margin="0,0,10,0" SelectionChanged="CategoryFilterComboBox_SelectionChanged">
                    <ComboBoxItem Content="جميع الفئات" IsSelected="True"/>
                    <ComboBoxItem Content="مشروبات"/>
                    <ComboBoxItem Content="وجبات سريعة"/>
                    <ComboBoxItem Content="حلويات"/>
                </ComboBox>
                <TextBox x:Name="SearchTextBox" Width="200" Height="30" Margin="0,0,10,0" KeyDown="SearchTextBox_KeyDown"/>
                <Button x:Name="SearchButton" Content="بحث" Width="80" Height="30" Click="SearchButton_Click"/>
            </StackPanel>
        </Grid>

        <!-- محتوى إدارة المخزون -->
        <TabControl Grid.Row="1">
            <!-- المخزون الحالي -->
            <TabItem Header="المخزون الحالي">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شريط أدوات المخزون -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,10">
                        <Button x:Name="AddStockButton" Content="إضافة مخزون" Width="120" Height="30" Margin="0,0,10,0" Click="AddStockButton_Click"/>
                        <Button x:Name="AdjustStockButton" Content="تعديل المخزون" Width="120" Height="30" Margin="0,0,10,0" Click="AdjustStockButton_Click"/>
                        <Button x:Name="TransferStockButton" Content="نقل المخزون" Width="120" Height="30" Margin="0,0,10,0" Click="TransferStockButton_Click"/>
                        <Button x:Name="ExportInventoryButton" Content="تصدير" Width="80" Height="30" Margin="0,0,10,0" Click="ExportInventoryButton_Click"/>
                        <Button x:Name="PrintInventoryButton" Content="طباعة" Width="80" Height="30" Click="PrintInventoryButton_Click"/>
                    </StackPanel>

                    <!-- جدول المخزون -->
                    <DataGrid x:Name="InventoryDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Single" SelectionChanged="InventoryDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60"/>
                            <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                            <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="الفئة" Binding="{Binding CategoryName}" Width="100"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinimumQuantity}" Width="80"/>
                            <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="100"/>
                            <DataGridTextColumn Header="سعر التكلفة" Binding="{Binding CostPrice, StringFormat={}{0:N2}}" Width="100"/>
                            <DataGridTextColumn Header="سعر البيع" Binding="{Binding SellingPrice, StringFormat={}{0:N2}}" Width="100"/>
                            <DataGridTextColumn Header="آخر تحديث" Binding="{Binding LastUpdated, StringFormat={}{0:yyyy/MM/dd}}" Width="100"/>
                            <DataGridTemplateColumn Header="الحالة" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding StatusColor}" CornerRadius="3" Padding="5,2">
                                            <TextBlock Text="{Binding StatusText}" Foreground="White" HorizontalAlignment="Center"/>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- ملخص المخزون -->
                    <Grid Grid.Row="2" Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#4CAF50" CornerRadius="5" Margin="0,0,10,0" Padding="10,5">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="إجمالي المنتجات: " Foreground="White"/>
                                <TextBlock x:Name="TotalProductsTextBlock" Text="0" Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Background="#2196F3" CornerRadius="5" Margin="0,0,10,0" Padding="10,5">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="إجمالي المخزون: " Foreground="White"/>
                                <TextBlock x:Name="TotalQuantityTextBlock" Text="0" Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Background="#F44336" CornerRadius="5" Padding="10,5">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="منتجات تحت الحد الأدنى: " Foreground="White"/>
                                <TextBlock x:Name="LowStockTextBlock" Text="0" Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- حركة المخزون -->
            <TabItem Header="حركة المخزون">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط أدوات حركة المخزون -->
                    <Grid Grid.Row="0" Margin="0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <DatePicker x:Name="FromDatePicker" Grid.Column="1" Width="120" Height="30" Margin="0,0,10,0" SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                        
                        <TextBlock Grid.Column="2" Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <DatePicker x:Name="ToDatePicker" Grid.Column="3" Width="120" Height="30" Margin="0,0,10,0" SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                        <ComboBox x:Name="MovementTypeComboBox" Grid.Column="5" Width="150" Height="30" SelectionChanged="MovementTypeComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع الحركات" IsSelected="True"/>
                            <ComboBoxItem Content="إضافة مخزون"/>
                            <ComboBoxItem Content="تعديل مخزون"/>
                            <ComboBoxItem Content="نقل مخزون"/>
                            <ComboBoxItem Content="بيع"/>
                            <ComboBoxItem Content="مرتجع"/>
                        </ComboBox>
                    </Grid>

                    <!-- جدول حركة المخزون -->
                    <DataGrid x:Name="StockMovementDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الحركة" Binding="{Binding Id}" Width="80"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat={}{0:yyyy/MM/dd HH:mm}}" Width="120"/>
                            <DataGridTextColumn Header="نوع الحركة" Binding="{Binding TypeText}" Width="100"/>
                            <DataGridTextColumn Header="المنتج" Binding="{Binding ProductName}" Width="*"/>
                            <DataGridTextColumn Header="الكمية قبل" Binding="{Binding QuantityBefore}" Width="80"/>
                            <DataGridTextColumn Header="الكمية بعد" Binding="{Binding QuantityAfter}" Width="80"/>
                            <DataGridTextColumn Header="التغيير" Binding="{Binding Change}" Width="80"/>
                            <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="100"/>
                            <DataGridTextColumn Header="المستخدم" Binding="{Binding UserName}" Width="100"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- تنبيهات المخزون -->
            <TabItem Header="تنبيهات المخزون">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- شريط أدوات التنبيهات -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,10">
                        <Button x:Name="MarkAsReadButton" Content="تعليم كمقروء" Width="120" Height="30" Margin="0,0,10,0" Click="MarkAsReadButton_Click"/>
                        <Button x:Name="DeleteAlertsButton" Content="حذف التنبيهات" Width="120" Height="30" Margin="0,0,10,0" Click="DeleteAlertsButton_Click"/>
                        <Button x:Name="RefreshAlertsButton" Content="تحديث" Width="80" Height="30" Click="RefreshAlertsButton_Click"/>
                    </StackPanel>

                    <!-- جدول التنبيهات -->
                    <DataGrid x:Name="AlertsDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True" SelectionMode="Extended">
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Width="30">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox IsChecked="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Header="الحالة" Width="30">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Ellipse Width="12" Height="12" Fill="{Binding StatusColor}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat={}{0:yyyy/MM/dd HH:mm}}" Width="120"/>
                            <DataGridTextColumn Header="نوع التنبيه" Binding="{Binding TypeText}" Width="100"/>
                            <DataGridTextColumn Header="المنتج" Binding="{Binding ProductName}" Width="*"/>
                            <DataGridTextColumn Header="الكمية الحالية" Binding="{Binding CurrentQuantity}" Width="100"/>
                            <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinimumQuantity}" Width="100"/>
                            <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="100"/>
                            <DataGridTemplateColumn Header="إجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="إضافة مخزون" Margin="0,0,5,0" Click="AddStockFromAlertButton_Click" Tag="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
