using System;
using System.Text.RegularExpressions;
using System.Windows;
using GoPOS.Core.Models;

namespace GoPOS.UI.Views
{
    /// <summary>
    /// Interaction logic for UserEditorWindow.xaml
    /// </summary>
    public partial class UserEditorWindow : Window
    {
        private UserViewModel _user;
        private bool _isEditMode;

        public UserEditorWindow()
        {
            InitializeComponent();
            _isEditMode = false;
            _user = new UserViewModel
            {
                Id = 0,
                UserName = "",
                FullName = "",
                Email = "",
                Role = "كاشير",
                RoleEnum = UserRole.Cashier,
                IsActive = true,
                Permissions = new UserPermissions()
            };

            // تعيين الصلاحيات الافتراضية للكاشير
            SetDefaultPermissionsForRole(UserRole.Cashier);
        }

        public UserEditorWindow(UserViewModel user)
        {
            InitializeComponent();
            _isEditMode = true;
            _user = user ?? throw new ArgumentNullException(nameof(user));
            TitleTextBlock.Text = "تعديل المستخدم";

            // إخفاء حقول كلمة المرور في وضع التعديل
            PasswordPanel.Visibility = Visibility.Collapsed;
            ConfirmPasswordPanel.Visibility = Visibility.Collapsed;

            // تحميل بيانات المستخدم
            LoadUserData();
        }

        private void LoadUserData()
        {
            try
            {
                // تعبئة حقول النموذج بالبيانات الحالية
                UsernameTextBox.Text = _user.UserName;
                FullNameTextBox.Text = _user.FullName;
                EmailTextBox.Text = _user.Email ?? string.Empty;
                IsActiveCheckBox.IsChecked = _user.IsActive;

                // تحديد الدور
                switch (_user.RoleEnum)
                {
                    case UserRole.Administrator:
                        RoleComboBox.SelectedIndex = 0;
                        break;
                    case UserRole.Manager:
                        RoleComboBox.SelectedIndex = 1;
                        break;
                    case UserRole.Cashier:
                        RoleComboBox.SelectedIndex = 2;
                        break;
                }

                // تحديد الصلاحيات
                if (_user.Permissions != null)
                {
                    // صلاحيات المبيعات
                    SalesPermissionCheckBox.IsChecked = _user.Permissions.CanManageSales;
                    InvoicesPermissionCheckBox.IsChecked = _user.Permissions.CanManageInvoices;
                    ReturnInvoicesPermissionCheckBox.IsChecked = _user.Permissions.CanReturnInvoices;

                    // صلاحيات المخزون
                    ProductsPermissionCheckBox.IsChecked = _user.Permissions.CanManageProducts;
                    CategoriesPermissionCheckBox.IsChecked = _user.Permissions.CanManageCategories;
                    InventoryPermissionCheckBox.IsChecked = _user.Permissions.CanManageInventory;

                    // صلاحيات النظام
                    UsersPermissionCheckBox.IsChecked = _user.Permissions.CanManageUsers;
                    ReportsPermissionCheckBox.IsChecked = _user.Permissions.CanViewReports;
                    SettingsPermissionCheckBox.IsChecked = _user.Permissions.CanManageSettings;
                }
                else
                {
                    // تعيين الصلاحيات الافتراضية للدور
                    SetDefaultPermissionsForRole(_user.RoleEnum);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetDefaultPermissionsForRole(UserRole role)
        {
            switch (role)
            {
                case UserRole.Administrator:
                    // المدير له جميع الصلاحيات
                    SalesPermissionCheckBox.IsChecked = true;
                    InvoicesPermissionCheckBox.IsChecked = true;
                    ReturnInvoicesPermissionCheckBox.IsChecked = true;
                    ProductsPermissionCheckBox.IsChecked = true;
                    CategoriesPermissionCheckBox.IsChecked = true;
                    InventoryPermissionCheckBox.IsChecked = true;
                    UsersPermissionCheckBox.IsChecked = true;
                    ReportsPermissionCheckBox.IsChecked = true;
                    SettingsPermissionCheckBox.IsChecked = true;
                    break;

                case UserRole.Manager:
                    // المدير له معظم الصلاحيات ما عدا إدارة المستخدمين وإعدادات النظام
                    SalesPermissionCheckBox.IsChecked = true;
                    InvoicesPermissionCheckBox.IsChecked = true;
                    ReturnInvoicesPermissionCheckBox.IsChecked = true;
                    ProductsPermissionCheckBox.IsChecked = true;
                    CategoriesPermissionCheckBox.IsChecked = true;
                    InventoryPermissionCheckBox.IsChecked = true;
                    UsersPermissionCheckBox.IsChecked = false;
                    ReportsPermissionCheckBox.IsChecked = true;
                    SettingsPermissionCheckBox.IsChecked = false;
                    break;

                case UserRole.Cashier:
                    // الكاشير له صلاحيات محدودة
                    SalesPermissionCheckBox.IsChecked = true;
                    InvoicesPermissionCheckBox.IsChecked = true;
                    ReturnInvoicesPermissionCheckBox.IsChecked = false;
                    ProductsPermissionCheckBox.IsChecked = false;
                    CategoriesPermissionCheckBox.IsChecked = false;
                    InventoryPermissionCheckBox.IsChecked = false;
                    UsersPermissionCheckBox.IsChecked = false;
                    ReportsPermissionCheckBox.IsChecked = false;
                    SettingsPermissionCheckBox.IsChecked = false;
                    break;
            }
        }

        private void RoleComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            if (RoleComboBox.SelectedItem != null)
            {
                var selectedItem = RoleComboBox.SelectedItem as System.Windows.Controls.ComboBoxItem;
                if (selectedItem != null)
                {
                    UserRole selectedRole = (UserRole)Enum.Parse(typeof(UserRole), selectedItem.Tag.ToString());
                    SetDefaultPermissionsForRole(selectedRole);
                }
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                {
                    return;
                }

                // إظهار مؤشر الانتظار
                Cursor = System.Windows.Input.Cursors.Wait;

                // جمع البيانات من النموذج
                _user.UserName = UsernameTextBox.Text.Trim();
                _user.FullName = FullNameTextBox.Text.Trim();
                _user.Email = EmailTextBox.Text.Trim();
                _user.IsActive = IsActiveCheckBox.IsChecked ?? true;

                // تحديد الدور
                var selectedRoleItem = RoleComboBox.SelectedItem as System.Windows.Controls.ComboBoxItem;
                if (selectedRoleItem != null)
                {
                    _user.RoleEnum = (UserRole)Enum.Parse(typeof(UserRole), selectedRoleItem.Tag.ToString());
                    _user.Role = selectedRoleItem.Content.ToString();
                }

                // جمع الصلاحيات
                _user.Permissions = new UserPermissions
                {
                    // صلاحيات المبيعات
                    CanManageSales = SalesPermissionCheckBox.IsChecked ?? false,
                    CanManageInvoices = InvoicesPermissionCheckBox.IsChecked ?? false,
                    CanReturnInvoices = ReturnInvoicesPermissionCheckBox.IsChecked ?? false,

                    // صلاحيات المخزون
                    CanManageProducts = ProductsPermissionCheckBox.IsChecked ?? false,
                    CanManageCategories = CategoriesPermissionCheckBox.IsChecked ?? false,
                    CanManageInventory = InventoryPermissionCheckBox.IsChecked ?? false,

                    // صلاحيات النظام
                    CanManageUsers = UsersPermissionCheckBox.IsChecked ?? false,
                    CanViewReports = ReportsPermissionCheckBox.IsChecked ?? false,
                    CanManageSettings = SettingsPermissionCheckBox.IsChecked ?? false
                };

                // حفظ كلمة المرور في حالة الإضافة
                if (!_isEditMode)
                {
                    _user.Password = PasswordBox.Password;
                }

                // حفظ البيانات (سيتم استبداله بالحفظ في قاعدة البيانات)
                if (_isEditMode)
                {
                    // تحديث المستخدم الموجود
                    // هنا يمكن إضافة كود لتحديث المستخدم في قاعدة البيانات

                    MessageBox.Show("تم تحديث المستخدم بنجاح", "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // إضافة مستخدم جديد
                    _user.Id = GenerateNewId(); // توليد معرف جديد (سيتم استبداله بمعرف من قاعدة البيانات)

                    // هنا يمكن إضافة كود لإضافة المستخدم إلى قاعدة البيانات

                    MessageBox.Show("تمت إضافة المستخدم بنجاح", "تمت الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private bool ValidateInput()
        {
            // التحقق من اسم المستخدم
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم المستخدم", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return false;
            }

            // التحقق من كلمة المرور في حالة الإضافة
            if (!_isEditMode)
            {
                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    MessageBox.Show("الرجاء إدخال كلمة المرور", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    PasswordBox.Focus();
                    return false;
                }

                if (PasswordBox.Password.Length < 6)
                {
                    MessageBox.Show("يجب أن تكون كلمة المرور 6 أحرف على الأقل", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    PasswordBox.Focus();
                    return false;
                }

                if (PasswordBox.Password != ConfirmPasswordBox.Password)
                {
                    MessageBox.Show("كلمة المرور وتأكيدها غير متطابقين", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    ConfirmPasswordBox.Focus();
                    return false;
                }
            }

            // التحقق من الاسم الكامل
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                MessageBox.Show("الرجاء إدخال الاسم الكامل", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                FullNameTextBox.Focus();
                return false;
            }

            // التحقق من البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                // التحقق من صحة تنسيق البريد الإلكتروني
                string emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
                if (!Regex.IsMatch(EmailTextBox.Text, emailPattern))
                {
                    MessageBox.Show("الرجاء إدخال بريد إلكتروني صحيح", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                    EmailTextBox.Focus();
                    return false;
                }
            }

            // التحقق من اختيار الدور
            if (RoleComboBox.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار دور المستخدم", "خطأ في الإدخال", MessageBoxButton.OK, MessageBoxImage.Warning);
                RoleComboBox.Focus();
                return false;
            }

            return true;
        }

        private int GenerateNewId()
        {
            // توليد معرف عشوائي (سيتم استبداله بمعرف من قاعدة البيانات)
            Random random = new Random();
            return random.Next(100, 1000);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class UserViewModel
    {
        public int Id { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; } // لا يتم تخزينه، يستخدم فقط للإضافة
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Role { get; set; } // النص العربي للدور
        public UserRole RoleEnum { get; set; } // قيمة التعداد للدور
        public bool IsActive { get; set; }
        public UserPermissions Permissions { get; set; }
        public DateTime? LastLogin { get; set; }
    }

    public class UserPermissions
    {
        // صلاحيات المبيعات
        public bool CanManageSales { get; set; }
        public bool CanManageInvoices { get; set; }
        public bool CanReturnInvoices { get; set; }

        // صلاحيات المخزون
        public bool CanManageProducts { get; set; }
        public bool CanManageCategories { get; set; }
        public bool CanManageInventory { get; set; }

        // صلاحيات النظام
        public bool CanManageUsers { get; set; }
        public bool CanViewReports { get; set; }
        public bool CanManageSettings { get; set; }
    }
}
