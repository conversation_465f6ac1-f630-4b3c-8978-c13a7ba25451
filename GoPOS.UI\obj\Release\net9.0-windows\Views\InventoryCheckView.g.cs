﻿#pragma checksum "..\..\..\..\Views\InventoryCheckView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DDE94F2B3D210A67182D600B91AEB991963BA6E4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GoPOS.UI.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GoPOS.UI.Views {
    
    
    /// <summary>
    /// InventoryCheckView
    /// </summary>
    public partial class InventoryCheckView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 27 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WarehouseComboBox;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewCheckButton;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditCheckButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteCheckButton;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintCheckButton;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportCheckButton;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InventoryChecksDataGrid;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ExpiryPeriodComboBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveExpiredButton;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\InventoryCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ExpiryProductsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GoPOS.UI;component/views/inventorycheckview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\InventoryCheckView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WarehouseComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 27 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.WarehouseComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WarehouseComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 33 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.NewCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.NewCheckButton.Click += new System.Windows.RoutedEventHandler(this.NewCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.EditCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.EditCheckButton.Click += new System.Windows.RoutedEventHandler(this.EditCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DeleteCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.DeleteCheckButton.Click += new System.Windows.RoutedEventHandler(this.DeleteCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.PrintCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.PrintCheckButton.Click += new System.Windows.RoutedEventHandler(this.PrintCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportCheckButton = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.ExportCheckButton.Click += new System.Windows.RoutedEventHandler(this.ExportCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.InventoryChecksDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 65 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.InventoryChecksDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InventoryChecksDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ExpiryPeriodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 118 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.ExpiryPeriodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ExpiryPeriodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.RemoveExpiredButton = ((System.Windows.Controls.Button)(target));
            
            #line 128 "..\..\..\..\Views\InventoryCheckView.xaml"
            this.RemoveExpiredButton.Click += new System.Windows.RoutedEventHandler(this.RemoveExpiredButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ExpiryProductsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.2.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 10:
            
            #line 87 "..\..\..\..\Views\InventoryCheckView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewCheckButton_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 88 "..\..\..\..\Views\InventoryCheckView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PerformCheckButton_Click);
            
            #line default
            #line hidden
            break;
            case 12:
            
            #line 89 "..\..\..\..\Views\InventoryCheckView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApproveCheckButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

