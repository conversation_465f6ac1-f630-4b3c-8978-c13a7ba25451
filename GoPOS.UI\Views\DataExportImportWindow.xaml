<Window x:Class="GoPOS.UI.Views.DataExportImportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GoPOS.UI.Views"
        mc:Ignorable="d"
        Title="تصدير واستيراد البيانات" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- رأس النافذة -->
        <Border Grid.Row="0" Background="#3F51B5" CornerRadius="8" Margin="0,0,0,20">
            <Grid Margin="15,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="تصدير واستيراد البيانات" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="تصدير واستيراد البيانات من وإلى ملفات Excel و CSV" FontSize="14" Foreground="White" Margin="0,5,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="RefreshButton" 
                            Width="100" 
                            Height="40" 
                            Click="RefreshButton_Click"
                            Background="White"
                            Foreground="#3F51B5"
                            FontWeight="SemiBold">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="4"/>
                            </Style>
                        </Button.Resources>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- محتوى النافذة -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- قائمة الجداول -->
            <Border Grid.Column="0" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="جداول قاعدة البيانات" FontSize="16" FontWeight="SemiBold" Margin="10"/>

                    <ListBox x:Name="TablesListBox" Grid.Row="1" BorderThickness="0" SelectionChanged="TablesListBox_SelectionChanged">
                        <ListBox.Resources>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Padding" Value="10"/>
                                <Setter Property="Margin" Value="0,2"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#E8E3F4"/>
                                        <Setter Property="BorderBrush" Value="#3F51B5"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </ListBox.Resources>
                    </ListBox>
                </Grid>
            </Border>

            <!-- تفاصيل الجدول -->
            <Border Grid.Column="1" Background="White" BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان الجدول -->
                    <TextBlock x:Name="TableNameTextBlock" Grid.Row="0" Text="اختر جدولاً من القائمة" FontSize="18" FontWeight="Bold" Margin="15,15,15,5"/>

                    <!-- أزرار التصدير والاستيراد -->
                    <Grid Grid.Row="1" Margin="15,5,15,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Button x:Name="ExportToExcelButton" 
                                Grid.Column="0" 
                                Content="تصدير إلى Excel" 
                                Width="120" 
                                Height="35" 
                                Margin="0,0,10,0" 
                                Click="ExportToExcelButton_Click"
                                Background="#4CAF50"
                                Foreground="White"
                                IsEnabled="False">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                        </Button>

                        <Button x:Name="ExportToCsvButton" 
                                Grid.Column="1" 
                                Content="تصدير إلى CSV" 
                                Width="120" 
                                Height="35" 
                                Margin="0,0,10,0" 
                                Click="ExportToCsvButton_Click"
                                Background="#2196F3"
                                Foreground="White"
                                IsEnabled="False">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                        </Button>

                        <Button x:Name="ImportButton" 
                                Grid.Column="3" 
                                Content="استيراد بيانات" 
                                Width="120" 
                                Height="35" 
                                Click="ImportButton_Click"
                                Background="#FF9800"
                                Foreground="White"
                                IsEnabled="False">
                            <Button.Resources>
                                <Style TargetType="Border">
                                    <Setter Property="CornerRadius" Value="4"/>
                                </Style>
                            </Button.Resources>
                        </Button>
                    </Grid>

                    <!-- معاينة البيانات -->
                    <DataGrid x:Name="DataPreviewGrid" 
                              Grid.Row="2" 
                              Margin="15,0,15,15" 
                              AutoGenerateColumns="True" 
                              IsReadOnly="True" 
                              AlternatingRowBackground="#F5F5F5" 
                              BorderBrush="#DDDDDD" 
                              BorderThickness="1" 
                              RowHeight="35" 
                              HeadersVisibility="Column" 
                              GridLinesVisibility="Horizontal" 
                              HorizontalGridLinesBrush="#EEEEEE">
                        <DataGrid.Resources>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="#3F51B5"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="Height" Value="40"/>
                                <Setter Property="Padding" Value="10,0"/>
                            </Style>
                        </DataGrid.Resources>
                    </DataGrid>
                </Grid>
            </Border>
        </Grid>

        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock x:Name="StatusTextBlock" Grid.Column="0" Text="" VerticalAlignment="Center"/>
            
            <Button x:Name="CloseButton" 
                    Grid.Column="1" 
                    Content="إغلاق" 
                    Width="100" 
                    Height="40" 
                    Click="CloseButton_Click"
                    Background="#607D8B"
                    Foreground="White">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </Grid>
    </Grid>
</Window>
