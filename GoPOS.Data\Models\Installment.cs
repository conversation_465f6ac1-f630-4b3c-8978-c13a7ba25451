using System;
using System.Collections.Generic;

namespace GoPOS.Data.Models
{
    /// <summary>
    /// حالة خطة الأقساط
    /// </summary>
    public enum InstallmentPlanStatus
    {
        /// <summary>
        /// نشطة
        /// </summary>
        Active = 0,
        
        /// <summary>
        /// مدفوعة جزئياً
        /// </summary>
        PartiallyPaid = 1,
        
        /// <summary>
        /// مكتملة
        /// </summary>
        Completed = 2,
        
        /// <summary>
        /// ملغاة
        /// </summary>
        Cancelled = 3,
        
        /// <summary>
        /// متأخرة
        /// </summary>
        Overdue = 4
    }

    /// <summary>
    /// حالة القسط
    /// </summary>
    public enum InstallmentStatus
    {
        /// <summary>
        /// معلق
        /// </summary>
        Pending = 0,
        
        /// <summary>
        /// مدفوع
        /// </summary>
        Paid = 1,
        
        /// <summary>
        /// متأخر
        /// </summary>
        Overdue = 2,
        
        /// <summary>
        /// ملغى
        /// </summary>
        Cancelled = 3
    }

    /// <summary>
    /// نموذج خطة الأقساط
    /// </summary>
    public class InstallmentPlan
    {
        /// <summary>
        /// معرف الخطة
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerId { get; set; }
        
        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName { get; set; }
        
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int? InvoiceId { get; set; }
        
        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        public string InvoiceNumber { get; set; }
        
        /// <summary>
        /// المبلغ الإجمالي
        /// </summary>
        public decimal TotalAmount { get; set; }
        
        /// <summary>
        /// الدفعة المقدمة
        /// </summary>
        public decimal DownPayment { get; set; }
        
        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; }
        
        /// <summary>
        /// عدد الأقساط
        /// </summary>
        public int NumberOfInstallments { get; set; }
        
        /// <summary>
        /// مبلغ القسط
        /// </summary>
        public decimal InstallmentAmount { get; set; }
        
        /// <summary>
        /// تاريخ البدء
        /// </summary>
        public DateTime StartDate { get; set; }
        
        /// <summary>
        /// تاريخ الانتهاء
        /// </summary>
        public DateTime EndDate { get; set; }
        
        /// <summary>
        /// حالة الخطة
        /// </summary>
        public InstallmentPlanStatus Status { get; set; }
        
        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// الأقساط
        /// </summary>
        public List<Installment> Installments { get; set; }
    }

    /// <summary>
    /// نموذج القسط
    /// </summary>
    public class Installment
    {
        /// <summary>
        /// معرف القسط
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// معرف الخطة
        /// </summary>
        public int PlanId { get; set; }
        
        /// <summary>
        /// تاريخ الاستحقاق
        /// </summary>
        public DateTime DueDate { get; set; }
        
        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// حالة القسط
        /// </summary>
        public InstallmentStatus Status { get; set; }
        
        /// <summary>
        /// تاريخ الدفع
        /// </summary>
        public DateTime? PaymentDate { get; set; }
        
        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// معرف العميل (للأقساط المستحقة)
        /// </summary>
        public int? CustomerId { get; set; }
        
        /// <summary>
        /// اسم العميل (للأقساط المستحقة)
        /// </summary>
        public string CustomerName { get; set; }
    }
}
